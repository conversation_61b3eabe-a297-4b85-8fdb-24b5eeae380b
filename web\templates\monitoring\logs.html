{% extends "base.html" %}

{% block title %}Log Management - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-file-alt me-2"></i>
                    Log Management
                </h2>
                <div>
                    <button onclick="location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh
                    </button>
                    <button onclick="cleanupLogs()" class="btn btn-outline-warning">
                        <i class="fas fa-broom me-2"></i>
                        Cleanup Old Logs
                    </button>
                    <a href="{{ url_for('monitoring.system_status') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-heartbeat me-2"></i>
                        System Status
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Statistics Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ log_stats.total_files }}</h3>
                    <p class="card-text">Total Log Files</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">{{ log_stats.total_size_mb }}</h3>
                    <p class="card-text">Total Size (MB)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ log_stats.compressed_files }}</h3>
                    <p class="card-text">Compressed Files</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">{{ log_stats.uncompressed_files }}</h3>
                    <p class="card-text">Uncompressed Files</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Files by Type -->
    {% if log_stats.by_type %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Log Files by Type
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>File Type</th>
                                    <th>Count</th>
                                    <th>Total Size (MB)</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for file_type, stats in log_stats.by_type.items() %}
                                <tr>
                                    <td>
                                        <code>{{ file_type if file_type else 'No Extension' }}</code>
                                    </td>
                                    <td>{{ stats.count }}</td>
                                    <td>{{ stats.size_mb }}</td>
                                    <td>
                                        {% set percentage = (stats.size_mb / log_stats.total_size_mb * 100) if log_stats.total_size_mb > 0 else 0 %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" 
                                                 role="progressbar" 
                                                 style="width: {{ percentage }}%"
                                                 aria-valuenow="{{ percentage }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                {{ "%.1f"|format(percentage) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- File Age Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Oldest Log File
                    </h6>
                </div>
                <div class="card-body">
                    {% if log_stats.oldest_file %}
                    <p class="mb-1"><strong>{{ log_stats.oldest_file.name }}</strong></p>
                    <small class="text-muted">{{ log_stats.oldest_file.date }}</small>
                    {% else %}
                    <p class="text-muted">No log files found</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Newest Log File
                    </h6>
                </div>
                <div class="card-body">
                    {% if log_stats.newest_file %}
                    <p class="mb-1"><strong>{{ log_stats.newest_file.name }}</strong></p>
                    <small class="text-muted">{{ log_stats.newest_file.date }}</small>
                    {% else %}
                    <p class="text-muted">No log files found</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Log Export -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-download me-2"></i>
                        Export Logs
                    </h5>
                </div>
                <div class="card-body">
                    <form id="exportForm" onsubmit="exportLogs(event)">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="startDate" class="form-label">Start Date</label>
                                    <input type="datetime-local" 
                                           class="form-control" 
                                           id="startDate" 
                                           name="start_date"
                                           value="{{ (moment().subtract(1, 'days').format('YYYY-MM-DDTHH:mm')) if moment else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="endDate" class="form-label">End Date</label>
                                    <input type="datetime-local" 
                                           class="form-control" 
                                           id="endDate" 
                                           name="end_date"
                                           value="{{ moment().format('YYYY-MM-DDTHH:mm') if moment else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="components" class="form-label">Components (Optional)</label>
                                    <select class="form-select" id="components" name="components" multiple>
                                        <option value="web">Web Application</option>
                                        <option value="instagram_client">Instagram Client</option>
                                        <option value="monitoring">Monitoring Service</option>
                                        <option value="database">Database</option>
                                        <option value="scheduler">Scheduler</option>
                                        <option value="auth">Authentication</option>
                                    </select>
                                    <div class="form-text">Hold Ctrl/Cmd to select multiple</div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download me-2"></i>
                            Export Logs
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Management Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Management Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-block">
                        <button onclick="cleanupLogs()" class="btn btn-warning">
                            <i class="fas fa-broom me-2"></i>
                            Cleanup Old Logs
                        </button>
                        <button onclick="compressLogs()" class="btn btn-info">
                            <i class="fas fa-compress-alt me-2"></i>
                            Compress Logs
                        </button>
                        <button onclick="rotateLogs()" class="btn btn-secondary">
                            <i class="fas fa-sync-alt me-2"></i>
                            Force Log Rotation
                        </button>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Automatic Management</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Log rotation when files exceed 10MB</li>
                            <li><i class="fas fa-check text-success me-2"></i>Automatic compression of old logs</li>
                            <li><i class="fas fa-check text-success me-2"></i>Cleanup of logs older than 30 days</li>
                            <li><i class="fas fa-check text-success me-2"></i>Daily maintenance at 2:00 AM</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Operation Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="statusMessage"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function showStatus(message, isError = false) {
    const statusMessage = document.getElementById('statusMessage');
    statusMessage.innerHTML = `
        <div class="alert alert-${isError ? 'danger' : 'success'}" role="alert">
            <i class="fas fa-${isError ? 'exclamation-circle' : 'check-circle'} me-2"></i>
            ${message}
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function cleanupLogs() {
    fetch('/monitoring/api/logs/cleanup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus(data.message);
            setTimeout(() => location.reload(), 2000);
        } else {
            showStatus(data.error || 'Cleanup failed', true);
        }
    })
    .catch(error => {
        showStatus('Network error: ' + error.message, true);
    });
}

function exportLogs(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        start_date: formData.get('start_date'),
        end_date: formData.get('end_date'),
        components: formData.getAll('components')
    };
    
    fetch('/monitoring/api/logs/export', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus(`Export completed: ${data.export_file}`);
        } else {
            showStatus(data.error || 'Export failed', true);
        }
    })
    .catch(error => {
        showStatus('Network error: ' + error.message, true);
    });
}

// Set default dates
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    document.getElementById('startDate').value = yesterday.toISOString().slice(0, 16);
    document.getElementById('endDate').value = now.toISOString().slice(0, 16);
});
</script>
{% endblock %}