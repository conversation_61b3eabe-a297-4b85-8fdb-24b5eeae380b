#!/bin/bash
set -e

# Initialize database if it doesn't exist
if [ ! -f "/app/data/instagram_monitor.db" ]; then
    echo "Initializing database..."
    python init_database.py
fi

# Run database migrations if needed
echo "Running database migrations..."
python -c "
from database.connection import DatabaseManager
db = DatabaseManager()
db.initialize_database()
print('Database initialization complete')
"

# Create log directories
mkdir -p /app/logs

# Start the application
echo "Starting Instagram Follower Monitor..."
exec "$@"