"""Core data models for Instagram follower monitoring."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, List
from enum import Enum


class ChangeType(Enum):
    """Enumeration for different types of follower/following changes."""
    GAINED = "gained"
    LOST = "lost"
    STARTED_FOLLOWING = "started_following"
    STOPPED_FOLLOWING = "stopped_following"


@dataclass
class ProfileInfo:
    """Data model for Instagram profile information."""
    username: str
    display_name: Optional[str] = None
    follower_count: int = 0
    following_count: int = 0
    is_private: bool = False
    last_updated: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate profile data after initialization."""
        if not self.username:
            raise ValueError("Username cannot be empty")
        
        if self.follower_count < 0:
            raise ValueError("Follower count cannot be negative")
        
        if self.following_count < 0:
            raise ValueError("Following count cannot be negative")
        
        # Clean username (remove @ if present)
        self.username = self.username.lstrip('@').lower()


@dataclass
class FollowerChange:
    """Data model for tracking follower/following changes."""
    profile_username: str
    affected_username: str
    change_type: ChangeType
    timestamp: datetime = field(default_factory=datetime.now)
    profile_id: Optional[int] = None
    
    def __post_init__(self):
        """Validate change data after initialization."""
        if not self.profile_username:
            raise ValueError("Profile username cannot be empty")
        
        if not self.affected_username:
            raise ValueError("Affected username cannot be empty")
        
        if not isinstance(self.change_type, ChangeType):
            raise ValueError("Change type must be a ChangeType enum value")
        
        # Clean usernames
        self.profile_username = self.profile_username.lstrip('@').lower()
        self.affected_username = self.affected_username.lstrip('@').lower()
    
    @property
    def is_follower_change(self) -> bool:
        """Check if this is a follower change (gained/lost)."""
        return self.change_type in [ChangeType.GAINED, ChangeType.LOST]
    
    @property
    def is_following_change(self) -> bool:
        """Check if this is a following change (started/stopped following)."""
        return self.change_type in [ChangeType.STARTED_FOLLOWING, ChangeType.STOPPED_FOLLOWING]


@dataclass
class MonitoringConfig:
    """Data model for profile monitoring configuration."""
    profile_username: str
    enabled: bool = True
    interval_hours: int = 2
    last_scan: Optional[datetime] = None
    profile_id: Optional[int] = None
    display_name: Optional[str] = None
    is_private: bool = False
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate monitoring configuration after initialization."""
        if not self.profile_username:
            raise ValueError("Profile username cannot be empty")
        
        if self.interval_hours <= 0:
            raise ValueError("Interval hours must be positive")
        
        if self.interval_hours > 168:  # 1 week
            raise ValueError("Interval hours cannot exceed 168 (1 week)")
        
        # Clean username
        self.profile_username = self.profile_username.lstrip('@').lower()
    
    @property
    def is_due_for_scan(self) -> bool:
        """Check if profile is due for scanning based on interval."""
        if not self.enabled:
            return False
        
        if self.last_scan is None:
            return True
        
        time_since_scan = datetime.now() - self.last_scan
        return time_since_scan.total_seconds() >= (self.interval_hours * 3600)


@dataclass
class UserList:
    """Data model for follower/following lists."""
    profile_username: str
    usernames: List[str] = field(default_factory=list)
    list_type: str = "followers"  # "followers" or "following"
    retrieved_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """Validate user list data after initialization."""
        if not self.profile_username:
            raise ValueError("Profile username cannot be empty")
        
        if self.list_type not in ["followers", "following"]:
            raise ValueError("List type must be 'followers' or 'following'")
        
        # Clean usernames
        self.profile_username = self.profile_username.lstrip('@').lower()
        self.usernames = [username.lstrip('@').lower() for username in self.usernames]
        
        # Remove duplicates while preserving order
        seen = set()
        unique_usernames = []
        for username in self.usernames:
            if username not in seen:
                seen.add(username)
                unique_usernames.append(username)
        self.usernames = unique_usernames
    
    @property
    def count(self) -> int:
        """Get count of users in the list."""
        return len(self.usernames)
    
    def to_set(self) -> set:
        """Convert usernames list to set for efficient operations."""
        return set(self.usernames)


@dataclass
class SystemSettings:
    """Data model for system configuration settings."""
    monitoring_interval_hours: int = 2
    max_profiles: int = 10
    data_retention_days: int = 365
    rate_limit_delay_seconds: int = 2
    max_retry_attempts: int = 3
    
    def __post_init__(self):
        """Validate system settings after initialization."""
        if self.monitoring_interval_hours <= 0:
            raise ValueError("Monitoring interval must be positive")
        
        if self.max_profiles <= 0:
            raise ValueError("Max profiles must be positive")
        
        if self.data_retention_days <= 0:
            raise ValueError("Data retention days must be positive")
        
        if self.rate_limit_delay_seconds < 0:
            raise ValueError("Rate limit delay cannot be negative")
        
        if self.max_retry_attempts < 0:
            raise ValueError("Max retry attempts cannot be negative")


def validate_username(username: str) -> str:
    """Validate and clean Instagram username."""
    if not username:
        raise ValueError("Username cannot be empty")
    
    # Remove @ symbol and convert to lowercase
    clean_username = username.lstrip('@').lower()
    
    # Basic validation - Instagram usernames can contain letters, numbers, periods, and underscores
    import re
    if not re.match(r'^[a-zA-Z0-9._]+$', clean_username):
        raise ValueError("Username contains invalid characters")
    
    if len(clean_username) > 30:
        raise ValueError("Username cannot exceed 30 characters")
    
    return clean_username