{% extends "base.html" %}

{% block title %}System Status - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-heartbeat me-2"></i>
                    System Status
                </h2>
                <button onclick="location.reload()" class="btn btn-outline-primary">
                    <i class="fas fa-sync-alt me-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Overall Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Overall System Health
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h3>
                                Status: 
                                {% if health_data.status == 'healthy' %}
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Healthy
                                    </span>
                                {% elif health_data.status == 'warning' %}
                                    <span class="badge bg-warning fs-6">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Warning
                                    </span>
                                {% elif health_data.status == 'degraded' %}
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        Degraded
                                    </span>
                                {% else %}
                                    <span class="badge bg-dark fs-6">
                                        <i class="fas fa-times-circle me-1"></i>
                                        Critical
                                    </span>
                                {% endif %}
                            </h3>
                            <p class="text-muted mb-0">
                                Last updated: {{ health_data.timestamp }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h4 mb-1 text-danger">{{ health_data.alerts.critical }}</div>
                                        <small class="text-muted">Critical</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h4 mb-1 text-warning">{{ health_data.alerts.error }}</div>
                                        <small class="text-muted">Errors</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h4 mb-1 text-info">{{ health_data.alerts.warning }}</div>
                                        <small class="text-muted">Warnings</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Component Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cubes me-2"></i>
                        Component Status
                    </h5>
                </div>
                <div class="card-body">
                    {% if health_data.components %}
                        <div class="row">
                            {% for component, status in health_data.components.items() %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            {{ component.replace('_', ' ').title() }}
                                        </h6>
                                        <p class="card-text">
                                            Status: 
                                            {% if status.status == 'healthy' or status.status == 'ok' %}
                                                <span class="badge bg-success">Healthy</span>
                                            {% elif status.status == 'warning' %}
                                                <span class="badge bg-warning">Warning</span>
                                            {% elif status.status == 'error' %}
                                                <span class="badge bg-danger">Error</span>
                                            {% elif status.status == 'stale' %}
                                                <span class="badge bg-secondary">Stale</span>
                                            {% else %}
                                                <span class="badge bg-info">{{ status.status.title() }}</span>
                                            {% endif %}
                                        </p>
                                        <small class="text-muted">
                                            Last updated: {{ status.last_updated }}
                                        </small>
                                        {% if status.error_count %}
                                        <div class="mt-2">
                                            <small class="text-danger">
                                                Errors: {{ status.error_count }}
                                            </small>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No component status information available.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Error Rates -->
    {% if health_data.error_rates %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Error Rates (Last Hour)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Component</th>
                                    <th>Error Rate</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for component, rate in health_data.error_rates.items() %}
                                <tr>
                                    <td>{{ component.replace('_', ' ').title() }}</td>
                                    <td>{{ "%.2f"|format(rate * 100) }}%</td>
                                    <td>
                                        {% if rate < 0.05 %}
                                            <span class="badge bg-success">Good</span>
                                        {% elif rate < 0.1 %}
                                            <span class="badge bg-warning">Elevated</span>
                                        {% else %}
                                            <span class="badge bg-danger">High</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-block">
                        <a href="{{ url_for('monitoring.alerts_dashboard') }}" class="btn btn-outline-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            View Alerts
                        </a>
                        <a href="{{ url_for('monitoring.errors_dashboard') }}" class="btn btn-outline-danger">
                            <i class="fas fa-bug me-2"></i>
                            View Errors
                        </a>
                        <a href="{{ url_for('monitoring.performance_dashboard') }}" class="btn btn-outline-info">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Performance
                        </a>
                        <a href="{{ url_for('monitoring.logs_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-file-alt me-2"></i>
                            Log Management
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}