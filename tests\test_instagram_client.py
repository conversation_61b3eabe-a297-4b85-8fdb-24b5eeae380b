"""
Unit tests for Instagram client functionality.
"""

import time
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from config import Config
from services.instagram_client import InstagramClient, RateLimiter
from services.authentication import AuthenticationManager
from models.data_models import ProfileInfo


class TestRateLimiter:
    """Test cases for RateLimiter class."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        config.MIN_REQUEST_DELAY = 1.0
        config.MAX_REQUEST_DELAY = 3.0
        config.MAX_RETRIES = 3
        return config
    
    @pytest.fixture
    def rate_limiter(self, mock_config):
        """Create a RateLimiter instance for testing."""
        return RateLimiter(mock_config)
    
    def test_initial_state(self, rate_limiter):
        """Test initial state of rate limiter."""
        assert rate_limiter.last_request_time == 0.0
        assert rate_limiter.request_count == 0
        assert rate_limiter.backoff_until == 0.0
        assert rate_limiter.consecutive_errors == 0
    
    @patch('services.instagram_client.time.sleep')
    @patch('services.instagram_client.time.time')
    def test_wait_if_needed_first_request(self, mock_time, mock_sleep, rate_limiter):
        """Test waiting behavior for first request."""
        mock_time.return_value = 100.0
        
        rate_limiter.wait_if_needed()
        
        # For first request, no sleep should occur since last_request_time is 0
        # and time_since_last (100.0) is much larger than required_delay (1-3)
        mock_sleep.assert_not_called()
        
        assert rate_limiter.request_count == 1
        assert rate_limiter.last_request_time == 100.0
    
    @patch('services.instagram_client.time.sleep')
    @patch('services.instagram_client.time.time')
    @patch('services.instagram_client.random.uniform')
    def test_wait_if_needed_subsequent_request(self, mock_random, mock_time, mock_sleep, rate_limiter):
        """Test waiting behavior for subsequent requests."""
        # Set up previous request time
        rate_limiter.last_request_time = 100.0
        mock_time.return_value = 101.0  # 1 second later
        mock_random.return_value = 2.5  # Required delay of 2.5 seconds
        
        rate_limiter.wait_if_needed()
        
        # Should sleep for additional time to meet minimum delay
        # time_since_last = 1.0, required_delay = 2.5, so wait_time = 1.5
        mock_sleep.assert_called_once()
        sleep_time = mock_sleep.call_args[0][0]
        assert sleep_time == 1.5
    
    @patch('services.instagram_client.time.sleep')
    @patch('services.instagram_client.time.time')
    def test_wait_if_needed_backoff_period(self, mock_time, mock_sleep, rate_limiter):
        """Test waiting behavior during backoff period."""
        current_time = 100.0
        rate_limiter.backoff_until = 150.0  # Backoff for 50 seconds
        mock_time.return_value = current_time
        
        rate_limiter.wait_if_needed()
        
        # Should sleep for backoff time plus regular delay
        assert mock_sleep.call_count >= 1
        # First call should be for backoff period
        first_sleep = mock_sleep.call_args_list[0][0][0]
        assert first_sleep == 50.0
    
    def test_handle_rate_limit_error(self, rate_limiter):
        """Test rate limit error handling with exponential backoff."""
        initial_time = time.time()
        
        # First error
        rate_limiter.handle_rate_limit_error()
        assert rate_limiter.consecutive_errors == 1
        assert rate_limiter.backoff_until > initial_time
        first_backoff = rate_limiter.backoff_until - initial_time
        
        # Second error
        rate_limiter.handle_rate_limit_error()
        assert rate_limiter.consecutive_errors == 2
        second_backoff = rate_limiter.backoff_until - initial_time
        
        # Second backoff should be longer than first
        assert second_backoff > first_backoff
    
    def test_reset_error_count(self, rate_limiter):
        """Test resetting error count after successful request."""
        rate_limiter.consecutive_errors = 5
        rate_limiter.reset_error_count()
        assert rate_limiter.consecutive_errors == 0
    
    @patch('services.instagram_client.time.time')
    def test_update_request_patterns(self, mock_time, rate_limiter):
        """Test request pattern monitoring."""
        mock_time.return_value = 1000.0
        
        # Test initial state
        assert len(rate_limiter.request_history) == 0
        assert len(rate_limiter.request_types) == 0
        
        # Update patterns
        rate_limiter._update_request_patterns('profile_info', 1000.0)
        
        assert len(rate_limiter.request_history) == 1
        assert 'profile_info' in rate_limiter.request_types
        assert len(rate_limiter.request_types['profile_info']) == 1
    
    @patch('services.instagram_client.time.time')
    def test_update_request_patterns_cleanup(self, mock_time, rate_limiter):
        """Test request pattern cleanup of old entries."""
        # Add old requests
        old_time = 1000.0
        current_time = old_time + 3700  # More than 1 hour later
        
        rate_limiter.request_history = [old_time, old_time + 100]
        rate_limiter.request_types['test'] = [old_time, old_time + 100]
        
        mock_time.return_value = current_time
        
        # Update patterns - should clean old entries
        rate_limiter._update_request_patterns('test', current_time)
        
        # Old entries should be removed, new one added
        assert len(rate_limiter.request_history) == 1
        assert rate_limiter.request_history[0] == current_time
        assert len(rate_limiter.request_types['test']) == 1
        assert rate_limiter.request_types['test'][0] == current_time
    
    @patch('services.instagram_client.time.time')
    @patch('services.instagram_client.time.localtime')
    def test_calculate_adaptive_delay(self, mock_localtime, mock_time, rate_limiter):
        """Test adaptive delay calculation."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Mock time structure for business hours
        mock_time_struct = Mock()
        mock_time_struct.tm_hour = 10  # Business hours
        mock_localtime.return_value = mock_time_struct
        
        # Test with no recent requests
        delay = rate_limiter._calculate_adaptive_delay('test')
        assert 0.5 <= delay <= 1.0  # Should only have business hours delay
        
        # Test with many recent requests
        recent_time = current_time - 300  # 5 minutes ago
        rate_limiter.request_history = [recent_time + i for i in range(25)]  # 25 requests
        
        delay = rate_limiter._calculate_adaptive_delay('test')
        assert delay >= 2.5  # Should have high frequency penalty + business hours
    
    @patch('services.instagram_client.time.time')
    def test_calculate_adaptive_delay_repetitive_requests(self, mock_time, rate_limiter):
        """Test adaptive delay for repetitive request types."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Add many requests of same type
        recent_time = current_time - 300  # 5 minutes ago
        rate_limiter.request_types['profile_info'] = [recent_time + i for i in range(15)]
        
        delay = rate_limiter._calculate_adaptive_delay('profile_info')
        assert delay >= 1.5  # Should have repetitive request penalty
    
    def test_get_next_user_agent(self, rate_limiter):
        """Test user agent rotation."""
        # Test initial user agent
        first_agent = rate_limiter.get_next_user_agent()
        assert first_agent in rate_limiter.user_agents
        
        # Test rotation
        second_agent = rate_limiter.get_next_user_agent()
        assert second_agent in rate_limiter.user_agents
        
        # Should eventually cycle back
        agents = []
        for _ in range(len(rate_limiter.user_agents) + 1):
            agents.append(rate_limiter.get_next_user_agent())
        
        # First and last should be the same (cycled back)
        assert agents[0] == agents[-1]
    
    @patch('services.instagram_client.time.time')
    def test_should_rotate_session_time_based(self, mock_time, rate_limiter):
        """Test session rotation based on time."""
        current_time = 1000.0
        rate_limiter.session_start_time = current_time - 7300  # More than 2 hours
        mock_time.return_value = current_time
        
        assert rate_limiter.should_rotate_session() is True
    
    def test_should_rotate_session_request_based(self, rate_limiter):
        """Test session rotation based on request count."""
        rate_limiter.request_count = 600  # More than 500
        
        assert rate_limiter.should_rotate_session() is True
    
    def test_should_rotate_session_error_based(self, rate_limiter):
        """Test session rotation based on consecutive errors."""
        rate_limiter.consecutive_errors = 5  # More than 3
        
        assert rate_limiter.should_rotate_session() is True
    
    @patch('services.instagram_client.time.time')
    def test_should_rotate_session_no_rotation_needed(self, mock_time, rate_limiter):
        """Test session rotation when no rotation is needed."""
        current_time = 1000.0
        rate_limiter.session_start_time = current_time - 1000  # Less than 2 hours
        rate_limiter.request_count = 100  # Less than 500
        rate_limiter.consecutive_errors = 1  # Less than 3
        mock_time.return_value = current_time
        
        assert rate_limiter.should_rotate_session() is False
    
    @patch('services.instagram_client.time.time')
    def test_reset_session(self, mock_time, rate_limiter):
        """Test session reset functionality."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Set up some state
        rate_limiter.request_count = 100
        rate_limiter.consecutive_errors = 3
        rate_limiter.request_history = [900.0, 950.0]
        rate_limiter.request_types = {'test': [900.0]}
        
        rate_limiter.reset_session()
        
        assert rate_limiter.session_start_time == current_time
        assert rate_limiter.request_count == 0
        assert rate_limiter.consecutive_errors == 0
        assert len(rate_limiter.request_history) == 0
        assert len(rate_limiter.request_types) == 0
    
    @patch('services.instagram_client.time.time')
    def test_get_request_stats(self, mock_time, rate_limiter):
        """Test getting request statistics."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Set up some state
        rate_limiter.request_count = 50
        rate_limiter.consecutive_errors = 2
        rate_limiter.session_start_time = current_time - 1800  # 30 minutes ago
        rate_limiter.backoff_until = current_time + 300  # 5 minutes from now
        
        # Add some recent requests
        recent_time = current_time - 300  # 5 minutes ago
        rate_limiter.request_history = [recent_time, recent_time + 100, current_time - 100]
        rate_limiter.request_types = {
            'profile_info': [recent_time, current_time - 100],
            'followers': [recent_time + 100]
        }
        
        stats = rate_limiter.get_request_stats()
        
        assert stats['total_requests'] == 50
        assert stats['recent_requests_10min'] == 3
        assert stats['consecutive_errors'] == 2
        assert stats['session_duration'] == 1800
        assert stats['backoff_until'] == current_time + 300
        assert stats['request_types']['profile_info'] == 2
        assert stats['request_types']['followers'] == 1


class TestInstagramClient:
    """Test cases for InstagramClient class."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        config.MIN_REQUEST_DELAY = 1.0
        config.MAX_REQUEST_DELAY = 3.0
        config.MAX_RETRIES = 3
        config.ENCRYPTION_KEY = "test_key"
        return config
    
    @pytest.fixture
    def mock_auth_manager(self):
        """Create a mock authentication manager."""
        auth_manager = Mock(spec=AuthenticationManager)
        auth_manager.validate_credentials.return_value = True
        auth_manager.retrieve_credentials.return_value = {
            'username': 'testuser',
            'password': 'testpass'
        }
        return auth_manager
    
    @pytest.fixture
    def instagram_client(self, mock_config):
        """Create an InstagramClient instance for testing."""
        with patch('services.instagram_client.AuthenticationManager'):
            return InstagramClient(mock_config)
    
    def test_initial_state(self, instagram_client):
        """Test initial state of Instagram client."""
        assert instagram_client._authenticated is False
        assert instagram_client._current_username is None
        assert instagram_client.loader is not None
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_authenticate_with_credentials_success(self, mock_instaloader_class, instagram_client):
        """Test successful authentication with provided credentials."""
        # Mock the loader instance
        mock_loader = Mock()
        mock_instaloader_class.return_value = mock_loader
        instagram_client.loader = mock_loader
        
        # Mock successful login
        mock_loader.login.return_value = None
        
        # Mock auth manager
        instagram_client.auth_manager.validate_credentials.return_value = True
        instagram_client.auth_manager.store_credentials.return_value = True
        
        result = instagram_client.authenticate("testuser", "testpass")
        
        assert result is True
        assert instagram_client._authenticated is True
        assert instagram_client._current_username == "testuser"
        mock_loader.login.assert_called_once_with("testuser", "testpass")
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_authenticate_with_stored_credentials(self, mock_instaloader_class, instagram_client):
        """Test authentication using stored credentials."""
        # Mock the loader instance
        mock_loader = Mock()
        mock_instaloader_class.return_value = mock_loader
        instagram_client.loader = mock_loader
        
        # Mock successful login
        mock_loader.login.return_value = None
        
        # Mock auth manager with stored credentials
        instagram_client.auth_manager.retrieve_credentials.return_value = {
            'username': 'stored_user',
            'password': 'stored_pass'
        }
        
        result = instagram_client.authenticate()
        
        assert result is True
        assert instagram_client._current_username == "stored_user"
        mock_loader.login.assert_called_once_with("stored_user", "stored_pass")
    
    def test_authenticate_no_credentials(self, instagram_client):
        """Test authentication failure when no credentials are available."""
        instagram_client.auth_manager.retrieve_credentials.return_value = None
        
        result = instagram_client.authenticate()
        
        assert result is False
        assert instagram_client._authenticated is False
    
    def test_authenticate_invalid_credentials_format(self, instagram_client):
        """Test authentication failure with invalid credential format."""
        instagram_client.auth_manager.validate_credentials.return_value = False
        
        result = instagram_client.authenticate("", "")
        
        assert result is False
        assert instagram_client._authenticated is False
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_authenticate_bad_credentials_exception(self, mock_instaloader_class, instagram_client):
        """Test authentication failure with bad credentials."""
        from instaloader.exceptions import BadCredentialsException
        
        # Mock the loader instance
        mock_loader = Mock()
        mock_instaloader_class.return_value = mock_loader
        instagram_client.loader = mock_loader
        
        # Mock login to raise BadCredentialsException
        mock_loader.login.side_effect = BadCredentialsException("Invalid credentials")
        
        # Mock auth manager
        instagram_client.auth_manager.validate_credentials.return_value = True
        
        result = instagram_client.authenticate("testuser", "wrongpass")
        
        assert result is False
        assert instagram_client._authenticated is False
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_authenticate_two_factor_required(self, mock_instaloader_class, instagram_client):
        """Test authentication with two-factor authentication required."""
        from instaloader.exceptions import TwoFactorAuthRequiredException
        
        # Mock the loader instance
        mock_loader = Mock()
        mock_instaloader_class.return_value = mock_loader
        instagram_client.loader = mock_loader
        
        # Mock login to raise TwoFactorAuthRequiredException
        mock_loader.login.side_effect = TwoFactorAuthRequiredException("2FA required")
        
        # Mock auth manager
        instagram_client.auth_manager.validate_credentials.return_value = True
        
        # Test non-interactive mode (should fail)
        result = instagram_client.authenticate("testuser", "testpass", interactive=False)
        assert result is False
        
        # Test interactive mode (should also fail for now as 2FA is not implemented)
        result = instagram_client.authenticate("testuser", "testpass", interactive=True)
        assert result is False
    
    def test_is_authenticated(self, instagram_client):
        """Test authentication status checking."""
        # Initially not authenticated
        assert instagram_client.is_authenticated() is False
        
        # Mock authenticated state
        instagram_client._authenticated = True
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        assert instagram_client.is_authenticated() is True
    
    def test_get_current_username(self, instagram_client):
        """Test getting current username."""
        # Initially no username
        assert instagram_client.get_current_username() is None
        
        # Set authenticated state
        instagram_client._authenticated = True
        instagram_client._current_username = "testuser"
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        assert instagram_client.get_current_username() == "testuser"
    
    @patch('services.instagram_client.instaloader.Profile')
    def test_get_profile_info_success(self, mock_profile_class, instagram_client):
        """Test successful profile information retrieval."""
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock profile data
        mock_profile = Mock()
        mock_profile.username = "testuser"
        mock_profile.full_name = "Test User"
        mock_profile.followers = 1000
        mock_profile.followees = 500
        mock_profile.is_private = False
        
        mock_profile_class.from_username.return_value = mock_profile
        
        result = instagram_client.get_profile_info("testuser")
        
        assert result is not None
        assert isinstance(result, ProfileInfo)
        assert result.username == "testuser"
        assert result.display_name == "Test User"
        assert result.follower_count == 1000
        assert result.following_count == 500
        assert result.is_private is False
    
    def test_get_profile_info_not_authenticated(self, instagram_client):
        """Test profile info retrieval when not authenticated."""
        result = instagram_client.get_profile_info("testuser")
        assert result is None
    
    @patch('services.instagram_client.instaloader.Profile')
    def test_get_profile_info_rate_limited(self, mock_profile_class, instagram_client):
        """Test profile info retrieval with rate limiting."""
        from instaloader.exceptions import QueryReturnedBadRequestException
        
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock rate limit error
        mock_profile_class.from_username.side_effect = QueryReturnedBadRequestException(
            "rate limited"
        )
        
        result = instagram_client.get_profile_info("testuser")
        
        assert result is None
        # Should have called handle_rate_limit_error
        assert instagram_client.rate_limiter.consecutive_errors > 0
    
    @patch('services.instagram_client.instaloader.Profile')
    def test_get_followers_success(self, mock_profile_class, instagram_client):
        """Test successful followers retrieval."""
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock profile and followers
        mock_profile = Mock()
        mock_profile.is_private = False
        mock_profile.followed_by_viewer = False
        
        # Mock followers
        mock_follower1 = Mock()
        mock_follower1.username = "follower1"
        mock_follower2 = Mock()
        mock_follower2.username = "follower2"
        
        mock_profile.get_followers.return_value = [mock_follower1, mock_follower2]
        mock_profile_class.from_username.return_value = mock_profile
        
        result = instagram_client.get_followers("testuser")
        
        assert result is not None
        assert isinstance(result, set)
        assert "follower1" in result
        assert "follower2" in result
        assert len(result) == 2
    
    def test_get_followers_not_authenticated(self, instagram_client):
        """Test followers retrieval when not authenticated."""
        result = instagram_client.get_followers("testuser")
        assert result is None
    
    @patch('services.instagram_client.instaloader.Profile')
    def test_get_followers_private_profile(self, mock_profile_class, instagram_client):
        """Test followers retrieval for private profile not followed."""
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock private profile not followed
        mock_profile = Mock()
        mock_profile.is_private = True
        mock_profile.followed_by_viewer = False
        
        mock_profile_class.from_username.return_value = mock_profile
        
        result = instagram_client.get_followers("testuser")
        
        assert result is None
    
    @patch('services.instagram_client.instaloader.Profile')
    def test_get_following_success(self, mock_profile_class, instagram_client):
        """Test successful following retrieval."""
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock profile and following
        mock_profile = Mock()
        mock_profile.is_private = False
        mock_profile.followed_by_viewer = False
        
        # Mock following
        mock_following1 = Mock()
        mock_following1.username = "following1"
        mock_following2 = Mock()
        mock_following2.username = "following2"
        
        mock_profile.get_followees.return_value = [mock_following1, mock_following2]
        mock_profile_class.from_username.return_value = mock_profile
        
        result = instagram_client.get_following("testuser")
        
        assert result is not None
        assert isinstance(result, set)
        assert "following1" in result
        assert "following2" in result
        assert len(result) == 2
    
    def test_get_following_not_authenticated(self, instagram_client):
        """Test following retrieval when not authenticated."""
        result = instagram_client.get_following("testuser")
        assert result is None
    
    def test_logout(self, instagram_client):
        """Test logout functionality."""
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client._current_username = "testuser"
        
        instagram_client.logout()
        
        assert instagram_client._authenticated is False
        assert instagram_client._current_username is None
    
    def test_get_session_info(self, instagram_client):
        """Test getting session information."""
        # Set up some state
        instagram_client._authenticated = True
        instagram_client._current_username = "testuser"
        instagram_client.rate_limiter.request_count = 10
        instagram_client.rate_limiter.consecutive_errors = 2
        instagram_client.rate_limiter.backoff_until = 123456789.0
        
        # Mock loader context
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock rate limiter stats
        with patch.object(instagram_client.rate_limiter, 'get_request_stats') as mock_stats:
            mock_stats.return_value = {
                'total_requests': 10,
                'recent_requests_10min': 5,
                'session_duration': 1800
            }
            
            session_info = instagram_client.get_session_info()
            
            assert session_info['authenticated'] is True
            assert session_info['username'] == "testuser"
            assert session_info['request_count'] == 10
            assert session_info['consecutive_errors'] == 2
            assert session_info['backoff_until'] == 123456789.0
            assert session_info['total_requests'] == 10
            assert session_info['recent_requests_10min'] == 5
            assert session_info['session_duration'] == 1800
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_rotate_session_if_needed_no_rotation(self, mock_instaloader_class, instagram_client):
        """Test session rotation when not needed."""
        # Mock rate limiter to not need rotation
        instagram_client.rate_limiter.should_rotate_session = Mock(return_value=False)
        
        result = instagram_client.rotate_session_if_needed()
        
        assert result is False
        instagram_client.rate_limiter.should_rotate_session.assert_called_once()
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_rotate_session_if_needed_with_rotation(self, mock_instaloader_class, instagram_client):
        """Test session rotation when needed."""
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client._current_username = "testuser"
        
        # Mock rate limiter to need rotation
        instagram_client.rate_limiter.should_rotate_session = Mock(return_value=True)
        instagram_client.rate_limiter.reset_session = Mock()
        instagram_client.rate_limiter.get_next_user_agent = Mock(return_value="new_user_agent")
        
        # Mock new loader
        mock_new_loader = Mock()
        mock_instaloader_class.return_value = mock_new_loader
        
        # Mock successful re-authentication
        instagram_client.authenticate = Mock(return_value=True)
        
        result = instagram_client.rotate_session_if_needed()
        
        assert result is True
        instagram_client.rate_limiter.should_rotate_session.assert_called_once()
        instagram_client.rate_limiter.reset_session.assert_called_once()
        instagram_client.authenticate.assert_called_once()
        
        # Should have created new loader
        mock_instaloader_class.assert_called_once()
        assert instagram_client.loader == mock_new_loader
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_rotate_session_if_needed_auth_failure(self, mock_instaloader_class, instagram_client):
        """Test session rotation with authentication failure."""
        # Set up authenticated state
        instagram_client._authenticated = True
        instagram_client._current_username = "testuser"
        
        # Mock rate limiter to need rotation
        instagram_client.rate_limiter.should_rotate_session = Mock(return_value=True)
        instagram_client.rate_limiter.reset_session = Mock()
        instagram_client.rate_limiter.get_next_user_agent = Mock(return_value="new_user_agent")
        
        # Mock failed re-authentication
        instagram_client.authenticate = Mock(return_value=False)
        
        result = instagram_client.rotate_session_if_needed()
        
        assert result is False
        instagram_client.authenticate.assert_called_once()
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_rotate_session_if_needed_no_username(self, mock_instaloader_class, instagram_client):
        """Test session rotation when no username is set."""
        # No authenticated state
        instagram_client._authenticated = False
        instagram_client._current_username = None
        
        # Mock rate limiter to need rotation
        instagram_client.rate_limiter.should_rotate_session = Mock(return_value=True)
        instagram_client.rate_limiter.reset_session = Mock()
        instagram_client.rate_limiter.get_next_user_agent = Mock(return_value="new_user_agent")
        
        # Mock authenticate method (should not be called)
        instagram_client.authenticate = Mock()
        
        result = instagram_client.rotate_session_if_needed()
        
        assert result is True
        instagram_client.rate_limiter.should_rotate_session.assert_called_once()
        instagram_client.rate_limiter.reset_session.assert_called_once()
        # Should not try to re-authenticate when no username
        instagram_client.authenticate.assert_not_called()