"""
Custom Exception Classes

This module defines custom exception classes used throughout the application
to provide structured error handling and better error categorization.
"""

from typing import Dict, Any


class ApplicationError(Exception):
    """Base class for application-specific errors."""
    
    def __init__(self, message: str, error_code: str = None, status_code: int = 500, details: Dict[str, Any] = None):
        """
        Initialize application error.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or 'INTERNAL_ERROR'
        self.status_code = status_code
        self.details = details or {}


class ValidationError(ApplicationError):
    """Error for input validation failures."""
    
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(
            message=message,
            error_code='VALIDATION_ERROR',
            status_code=400,
            details={'field': field, 'value': str(value) if value is not None else None}
        )


class AuthenticationError(ApplicationError):
    """Error for authentication failures."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code='AUTHENTICATION_ERROR',
            status_code=401
        )


class AuthorizationError(ApplicationError):
    """Error for authorization failures."""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            error_code='AUTHORIZATION_ERROR',
            status_code=403
        )


class ResourceNotFoundError(ApplicationError):
    """Error for resource not found."""
    
    def __init__(self, resource_type: str, resource_id: str = None):
        message = f"{resource_type} not found"
        if resource_id:
            message += f": {resource_id}"
        
        super().__init__(
            message=message,
            error_code='RESOURCE_NOT_FOUND',
            status_code=404,
            details={'resource_type': resource_type, 'resource_id': resource_id}
        )


class RateLimitError(ApplicationError):
    """Error for rate limiting."""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = None):
        super().__init__(
            message=message,
            error_code='RATE_LIMIT_EXCEEDED',
            status_code=429,
            details={'retry_after': retry_after}
        )


class ExternalServiceError(ApplicationError):
    """Error for external service failures."""
    
    def __init__(self, service: str, message: str = None):
        message = message or f"External service error: {service}"
        super().__init__(
            message=message,
            error_code='EXTERNAL_SERVICE_ERROR',
            status_code=502,
            details={'service': service}
        )