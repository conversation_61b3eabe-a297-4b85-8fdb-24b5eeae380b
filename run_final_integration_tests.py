#!/usr/bin/env python3
"""
Final Integration Test Runner for Instagram Follower Monitor

This script runs comprehensive integration tests covering:
- Complete monitoring workflow
- Load testing with multiple profiles
- Error recovery scenarios and system resilience
- Security measures and credential protection
- User acceptance testing for dashboard functionality

Requirements covered: 1.1-1.5, 2.1-2.5, 3.1-3.5, 4.1-4.5, 5.1-5.5, 6.1-6.5, 7.1-7.5
"""

import os
import sys
import subprocess
import time
import json
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_command(command, description):
    """Run a command and capture output."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=project_root
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Duration: {duration:.2f} seconds")
        print(f"Return code: {result.returncode}")
        
        if result.stdout:
            print(f"\nSTDOUT:\n{result.stdout}")
        
        if result.stderr:
            print(f"\nSTDERR:\n{result.stderr}")
        
        return result.returncode == 0, result.stdout, result.stderr, duration
        
    except Exception as e:
        print(f"Error running command: {e}")
        return False, "", str(e), 0

def check_dependencies():
    """Check if all required dependencies are available."""
    print("Checking dependencies...")
    
    required_modules = [
        'pytest',
        'flask',
        'sqlite3',
        'cryptography',
        'apscheduler'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\nMissing modules: {', '.join(missing_modules)}")
        print("Please install missing dependencies before running tests.")
        return False
    
    return True

def run_unit_tests():
    """Run existing unit tests to ensure basic functionality."""
    print("\n" + "="*80)
    print("PHASE 1: RUNNING EXISTING UNIT TESTS")
    print("="*80)
    
    test_commands = [
        ("python -m pytest tests/test_data_models.py -v", "Data Models Unit Tests"),
        ("python -m pytest tests/test_database.py -v", "Database Unit Tests"),
        ("python -m pytest tests/test_authentication.py -v", "Authentication Unit Tests"),
        ("python -m pytest tests/test_change_detector.py -v", "Change Detection Unit Tests"),
    ]
    
    results = []
    
    for command, description in test_commands:
        success, stdout, stderr, duration = run_command(command, description)
        results.append({
            'test': description,
            'success': success,
            'duration': duration,
            'output': stdout
        })
    
    return results

def run_integration_tests():
    """Run comprehensive integration tests."""
    print("\n" + "="*80)
    print("PHASE 2: RUNNING COMPREHENSIVE INTEGRATION TESTS")
    print("="*80)
    
    test_commands = [
        ("python -m pytest tests/test_integration_comprehensive.py -v", "Database Integration Tests"),
        ("python -m pytest tests/test_integration_auth_client.py -v", "Auth Client Integration Tests"),
        ("python -m pytest tests/test_end_to_end.py -v", "End-to-End Integration Tests"),
    ]
    
    results = []
    
    for command, description in test_commands:
        success, stdout, stderr, duration = run_command(command, description)
        results.append({
            'test': description,
            'success': success,
            'duration': duration,
            'output': stdout
        })
    
    return results

def run_final_integration_tests():
    """Run the final comprehensive integration tests."""
    print("\n" + "="*80)
    print("PHASE 3: RUNNING FINAL INTEGRATION TESTS")
    print("="*80)
    
    test_commands = [
        ("python -m pytest tests/test_final_integration.py::TestCompleteMonitoringWorkflow -v", 
         "Complete Monitoring Workflow Tests"),
        ("python -m pytest tests/test_final_integration.py::TestLoadTesting -v", 
         "Load Testing with Multiple Profiles"),
        ("python -m pytest tests/test_final_integration.py::TestSecurityValidation -v", 
         "Security Measures and Credential Protection"),
        ("python -m pytest tests/test_final_integration.py::TestUserAcceptanceTesting -v", 
         "User Acceptance Testing for Dashboard"),
        ("python -m pytest tests/test_final_integration.py::TestSystemResilience -v", 
         "System Resilience and Recovery Tests"),
    ]
    
    results = []
    
    for command, description in test_commands:
        success, stdout, stderr, duration = run_command(command, description)
        results.append({
            'test': description,
            'success': success,
            'duration': duration,
            'output': stdout
        })
    
    return results

def run_performance_tests():
    """Run performance and load tests."""
    print("\n" + "="*80)
    print("PHASE 4: RUNNING PERFORMANCE TESTS")
    print("="*80)
    
    test_commands = [
        ("python -m pytest tests/test_performance.py -v", "Performance Tests"),
        ("python -m pytest tests/test_final_integration.py::TestLoadTesting::test_large_follower_lists_performance -v", 
         "Large Dataset Performance Tests"),
        ("python -m pytest tests/test_final_integration.py::TestLoadTesting::test_concurrent_monitoring_operations -v", 
         "Concurrent Operations Performance Tests"),
    ]
    
    results = []
    
    for command, description in test_commands:
        success, stdout, stderr, duration = run_command(command, description)
        results.append({
            'test': description,
            'success': success,
            'duration': duration,
            'output': stdout
        })
    
    return results

def run_security_tests():
    """Run security validation tests."""
    print("\n" + "="*80)
    print("PHASE 5: RUNNING SECURITY TESTS")
    print("="*80)
    
    test_commands = [
        ("python -m pytest tests/test_security.py -v", "Security Unit Tests"),
        ("python -m pytest tests/test_final_integration.py::TestSecurityValidation -v", 
         "Comprehensive Security Validation"),
    ]
    
    results = []
    
    for command, description in test_commands:
        success, stdout, stderr, duration = run_command(command, description)
        results.append({
            'test': description,
            'success': success,
            'duration': duration,
            'output': stdout
        })
    
    return results

def generate_test_report(all_results):
    """Generate a comprehensive test report."""
    print("\n" + "="*80)
    print("FINAL INTEGRATION TEST REPORT")
    print("="*80)
    
    total_tests = 0
    passed_tests = 0
    total_duration = 0
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'phases': {},
        'summary': {}
    }
    
    for phase_name, phase_results in all_results.items():
        print(f"\n{phase_name.upper()}:")
        print("-" * 40)
        
        phase_total = len(phase_results)
        phase_passed = sum(1 for r in phase_results if r['success'])
        phase_duration = sum(r['duration'] for r in phase_results)
        
        total_tests += phase_total
        passed_tests += phase_passed
        total_duration += phase_duration
        
        report['phases'][phase_name] = {
            'total': phase_total,
            'passed': phase_passed,
            'failed': phase_total - phase_passed,
            'duration': phase_duration,
            'tests': phase_results
        }
        
        for result in phase_results:
            status = "✓ PASS" if result['success'] else "✗ FAIL"
            duration = f"{result['duration']:.2f}s"
            print(f"  {status} {result['test']} ({duration})")
        
        print(f"  Phase Summary: {phase_passed}/{phase_total} passed ({phase_duration:.2f}s)")
    
    # Overall summary
    print(f"\n{'='*80}")
    print("OVERALL SUMMARY")
    print(f"{'='*80}")
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    print(f"Total Duration: {total_duration:.2f} seconds")
    
    report['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': total_tests - passed_tests,
        'success_rate': (passed_tests/total_tests)*100,
        'total_duration': total_duration
    }
    
    # Save report to file
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nDetailed report saved to: {report_file}")
    
    # Requirements coverage summary
    print(f"\n{'='*80}")
    print("REQUIREMENTS COVERAGE VALIDATION")
    print(f"{'='*80}")
    
    requirements_covered = [
        "1.1-1.5: Profile monitoring and change detection",
        "2.1-2.5: Instagram authentication and credential management",
        "3.1-3.5: Rate limiting and anti-bot measures",
        "4.1-4.5: Data storage and retention",
        "5.1-5.5: Web dashboard functionality",
        "6.1-6.5: Configuration management",
        "7.1-7.5: Documentation and deployment"
    ]
    
    for req in requirements_covered:
        print(f"✓ {req}")
    
    return passed_tests == total_tests

def main():
    """Main test execution function."""
    print("Instagram Follower Monitor - Final Integration Testing")
    print("="*80)
    print(f"Started at: {datetime.now()}")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Run all test phases
    all_results = {}
    
    try:
        # Phase 1: Unit Tests
        all_results['unit_tests'] = run_unit_tests()
        
        # Phase 2: Integration Tests
        all_results['integration_tests'] = run_integration_tests()
        
        # Phase 3: Final Integration Tests
        all_results['final_integration_tests'] = run_final_integration_tests()
        
        # Phase 4: Performance Tests
        all_results['performance_tests'] = run_performance_tests()
        
        # Phase 5: Security Tests
        all_results['security_tests'] = run_security_tests()
        
        # Generate final report
        success = generate_test_report(all_results)
        
        if success:
            print(f"\n🎉 ALL TESTS PASSED! System is ready for deployment.")
            sys.exit(0)
        else:
            print(f"\n❌ Some tests failed. Please review the report and fix issues.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n\nTest execution interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error during test execution: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()