{% extends "base.html" %}

{% block title %}Validation Error - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Validation Error
                    </h4>
                </div>
                <div class="card-body">
                    <h5 class="card-title">Input Validation Failed</h5>
                    <p class="card-text">{{ error_message }}</p>
                    
                    {% if field or value %}
                    <div class="mt-3">
                        <h6>Error Details:</h6>
                        <ul class="list-unstyled">
                            {% if field %}
                            <li><strong>Field:</strong> {{ field }}</li>
                            {% endif %}
                            {% if value %}
                            <li><strong>Value:</strong> <code>{{ value }}</code></li>
                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <div class="mt-4">
                        <h6>How to fix this:</h6>
                        <ul>
                            <li>Check that all required fields are filled out</li>
                            <li>Ensure data is in the correct format</li>
                            <li>Verify that usernames are valid Instagram usernames</li>
                            <li>Make sure numeric values are within acceptable ranges</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <button onclick="history.back()" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Go Back and Fix
                        </button>
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}