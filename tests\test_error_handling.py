"""
Tests for Error Handling and Logging

This module tests the comprehensive error handling and logging functionality.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from services.logging_config import setup_logging, get_logger, LoggingManager
from services.system_monitor import SystemMonitor, AlertLevel, SystemAlert
from services.log_manager import LogManager
from services.exceptions import (
    ApplicationError, ValidationError, AuthenticationError,
    ExternalServiceError
)
from web.error_handlers import create_error_response
from config import Config


class TestLoggingConfiguration:
    """Test logging configuration and structured logging."""
    
    def test_logging_setup(self):
        """Test that logging is set up correctly."""
        config = Config()
        logging_manager = setup_logging(config)
        
        assert isinstance(logging_manager, LoggingManager)
        
        # Test structured logger
        logger = get_logger(__name__)
        assert logger is not None
        
        # Test logging with extra data
        logger.info("Test message", component="test", value=123)
        logger.error("Test error", error_type="TestError", details={"key": "value"})
    
    def test_log_function_decorator(self):
        """Test the log_function_call decorator."""
        from services.logging_config import log_function_call
        
        @log_function_call
        def test_function(x, y=10):
            return x + y
        
        result = test_function(5, y=15)
        assert result == 20
    
    def test_performance_logging(self):
        """Test performance logging context manager."""
        from services.logging_config import log_performance
        
        with log_performance("test_operation"):
            # Simulate some work
            sum(range(1000))


class TestErrorHandlers:
    """Test custom error classes and handlers."""
    
    def test_application_error(self):
        """Test ApplicationError class."""
        error = ApplicationError(
            message="Test error",
            error_code="TEST_ERROR",
            status_code=400,
            details={"field": "test_field"}
        )
        
        assert error.message == "Test error"
        assert error.error_code == "TEST_ERROR"
        assert error.status_code == 400
        assert error.details["field"] == "test_field"
    
    def test_validation_error(self):
        """Test ValidationError class."""
        error = ValidationError("Invalid input", field="username", value="invalid@user")
        
        assert error.status_code == 400
        assert error.error_code == "VALIDATION_ERROR"
        assert error.details["field"] == "username"
        assert error.details["value"] == "invalid@user"
    
    def test_authentication_error(self):
        """Test AuthenticationError class."""
        error = AuthenticationError("Login failed")
        
        assert error.status_code == 401
        assert error.error_code == "AUTHENTICATION_ERROR"
        assert error.message == "Login failed"
    
    def test_external_service_error(self):
        """Test ExternalServiceError class."""
        error = ExternalServiceError("Instagram", "Connection timeout")
        
        assert error.status_code == 502
        assert error.error_code == "EXTERNAL_SERVICE_ERROR"
        assert error.details["service"] == "Instagram"
    
    def test_create_error_response(self):
        """Test error response creation."""
        error = ValidationError("Invalid data", field="email")
        response, status_code = create_error_response(error)
        
        assert status_code == 400
        assert response["error"]["code"] == "VALIDATION_ERROR"
        assert response["error"]["message"] == "Invalid data"
        assert response["error"]["details"]["field"] == "email"


class TestSystemMonitor:
    """Test system monitoring functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = Config()
        self.monitor = SystemMonitor(self.config)
    
    def test_monitor_initialization(self):
        """Test system monitor initialization."""
        assert self.monitor.config == self.config
        assert len(self.monitor.alerts) == 0
        assert len(self.monitor.metrics) == 0
    
    def test_record_error(self):
        """Test error recording."""
        test_error = ValueError("Test error")
        self.monitor.record_error("test_component", test_error)
        
        assert len(self.monitor.alerts) == 1
        alert = self.monitor.alerts[0]
        assert alert.component == "test_component"
        assert "Test error" in alert.message
    
    def test_record_performance_metric(self):
        """Test performance metric recording."""
        self.monitor.record_performance_metric(
            name="response_time",
            value=1.5,
            unit="seconds",
            component="web"
        )
        
        assert len(self.monitor.metrics) == 1
        metric = self.monitor.metrics[0]
        assert metric.name == "response_time"
        assert metric.value == 1.5
        assert metric.unit == "seconds"
    
    def test_system_health_summary(self):
        """Test system health summary."""
        # Add some test data
        self.monitor.record_error("test_component", ValueError("Test error"))
        self.monitor.record_performance_metric("test_metric", 100, "ms")
        
        health = self.monitor.get_system_health()
        
        assert "status" in health
        assert "alerts" in health
        assert "components" in health
        assert "timestamp" in health
    
    def test_alert_handlers(self):
        """Test alert handler functionality."""
        handler_called = False
        received_alert = None
        
        def test_handler(alert: SystemAlert):
            nonlocal handler_called, received_alert
            handler_called = True
            received_alert = alert
        
        self.monitor.add_alert_handler(test_handler)
        self.monitor.record_error("test_component", ValueError("Test error"))
        
        assert handler_called
        assert received_alert is not None
        assert received_alert.component == "test_component"


class TestLogManager:
    """Test log management functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = Config()
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create a temporary log manager with test directory
        self.log_manager = LogManager(self.config)
        self.log_manager.log_dir = self.temp_dir
    
    def teardown_method(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_log_manager_initialization(self):
        """Test log manager initialization."""
        assert self.log_manager.config == self.config
        assert self.log_manager.log_dir.exists()
    
    def test_log_statistics(self):
        """Test log statistics generation."""
        # Create some test log files with content
        (self.temp_dir / "test.log").write_text("Test log content with more data to ensure size > 0")
        (self.temp_dir / "error.log").write_text("Error log content with more data to ensure size > 0")
        
        stats = self.log_manager.get_log_statistics()
        
        assert stats["total_files"] >= 2  # May include other log files
        assert stats["total_size_mb"] >= 0  # Allow 0 size for small files
        assert ".log" in stats["by_type"]
    
    def test_should_rotate_log(self):
        """Test log rotation decision."""
        # Create a small test file
        test_file = self.temp_dir / "test.log"
        test_file.write_text("small content")
        
        # Should not rotate small file
        assert not self.log_manager._should_rotate_log(test_file)
        
        # Create a large test file
        large_content = "x" * (self.log_manager.max_log_size + 1)
        test_file.write_text(large_content)
        
        # Should rotate large file
        assert self.log_manager._should_rotate_log(test_file)
    
    def test_cleanup_old_logs(self):
        """Test old log cleanup."""
        # Create an old log file
        old_log = self.temp_dir / "old.log"
        old_log.write_text("Old log content")
        
        # Set modification time to old date using os.utime
        import os
        old_time = (datetime.now() - timedelta(days=35)).timestamp()
        os.utime(old_log, (old_time, old_time))
        
        # Create a recent log file
        recent_log = self.temp_dir / "recent.log"
        recent_log.write_text("Recent log content")
        
        # Run cleanup
        self.log_manager.cleanup_old_logs()
        
        # Old file should be deleted, recent file should remain
        assert not old_log.exists()
        assert recent_log.exists()


class TestIntegration:
    """Integration tests for error handling and logging."""
    
    def test_flask_error_handling(self):
        """Test Flask error handling integration."""
        from web.app import create_app
        
        config = Config()
        app = create_app(config)
        
        with app.test_client() as client:
            # Test 404 error
            response = client.get('/nonexistent')
            assert response.status_code == 404
            
            # Test API 404 error
            response = client.get('/api/nonexistent', 
                                headers={'Accept': 'application/json'})
            assert response.status_code == 404
            assert response.is_json
    
    def test_logging_in_services(self):
        """Test that services use structured logging."""
        from services.instagram_client import InstagramClient
        
        config = Config()
        client = InstagramClient(config)
        
        # This should log without errors
        assert client is not None
    
    @patch('services.system_monitor.get_system_monitor')
    def test_error_monitoring_integration(self, mock_get_monitor):
        """Test integration between error handling and monitoring."""
        mock_monitor = Mock()
        mock_get_monitor.return_value = mock_monitor
        
        # Simulate an error that should be monitored
        error = ExternalServiceError("Instagram", "Connection failed")
        
        # The error should be recorded in the monitor
        # (This would happen in actual error handling code)
        mock_monitor.record_error.assert_not_called()  # Not called yet
        
        # Simulate error handling
        mock_monitor.record_error("instagram_client", error)
        mock_monitor.record_error.assert_called_once_with("instagram_client", error)


if __name__ == "__main__":
    pytest.main([__file__])