"""
Change detection algorithms for Instagram follower monitoring.

This module implements algorithms to compare current vs previous follower/following
lists and detect changes with proper categorization.
"""

import logging
from typing import Set, List, Dict, Any, Optional, Tuple
from datetime import datetime

from models.data_models import Follower<PERSON>hange, ChangeType, validate_username

logger = logging.getLogger(__name__)


class ChangeDetector:
    """Detects and categorizes changes in follower/following lists."""
    
    def __init__(self):
        """Initialize change detector."""
        self._detection_stats = {
            'total_detections': 0,
            'changes_detected': 0,
            'follower_changes': 0,
            'following_changes': 0,
            'last_detection_time': None
        }
    
    def detect_changes(self, profile_username: str, profile_id: Optional[int],
                      current_followers: Set[str], previous_followers: Set[str],
                      current_following: Set[str], previous_following: Set[str]) -> List[FollowerChange]:
        """Detect all changes between current and previous lists.
        
        Args:
            profile_username: Username of the profile being monitored
            profile_id: Database ID of the profile
            current_followers: Current set of followers
            previous_followers: Previous set of followers
            current_following: Current set of following
            previous_following: Previous set of following
            
        Returns:
            List[FollowerChange]: List of detected changes
        """
        try:
            profile_username = validate_username(profile_username)
            
            logger.debug(f"Detecting changes for {profile_username}")
            logger.debug(f"Current: {len(current_followers)} followers, {len(current_following)} following")
            logger.debug(f"Previous: {len(previous_followers)} followers, {len(previous_following)} following")
            
            changes = []
            timestamp = datetime.now()
            
            # Detect follower changes
            follower_changes = self._detect_follower_changes(
                profile_username, profile_id, current_followers, previous_followers, timestamp
            )
            changes.extend(follower_changes)
            
            # Detect following changes
            following_changes = self._detect_following_changes(
                profile_username, profile_id, current_following, previous_following, timestamp
            )
            changes.extend(following_changes)
            
            # Update statistics
            self._update_detection_stats(len(follower_changes), len(following_changes))
            
            logger.info(f"Detected {len(changes)} total changes for {profile_username}: "
                       f"{len(follower_changes)} follower changes, {len(following_changes)} following changes")
            
            return changes
            
        except Exception as e:
            logger.error(f"Error detecting changes for {profile_username}: {e}")
            return []
    
    def _detect_follower_changes(self, profile_username: str, profile_id: Optional[int],
                                current_followers: Set[str], previous_followers: Set[str],
                                timestamp: datetime) -> List[FollowerChange]:
        """Detect changes in followers list.
        
        Args:
            profile_username: Username of the profile
            profile_id: Database ID of the profile
            current_followers: Current followers set
            previous_followers: Previous followers set
            timestamp: Timestamp for the changes
            
        Returns:
            List[FollowerChange]: Follower changes detected
        """
        changes = []
        
        try:
            # Clean and validate usernames
            current_clean = self._clean_username_set(current_followers)
            previous_clean = self._clean_username_set(previous_followers)
            
            # Detect new followers (gained)
            new_followers = current_clean - previous_clean
            for username in new_followers:
                change = FollowerChange(
                    profile_username=profile_username,
                    affected_username=username,
                    change_type=ChangeType.GAINED,
                    timestamp=timestamp,
                    profile_id=profile_id
                )
                changes.append(change)
            
            # Detect lost followers (unfollowed)
            lost_followers = previous_clean - current_clean
            for username in lost_followers:
                change = FollowerChange(
                    profile_username=profile_username,
                    affected_username=username,
                    change_type=ChangeType.LOST,
                    timestamp=timestamp,
                    profile_id=profile_id
                )
                changes.append(change)
            
            if new_followers or lost_followers:
                logger.debug(f"Follower changes for {profile_username}: "
                           f"+{len(new_followers)} gained, -{len(lost_followers)} lost")
            
            return changes
            
        except Exception as e:
            logger.error(f"Error detecting follower changes for {profile_username}: {e}")
            return []
    
    def _detect_following_changes(self, profile_username: str, profile_id: Optional[int],
                                 current_following: Set[str], previous_following: Set[str],
                                 timestamp: datetime) -> List[FollowerChange]:
        """Detect changes in following list.
        
        Args:
            profile_username: Username of the profile
            profile_id: Database ID of the profile
            current_following: Current following set
            previous_following: Previous following set
            timestamp: Timestamp for the changes
            
        Returns:
            List[FollowerChange]: Following changes detected
        """
        changes = []
        
        try:
            # Clean and validate usernames
            current_clean = self._clean_username_set(current_following)
            previous_clean = self._clean_username_set(previous_following)
            
            # Detect new following (started following)
            new_following = current_clean - previous_clean
            for username in new_following:
                change = FollowerChange(
                    profile_username=profile_username,
                    affected_username=username,
                    change_type=ChangeType.STARTED_FOLLOWING,
                    timestamp=timestamp,
                    profile_id=profile_id
                )
                changes.append(change)
            
            # Detect stopped following (unfollowed)
            stopped_following = previous_clean - current_clean
            for username in stopped_following:
                change = FollowerChange(
                    profile_username=profile_username,
                    affected_username=username,
                    change_type=ChangeType.STOPPED_FOLLOWING,
                    timestamp=timestamp,
                    profile_id=profile_id
                )
                changes.append(change)
            
            if new_following or stopped_following:
                logger.debug(f"Following changes for {profile_username}: "
                           f"+{len(new_following)} started following, -{len(stopped_following)} stopped following")
            
            return changes
            
        except Exception as e:
            logger.error(f"Error detecting following changes for {profile_username}: {e}")
            return []
    
    def _clean_username_set(self, usernames: Set[str]) -> Set[str]:
        """Clean and validate a set of usernames.
        
        Args:
            usernames: Set of usernames to clean
            
        Returns:
            Set[str]: Cleaned set of valid usernames
        """
        clean_usernames = set()
        
        for username in usernames:
            try:
                clean_username = validate_username(username)
                clean_usernames.add(clean_username)
            except ValueError as e:
                logger.warning(f"Invalid username '{username}' excluded from change detection: {e}")
                continue
        
        return clean_usernames
    
    def detect_follower_changes_only(self, profile_username: str, profile_id: Optional[int],
                                    current_followers: Set[str], previous_followers: Set[str]) -> List[FollowerChange]:
        """Detect only follower changes (not following changes).
        
        Args:
            profile_username: Username of the profile
            profile_id: Database ID of the profile
            current_followers: Current followers set
            previous_followers: Previous followers set
            
        Returns:
            List[FollowerChange]: Follower changes detected
        """
        timestamp = datetime.now()
        return self._detect_follower_changes(
            profile_username, profile_id, current_followers, previous_followers, timestamp
        )
    
    def detect_following_changes_only(self, profile_username: str, profile_id: Optional[int],
                                     current_following: Set[str], previous_following: Set[str]) -> List[FollowerChange]:
        """Detect only following changes (not follower changes).
        
        Args:
            profile_username: Username of the profile
            profile_id: Database ID of the profile
            current_following: Current following set
            previous_following: Previous following set
            
        Returns:
            List[FollowerChange]: Following changes detected
        """
        timestamp = datetime.now()
        return self._detect_following_changes(
            profile_username, profile_id, current_following, previous_following, timestamp
        )
    
    def analyze_change_patterns(self, changes: List[FollowerChange]) -> Dict[str, Any]:
        """Analyze patterns in detected changes.
        
        Args:
            changes: List of changes to analyze
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        try:
            if not changes:
                return {
                    'total_changes': 0,
                    'follower_changes': 0,
                    'following_changes': 0,
                    'net_follower_change': 0,
                    'net_following_change': 0,
                    'change_types': {},
                    'affected_users': []
                }
            
            # Categorize changes
            follower_changes = [c for c in changes if c.is_follower_change]
            following_changes = [c for c in changes if c.is_following_change]
            
            # Count change types
            change_type_counts = {}
            for change in changes:
                change_type = change.change_type.value
                change_type_counts[change_type] = change_type_counts.get(change_type, 0) + 1
            
            # Calculate net changes
            gained_followers = len([c for c in follower_changes if c.change_type == ChangeType.GAINED])
            lost_followers = len([c for c in follower_changes if c.change_type == ChangeType.LOST])
            net_follower_change = gained_followers - lost_followers
            
            started_following = len([c for c in following_changes if c.change_type == ChangeType.STARTED_FOLLOWING])
            stopped_following = len([c for c in following_changes if c.change_type == ChangeType.STOPPED_FOLLOWING])
            net_following_change = started_following - stopped_following
            
            # Get affected users
            affected_users = list(set(c.affected_username for c in changes))
            
            return {
                'total_changes': len(changes),
                'follower_changes': len(follower_changes),
                'following_changes': len(following_changes),
                'net_follower_change': net_follower_change,
                'net_following_change': net_following_change,
                'change_types': change_type_counts,
                'affected_users': affected_users,
                'gained_followers': gained_followers,
                'lost_followers': lost_followers,
                'started_following': started_following,
                'stopped_following': stopped_following
            }
            
        except Exception as e:
            logger.error(f"Error analyzing change patterns: {e}")
            return {
                'total_changes': len(changes) if changes else 0,
                'error': str(e)
            }
    
    def compare_lists(self, current_list: Set[str], previous_list: Set[str]) -> Dict[str, Set[str]]:
        """Compare two lists and return the differences.
        
        Args:
            current_list: Current list of usernames
            previous_list: Previous list of usernames
            
        Returns:
            Dict[str, Set[str]]: Dictionary with 'added', 'removed', and 'unchanged' sets
        """
        try:
            # Clean both lists
            current_clean = self._clean_username_set(current_list)
            previous_clean = self._clean_username_set(previous_list)
            
            return {
                'added': current_clean - previous_clean,
                'removed': previous_clean - current_clean,
                'unchanged': current_clean & previous_clean
            }
            
        except Exception as e:
            logger.error(f"Error comparing lists: {e}")
            return {
                'added': set(),
                'removed': set(),
                'unchanged': set(),
                'error': str(e)
            }
    
    def is_significant_change(self, changes: List[FollowerChange], 
                             threshold_percentage: float = 5.0,
                             threshold_absolute: int = 10) -> bool:
        """Determine if the detected changes are significant.
        
        Args:
            changes: List of changes to evaluate
            threshold_percentage: Percentage threshold for significance
            threshold_absolute: Absolute number threshold for significance
            
        Returns:
            bool: True if changes are considered significant
        """
        try:
            if not changes:
                return False
            
            # Count total changes
            total_changes = len(changes)
            
            # Check absolute threshold
            if total_changes >= threshold_absolute:
                return True
            
            # For percentage threshold, we'd need the total follower/following counts
            # This is a simplified check - in practice you might want to pass those counts
            logger.debug(f"Change significance check: {total_changes} changes (threshold: {threshold_absolute})")
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking change significance: {e}")
            return False
    
    def filter_changes_by_type(self, changes: List[FollowerChange], 
                              change_types: List[ChangeType]) -> List[FollowerChange]:
        """Filter changes by specific change types.
        
        Args:
            changes: List of changes to filter
            change_types: List of change types to include
            
        Returns:
            List[FollowerChange]: Filtered list of changes
        """
        try:
            return [change for change in changes if change.change_type in change_types]
            
        except Exception as e:
            logger.error(f"Error filtering changes by type: {e}")
            return []
    
    def group_changes_by_profile(self, changes: List[FollowerChange]) -> Dict[str, List[FollowerChange]]:
        """Group changes by profile username.
        
        Args:
            changes: List of changes to group
            
        Returns:
            Dict[str, List[FollowerChange]]: Changes grouped by profile
        """
        try:
            grouped = {}
            
            for change in changes:
                profile = change.profile_username
                if profile not in grouped:
                    grouped[profile] = []
                grouped[profile].append(change)
            
            return grouped
            
        except Exception as e:
            logger.error(f"Error grouping changes by profile: {e}")
            return {}
    
    def _update_detection_stats(self, follower_changes: int, following_changes: int) -> None:
        """Update detection statistics.
        
        Args:
            follower_changes: Number of follower changes detected
            following_changes: Number of following changes detected
        """
        try:
            self._detection_stats['total_detections'] += 1
            self._detection_stats['changes_detected'] += follower_changes + following_changes
            self._detection_stats['follower_changes'] += follower_changes
            self._detection_stats['following_changes'] += following_changes
            self._detection_stats['last_detection_time'] = datetime.now()
            
        except Exception as e:
            logger.error(f"Error updating detection stats: {e}")
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """Get current detection statistics.
        
        Returns:
            Dict[str, Any]: Detection statistics
        """
        stats = self._detection_stats.copy()
        
        # Format last detection time
        if stats['last_detection_time']:
            stats['last_detection_time'] = stats['last_detection_time'].isoformat()
        
        # Calculate average changes per detection
        if stats['total_detections'] > 0:
            stats['average_changes_per_detection'] = stats['changes_detected'] / stats['total_detections']
        else:
            stats['average_changes_per_detection'] = 0.0
        
        return stats
    
    def reset_statistics(self) -> None:
        """Reset detection statistics."""
        self._detection_stats = {
            'total_detections': 0,
            'changes_detected': 0,
            'follower_changes': 0,
            'following_changes': 0,
            'last_detection_time': None
        }
        logger.info("Change detector statistics reset")