# Instagram Follower Monitor - Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the Instagram Follower Monitor application.

## Table of Contents

1. [Quick Diagnostics](#quick-diagnostics)
2. [Installation Issues](#installation-issues)
3. [Authentication Problems](#authentication-problems)
4. [Monitoring Issues](#monitoring-issues)
5. [Performance Problems](#performance-problems)
6. [Database Issues](#database-issues)
7. [Network and Connectivity](#network-and-connectivity)
8. [Instagram API Issues](#instagram-api-issues)
9. [Web Interface Problems](#web-interface-problems)
10. [Log Analysis](#log-analysis)
11. [Recovery Procedures](#recovery-procedures)

## Quick Diagnostics

### System Health Check

Run these commands to quickly assess system status:

```bash
# Check application status
curl -f http://localhost:8000/health

# Check service status (if using systemd)
sudo systemctl status instagram-monitor

# Check recent logs
tail -n 50 logs/application.log

# Check database connectivity
python3 -c "
from database.connection import DatabaseManager
db = DatabaseManager()
print('Database:', 'OK' if db.test_connection() else 'FAILED')
"

# Check disk space
df -h

# Check memory usage
free -h
```

### Common Status Indicators

| Status | Meaning | Action Required |
|--------|---------|-----------------|
| 🟢 All systems operational | Everything working normally | None |
| 🟡 Authentication warning | Instagram credentials need attention | Update credentials |
| 🟠 Performance degraded | System running slowly | Check resources |
| 🔴 Service unavailable | Critical system failure | Immediate attention needed |

## Installation Issues

### Python Version Problems

**Problem**: `python3: command not found` or version conflicts

**Solutions**:
```bash
# Check Python version
python3 --version

# Install Python 3.9+ on Ubuntu/Debian
sudo apt update
sudo apt install python3.9 python3.9-venv python3.9-pip

# Install Python 3.9+ on CentOS/RHEL
sudo yum install python39 python39-pip

# Create virtual environment with specific Python version
python3.9 -m venv venv
```

### Dependency Installation Failures

**Problem**: `pip install` fails with compilation errors

**Solutions**:
```bash
# Install build dependencies on Ubuntu/Debian
sudo apt install build-essential python3-dev libffi-dev libssl-dev

# Install build dependencies on CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel libffi-devel openssl-devel

# Upgrade pip and setuptools
pip install --upgrade pip setuptools wheel

# Install with no cache (if cache is corrupted)
pip install --no-cache-dir -r requirements.txt
```

### Permission Issues

**Problem**: Permission denied errors during installation

**Solutions**:
```bash
# Fix ownership of application directory
sudo chown -R $USER:$USER /var/www/instagram-monitor

# Set correct permissions
chmod 755 /var/www/instagram-monitor
chmod 644 /var/www/instagram-monitor/.env
chmod 755 /var/www/instagram-monitor/logs
chmod 755 /var/www/instagram-monitor/backups

# For systemd service
sudo chown -R www-data:www-data /var/www/instagram-monitor
```

### Database Initialization Failures

**Problem**: Database creation fails or tables are missing

**Solutions**:
```bash
# Remove corrupted database
rm -f instagram_monitor.db scheduler_jobs.db

# Reinitialize database
python init_database.py

# Check database schema
sqlite3 instagram_monitor.db ".schema"

# Verify tables exist
sqlite3 instagram_monitor.db ".tables"
```

## Authentication Problems

### Instagram Login Failures

**Problem**: Cannot authenticate with Instagram

**Diagnostic Steps**:
```bash
# Check stored credentials (without revealing them)
python3 -c "
from services.authentication import AuthenticationManager
auth = AuthenticationManager()
print('Credentials stored:', auth.has_credentials())
"

# Test authentication
python3 -c "
from services.instagram_client import InstagramClient
client = InstagramClient()
print('Auth test:', client.test_authentication())
"
```

**Common Causes and Solutions**:

1. **Invalid Credentials**:
   - Verify username and password are correct
   - Check for typos in username (no @ symbol needed)
   - Ensure password hasn't been changed

2. **Two-Factor Authentication**:
   - Disable 2FA temporarily for testing
   - Use app-specific password if available
   - Check 2FA code generation timing

3. **Account Restrictions**:
   - Verify account isn't suspended or restricted
   - Check if account requires phone verification
   - Ensure account isn't flagged for suspicious activity

4. **Rate Limiting**:
   - Wait 15-30 minutes before retrying
   - Reduce monitoring frequency
   - Check for multiple login attempts

### Credential Storage Issues

**Problem**: Credentials not saving or encryption errors

**Solutions**:
```bash
# Check encryption key
echo $ENCRYPTION_KEY

# Generate new encryption key if missing
python3 -c "
from cryptography.fernet import Fernet
print('New key:', Fernet.generate_key().decode())
"

# Test encryption/decryption
python3 -c "
from services.authentication import AuthenticationManager
auth = AuthenticationManager()
auth.store_credentials('test_user', 'test_pass')
print('Test successful')
"
```

### Session Management Problems

**Problem**: Frequent re-authentication required

**Solutions**:
- Increase session timeout in configuration
- Check for system clock synchronization issues
- Verify Instagram isn't forcing re-authentication
- Review rate limiting settings

## Monitoring Issues

### Profiles Not Being Monitored

**Problem**: Profiles added but no monitoring activity

**Diagnostic Steps**:
```bash
# Check profile status in database
sqlite3 instagram_monitor.db "
SELECT username, monitoring_enabled, last_scan 
FROM profiles;
"

# Check scheduler status
python3 -c "
from services.scheduler import SchedulerManager
scheduler = SchedulerManager()
print('Jobs:', scheduler.get_job_status())
"

# Check recent scheduler logs
tail -f logs/scheduler.log
```

**Solutions**:
1. **Profile Not Enabled**:
   - Check profile settings in web interface
   - Enable monitoring for the profile
   - Verify profile exists and is accessible

2. **Scheduler Not Running**:
   - Restart the scheduler service
   - Check for scheduler errors in logs
   - Verify APScheduler configuration

3. **Authentication Required**:
   - Ensure Instagram credentials are valid
   - Check if profile requires authentication to view
   - Verify profile privacy settings

### Missing or Delayed Changes

**Problem**: Changes not detected or detected late

**Diagnostic Steps**:
```bash
# Check last scan times
sqlite3 instagram_monitor.db "
SELECT username, last_scan, 
       datetime(last_scan, 'localtime') as local_time
FROM profiles;
"

# Manual scan test
python3 -c "
from services.monitoring_service import MonitoringService
service = MonitoringService()
result = service.scan_profile('username_here')
print('Scan result:', result)
"
```

**Solutions**:
1. **Increase Scan Frequency**:
   - Reduce monitoring interval (but respect rate limits)
   - Enable priority monitoring for important profiles
   - Check system resources aren't limiting scans

2. **Rate Limiting Issues**:
   - Increase delays between requests
   - Reduce number of concurrent profiles
   - Monitor for 429 (rate limit) errors

3. **Network Issues**:
   - Check internet connectivity
   - Verify DNS resolution
   - Test Instagram accessibility

### Incorrect Change Detection

**Problem**: False positives or missed changes

**Diagnostic Steps**:
```bash
# Check change detection algorithm
python3 -c "
from services.change_detector import ChangeDetector
detector = ChangeDetector()
# Test with known data sets
current = {'user1', 'user2', 'user3'}
previous = {'user1', 'user2', 'user4'}
changes = detector.detect_changes(current, previous)
print('Changes:', changes)
"
```

**Solutions**:
1. **Algorithm Issues**:
   - Review change detection logic
   - Check for case sensitivity issues
   - Verify data normalization

2. **Data Corruption**:
   - Check database integrity
   - Verify backup and restore procedures
   - Look for concurrent access issues

## Performance Problems

### Slow Response Times

**Problem**: Web interface or API responses are slow

**Diagnostic Steps**:
```bash
# Check system resources
top
htop
iostat -x 1

# Check database performance
sqlite3 instagram_monitor.db "
.timer on
SELECT COUNT(*) FROM follower_changes;
"

# Profile application performance
python3 -m cProfile -o profile.stats app.py
```

**Solutions**:
1. **Database Optimization**:
   ```sql
   -- Add missing indexes
   CREATE INDEX IF NOT EXISTS idx_changes_profile_timestamp 
   ON follower_changes(profile_id, timestamp);
   
   CREATE INDEX IF NOT EXISTS idx_followers_profile 
   ON current_followers(profile_id);
   
   -- Analyze database
   ANALYZE;
   
   -- Vacuum database
   VACUUM;
   ```

2. **Memory Issues**:
   - Increase system RAM
   - Optimize Python memory usage
   - Implement result pagination
   - Add caching layer

3. **CPU Bottlenecks**:
   - Increase CPU cores
   - Optimize algorithms
   - Reduce concurrent operations
   - Profile code for bottlenecks

### High Memory Usage

**Problem**: Application consuming excessive memory

**Diagnostic Steps**:
```bash
# Monitor memory usage
ps aux | grep python
pmap -x $(pgrep -f instagram-monitor)

# Python memory profiling
python3 -c "
import tracemalloc
tracemalloc.start()
# Run your code here
current, peak = tracemalloc.get_traced_memory()
print(f'Current: {current / 1024 / 1024:.1f} MB')
print(f'Peak: {peak / 1024 / 1024:.1f} MB')
"
```

**Solutions**:
1. **Memory Leaks**:
   - Review code for circular references
   - Implement proper cleanup in loops
   - Use generators for large datasets
   - Monitor object lifecycle

2. **Large Datasets**:
   - Implement pagination
   - Process data in chunks
   - Use database cursors
   - Implement data archiving

### Database Performance Issues

**Problem**: Database operations are slow

**Solutions**:
```sql
-- Check database size
SELECT 
    name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size(name)) as size
FROM (
    SELECT 'profiles' as name UNION
    SELECT 'current_followers' UNION
    SELECT 'follower_changes'
) tables;

-- Optimize queries
EXPLAIN QUERY PLAN SELECT * FROM follower_changes 
WHERE profile_id = 1 ORDER BY timestamp DESC LIMIT 100;

-- Database maintenance
PRAGMA optimize;
PRAGMA integrity_check;
```

## Database Issues

### Database Corruption

**Problem**: Database file is corrupted or inaccessible

**Diagnostic Steps**:
```bash
# Check database integrity
sqlite3 instagram_monitor.db "PRAGMA integrity_check;"

# Check database file
file instagram_monitor.db
ls -la instagram_monitor.db

# Test basic operations
sqlite3 instagram_monitor.db ".tables"
```

**Recovery Steps**:
```bash
# Backup current database
cp instagram_monitor.db instagram_monitor.db.backup

# Try to repair
sqlite3 instagram_monitor.db "
.recover
.backup main recovered.db
"

# If repair fails, restore from backup
cp backups/latest_backup.db instagram_monitor.db

# Reinitialize if no backup available
rm instagram_monitor.db
python init_database.py
```

### Migration Issues

**Problem**: Database schema updates fail

**Solutions**:
```bash
# Check current schema version
sqlite3 instagram_monitor.db "
SELECT value FROM settings WHERE key = 'schema_version';
"

# Manual migration
python3 -c "
from database.connection import DatabaseManager
db = DatabaseManager()
db.migrate_database()
"

# Backup before migration
cp instagram_monitor.db pre_migration_backup.db
```

### Lock Issues

**Problem**: Database locked errors

**Solutions**:
```bash
# Check for long-running processes
ps aux | grep sqlite
lsof instagram_monitor.db

# Kill blocking processes (carefully)
sudo kill -9 <process_id>

# Check database mode
sqlite3 instagram_monitor.db "PRAGMA journal_mode;"

# Set WAL mode for better concurrency
sqlite3 instagram_monitor.db "PRAGMA journal_mode=WAL;"
```

## Network and Connectivity

### Instagram API Connectivity

**Problem**: Cannot connect to Instagram

**Diagnostic Steps**:
```bash
# Test basic connectivity
ping instagram.com
curl -I https://www.instagram.com

# Test with user agent
curl -H "User-Agent: Mozilla/5.0 (compatible)" https://www.instagram.com

# Check DNS resolution
nslookup instagram.com
dig instagram.com
```

**Solutions**:
1. **Network Issues**:
   - Check firewall settings
   - Verify proxy configuration
   - Test with different DNS servers
   - Check for network restrictions

2. **IP Blocking**:
   - Use VPN or proxy
   - Rotate IP addresses
   - Reduce request frequency
   - Contact ISP about restrictions

### Rate Limiting

**Problem**: Getting 429 (Too Many Requests) errors

**Solutions**:
```python
# Adjust rate limiting settings
RATE_LIMIT_DELAY = 5  # Increase delay between requests
MAX_RETRIES = 3       # Reduce retry attempts
BACKOFF_FACTOR = 2    # Increase backoff multiplier

# Monitor rate limit headers
import requests
response = requests.get(url)
print('Rate limit remaining:', response.headers.get('X-RateLimit-Remaining'))
print('Rate limit reset:', response.headers.get('X-RateLimit-Reset'))
```

### SSL/TLS Issues

**Problem**: SSL certificate errors

**Solutions**:
```bash
# Check SSL certificate
openssl s_client -connect instagram.com:443

# Update CA certificates
sudo apt update && sudo apt install ca-certificates

# Python SSL debugging
python3 -c "
import ssl
import requests
print('SSL version:', ssl.OPENSSL_VERSION)
response = requests.get('https://instagram.com', verify=True)
print('SSL OK')
"
```

## Instagram API Issues

### Common Instagram Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| 400 | Bad Request | Check request format and parameters |
| 401 | Unauthorized | Update authentication credentials |
| 403 | Forbidden | Check account permissions and restrictions |
| 404 | Not Found | Verify profile exists and is accessible |
| 429 | Rate Limited | Reduce request frequency, implement backoff |
| 500 | Server Error | Retry later, check Instagram status |

### Profile Access Issues

**Problem**: Cannot access specific profiles

**Diagnostic Steps**:
```python
# Test profile accessibility
from services.instagram_client import InstagramClient
client = InstagramClient()

try:
    profile = client.get_profile_info('username')
    print('Profile accessible:', profile.username)
except Exception as e:
    print('Error:', str(e))
```

**Solutions**:
1. **Private Profiles**:
   - Ensure you're authenticated
   - Verify you follow the private profile
   - Check if profile owner has blocked you

2. **Deleted/Suspended Profiles**:
   - Verify profile still exists
   - Check for username changes
   - Remove from monitoring if permanently unavailable

### Anti-Bot Detection

**Problem**: Instagram detecting automated behavior

**Solutions**:
1. **Reduce Automation Signatures**:
   - Randomize request timing
   - Vary user agents
   - Implement human-like delays
   - Rotate session cookies

2. **Behavioral Patterns**:
   - Limit requests per hour
   - Avoid predictable patterns
   - Implement random breaks
   - Monitor for detection signals

## Web Interface Problems

### Page Loading Issues

**Problem**: Web pages not loading or showing errors

**Diagnostic Steps**:
```bash
# Check web server status
curl -I http://localhost:8000

# Check Flask application logs
tail -f logs/web.log

# Test specific routes
curl http://localhost:8000/api/health
curl http://localhost:8000/api/profiles
```

**Solutions**:
1. **Server Configuration**:
   - Check Gunicorn/Flask configuration
   - Verify port bindings
   - Check firewall rules
   - Review proxy settings

2. **Static Files**:
   - Verify static file paths
   - Check file permissions
   - Clear browser cache
   - Test with different browsers

### JavaScript Errors

**Problem**: Dashboard features not working

**Diagnostic Steps**:
1. Open browser developer tools (F12)
2. Check Console tab for JavaScript errors
3. Check Network tab for failed requests
4. Verify API endpoints are responding

**Solutions**:
```javascript
// Common fixes for JavaScript issues

// Check jQuery loading
if (typeof jQuery === 'undefined') {
    console.error('jQuery not loaded');
}

// Check API connectivity
fetch('/api/health')
    .then(response => response.json())
    .then(data => console.log('API OK:', data))
    .catch(error => console.error('API Error:', error));

// Clear local storage
localStorage.clear();
sessionStorage.clear();
```

### Authentication Issues

**Problem**: Cannot log into web interface

**Solutions**:
1. **Session Issues**:
   - Clear browser cookies
   - Check session configuration
   - Verify secret key is set
   - Check session timeout settings

2. **CSRF Protection**:
   - Ensure CSRF tokens are included
   - Check token expiration
   - Verify form submission method
   - Clear browser cache

## Log Analysis

### Understanding Log Levels

| Level | Purpose | When to Check |
|-------|---------|---------------|
| DEBUG | Detailed debugging info | Development and troubleshooting |
| INFO | General information | Normal operation monitoring |
| WARNING | Potential issues | When things seem off |
| ERROR | Error conditions | When features aren't working |
| CRITICAL | Serious failures | When system is down |

### Key Log Files

```bash
# Application logs
tail -f logs/application.log

# Web server logs
tail -f logs/web.log

# Instagram client logs
tail -f logs/instagram_client.log

# Scheduler logs
tail -f logs/scheduler.log

# Database logs
tail -f logs/database.log

# Error logs (all errors)
tail -f logs/errors.log
```

### Log Analysis Commands

```bash
# Find errors in last hour
grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')" logs/application.log | grep ERROR

# Count error types
grep ERROR logs/application.log | cut -d' ' -f4- | sort | uniq -c | sort -nr

# Monitor real-time errors
tail -f logs/application.log | grep --color=always ERROR

# Find authentication failures
grep -i "auth" logs/application.log | grep -i "fail\|error"

# Check rate limiting
grep "429\|rate.limit" logs/instagram_client.log

# Database errors
grep -i "database\|sqlite" logs/errors.log
```

### Log Rotation Issues

**Problem**: Log files growing too large

**Solutions**:
```bash
# Manual log rotation
logrotate -f /etc/logrotate.d/instagram-monitor

# Check log rotation configuration
cat /etc/logrotate.d/instagram-monitor

# Compress old logs
gzip logs/*.log.1

# Clean old logs
find logs/ -name "*.log.*" -mtime +30 -delete
```

## Recovery Procedures

### Complete System Recovery

**When to Use**: System completely non-functional

**Steps**:
1. **Stop all services**:
   ```bash
   sudo systemctl stop instagram-monitor
   sudo systemctl stop nginx
   ```

2. **Backup current state**:
   ```bash
   cp -r /var/www/instagram-monitor /var/www/instagram-monitor.backup
   ```

3. **Restore from backup**:
   ```bash
   # Restore database
   cp backups/latest_backup.db instagram_monitor.db
   
   # Restore configuration
   cp backups/.env.backup .env
   ```

4. **Reinitialize if needed**:
   ```bash
   python init_database.py
   ```

5. **Restart services**:
   ```bash
   sudo systemctl start instagram-monitor
   sudo systemctl start nginx
   ```

### Partial Recovery

**Database Only**:
```bash
# Stop application
sudo systemctl stop instagram-monitor

# Restore database
cp backups/database_backup_YYYYMMDD.db instagram_monitor.db

# Restart application
sudo systemctl start instagram-monitor
```

**Configuration Only**:
```bash
# Restore configuration
cp backups/.env.backup .env

# Restart to reload configuration
sudo systemctl restart instagram-monitor
```

### Emergency Procedures

**System Unresponsive**:
1. Check system resources: `top`, `df -h`, `free -h`
2. Kill runaway processes: `pkill -f instagram-monitor`
3. Restart services: `sudo systemctl restart instagram-monitor`
4. Check logs for root cause

**Data Corruption**:
1. Stop all write operations
2. Backup current state (even if corrupted)
3. Restore from last known good backup
4. Verify data integrity
5. Resume operations

**Security Breach**:
1. Immediately change all passwords
2. Revoke API keys and tokens
3. Check logs for unauthorized access
4. Update security configurations
5. Monitor for continued threats

## Getting Additional Help

### Before Contacting Support

1. **Gather Information**:
   - System specifications
   - Error messages (exact text)
   - Log file excerpts
   - Steps to reproduce the issue
   - Recent changes made

2. **Try Basic Fixes**:
   - Restart the application
   - Check system resources
   - Review recent logs
   - Verify configuration

3. **Document the Issue**:
   - When did it start?
   - What changed recently?
   - Is it affecting all profiles or specific ones?
   - Are there any patterns?

### Support Channels

- **GitHub Issues**: For bugs and feature requests
- **Documentation**: Check FAQ and user guide
- **Community Forum**: For general questions
- **Email Support**: For urgent production issues

### Information to Include

When reporting issues, include:
- Application version
- Operating system and version
- Python version
- Error messages and stack traces
- Relevant log file excerpts
- Configuration details (sanitized)
- Steps to reproduce the issue