"""Unit tests for database operations."""

import pytest
import os
import tempfile
from datetime import datetime, timedelta
from models.data_models import (
    ProfileInfo, FollowerChange, MonitoringConfig, ChangeType
)
from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, FollowerRepository, ChangeRepository, DataRetentionManager
)


@pytest.fixture
def temp_db():
    """Create a temporary database for testing."""
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_file.close()
    
    db_manager = DatabaseManager(temp_file.name)
    db_manager.migrate_schema()
    
    yield db_manager
    
    # Cleanup
    os.unlink(temp_file.name)


@pytest.fixture
def profile_repo(temp_db, monkeypatch):
    """Create ProfileRepository with temporary database."""
    # Patch the global db_manager to use our test database
    monkeypatch.setattr('database.repositories.db_manager', temp_db)
    return ProfileRepository()


@pytest.fixture
def follower_repo(temp_db, monkeypatch):
    """Create FollowerRepository with temporary database."""
    # Patch the global db_manager to use our test database
    monkeypatch.setattr('database.repositories.db_manager', temp_db)
    return FollowerRepository()


@pytest.fixture
def change_repo(temp_db, monkeypatch):
    """Create ChangeRepository with temporary database."""
    # Patch the global db_manager to use our test database
    monkeypatch.setattr('database.repositories.db_manager', temp_db)
    return ChangeRepository()


@pytest.fixture
def retention_manager(temp_db, monkeypatch):
    """Create DataRetentionManager with temporary database."""
    # Patch the global db_manager to use our test database
    monkeypatch.setattr('database.repositories.db_manager', temp_db)
    return DataRetentionManager(retention_days=30)


class TestDatabaseManager:
    """Test cases for DatabaseManager."""
    
    def test_database_creation(self):
        """Test database file creation."""
        # Create a unique temporary path
        temp_dir = tempfile.gettempdir()
        temp_path = os.path.join(temp_dir, f"test_db_{os.getpid()}_{id(self)}.db")
        
        # Ensure file doesn't exist
        if os.path.exists(temp_path):
            os.unlink(temp_path)
        
        db_manager = DatabaseManager(temp_path)
        assert os.path.exists(temp_path)
        
        # Cleanup - close any connections first
        del db_manager
        if os.path.exists(temp_path):
            try:
                os.unlink(temp_path)
            except PermissionError:
                # File might still be locked on Windows, that's okay for this test
                pass
    
    def test_schema_initialization(self, temp_db):
        """Test schema initialization."""
        # Schema should be initialized by fixture
        version = temp_db.get_schema_version()
        assert version == 1
    
    def test_connection_context_manager(self, temp_db):
        """Test database connection context manager."""
        with temp_db.get_connection() as conn:
            cursor = conn.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
    
    def test_execute_query(self, temp_db):
        """Test execute_query method."""
        # Insert test data
        temp_db.execute_update(
            "INSERT INTO settings (key, value) VALUES (?, ?)",
            ("test_key", "test_value")
        )
        
        # Query data
        results = temp_db.execute_query(
            "SELECT key, value FROM settings WHERE key = ?",
            ("test_key",)
        )
        
        assert len(results) == 1
        assert results[0]['key'] == "test_key"
        assert results[0]['value'] == "test_value"
    
    def test_execute_update(self, temp_db):
        """Test execute_update method."""
        affected_rows = temp_db.execute_update(
            "INSERT INTO settings (key, value) VALUES (?, ?)",
            ("test_key", "test_value")
        )
        
        assert affected_rows == 1


class TestProfileRepository:
    """Test cases for ProfileRepository."""
    
    def test_create_profile(self, profile_repo):
        """Test creating a new profile."""
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True,
            is_private=False
        )
        
        profile_id = profile_repo.create_profile(config)
        assert profile_id is not None
        assert profile_id > 0
    
    def test_get_profile_by_username(self, profile_repo):
        """Test retrieving profile by username."""
        # Create profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Retrieve profile
        retrieved_config = profile_repo.get_profile_by_username("testuser")
        
        assert retrieved_config is not None
        assert retrieved_config.profile_username == "testuser"
        assert retrieved_config.display_name == "Test User"
        assert retrieved_config.enabled is True
        assert retrieved_config.profile_id == profile_id
    
    def test_get_profile_by_username_not_found(self, profile_repo):
        """Test retrieving non-existent profile returns None."""
        result = profile_repo.get_profile_by_username("nonexistent")
        assert result is None
    
    def test_get_all_profiles(self, profile_repo):
        """Test retrieving all profiles."""
        # Create multiple profiles
        configs = [
            MonitoringConfig(profile_username="user1", display_name="User 1"),
            MonitoringConfig(profile_username="user2", display_name="User 2"),
            MonitoringConfig(profile_username="user3", display_name="User 3")
        ]
        
        for config in configs:
            profile_repo.create_profile(config)
        
        # Retrieve all profiles
        all_profiles = profile_repo.get_all_profiles()
        
        assert len(all_profiles) == 3
        usernames = {profile.profile_username for profile in all_profiles}
        assert usernames == {"user1", "user2", "user3"}
    
    def test_get_enabled_profiles(self, profile_repo):
        """Test retrieving only enabled profiles."""
        # Create profiles with different enabled states
        enabled_config = MonitoringConfig(profile_username="enabled_user", enabled=True)
        disabled_config = MonitoringConfig(profile_username="disabled_user", enabled=False)
        
        profile_repo.create_profile(enabled_config)
        profile_repo.create_profile(disabled_config)
        
        # Retrieve enabled profiles
        enabled_profiles = profile_repo.get_enabled_profiles()
        
        assert len(enabled_profiles) == 1
        assert enabled_profiles[0].profile_username == "enabled_user"
        assert enabled_profiles[0].enabled is True
    
    def test_update_profile(self, profile_repo):
        """Test updating profile configuration."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser", display_name="Original Name")
        profile_id = profile_repo.create_profile(config)
        
        # Update profile
        config.profile_id = profile_id
        config.display_name = "Updated Name"
        config.enabled = False
        config.last_scan = datetime.now()
        
        success = profile_repo.update_profile(config)
        assert success is True
        
        # Verify update
        updated_config = profile_repo.get_profile_by_username("testuser")
        assert updated_config.display_name == "Updated Name"
        assert updated_config.enabled is False
        assert updated_config.last_scan is not None
    
    def test_delete_profile(self, profile_repo):
        """Test deleting a profile."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_repo.create_profile(config)
        
        # Delete profile
        success = profile_repo.delete_profile("testuser")
        assert success is True
        
        # Verify deletion
        deleted_profile = profile_repo.get_profile_by_username("testuser")
        assert deleted_profile is None


class TestFollowerRepository:
    """Test cases for FollowerRepository."""
    
    def test_store_and_get_current_followers(self, profile_repo, follower_repo):
        """Test storing and retrieving current followers."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Store followers
        followers = {"follower1", "follower2", "follower3"}
        follower_repo.store_current_followers(profile_id, followers)
        
        # Retrieve followers
        retrieved_followers = follower_repo.get_current_followers(profile_id)
        
        assert retrieved_followers == followers
    
    def test_store_and_get_current_following(self, profile_repo, follower_repo):
        """Test storing and retrieving current following."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Store following
        following = {"following1", "following2", "following3"}
        follower_repo.store_current_following(profile_id, following)
        
        # Retrieve following
        retrieved_following = follower_repo.get_current_following(profile_id)
        
        assert retrieved_following == following
    
    def test_replace_current_followers(self, profile_repo, follower_repo):
        """Test that storing followers replaces existing data."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Store initial followers
        initial_followers = {"follower1", "follower2"}
        follower_repo.store_current_followers(profile_id, initial_followers)
        
        # Store new followers (should replace)
        new_followers = {"follower3", "follower4"}
        follower_repo.store_current_followers(profile_id, new_followers)
        
        # Verify replacement
        retrieved_followers = follower_repo.get_current_followers(profile_id)
        assert retrieved_followers == new_followers


class TestChangeRepository:
    """Test cases for ChangeRepository."""
    
    def test_store_follower_changes(self, profile_repo, change_repo):
        """Test storing follower changes."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Create changes
        changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="newfollower",
                change_type=ChangeType.GAINED,
                profile_id=profile_id
            ),
            FollowerChange(
                profile_username="testuser",
                affected_username="lostfollower",
                change_type=ChangeType.LOST,
                profile_id=profile_id
            )
        ]
        
        # Store changes
        change_repo.store_follower_changes(changes)
        
        # Retrieve changes
        retrieved_changes = change_repo.get_recent_changes(profile_id=profile_id)
        
        assert len(retrieved_changes) == 2
        change_types = {change.change_type for change in retrieved_changes}
        assert ChangeType.GAINED in change_types
        assert ChangeType.LOST in change_types
    
    def test_store_following_changes(self, profile_repo, change_repo):
        """Test storing following changes."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Create changes
        changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="newfollowing",
                change_type=ChangeType.STARTED_FOLLOWING,
                profile_id=profile_id
            ),
            FollowerChange(
                profile_username="testuser",
                affected_username="stoppedfollowing",
                change_type=ChangeType.STOPPED_FOLLOWING,
                profile_id=profile_id
            )
        ]
        
        # Store changes
        change_repo.store_follower_changes(changes)
        
        # Retrieve changes
        retrieved_changes = change_repo.get_recent_changes(profile_id=profile_id)
        
        assert len(retrieved_changes) == 2
        change_types = {change.change_type for change in retrieved_changes}
        assert ChangeType.STARTED_FOLLOWING in change_types
        assert ChangeType.STOPPED_FOLLOWING in change_types
    
    def test_get_recent_changes_limit(self, profile_repo, change_repo):
        """Test limiting recent changes results."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Create many changes
        changes = []
        for i in range(10):
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=f"user{i}",
                change_type=ChangeType.GAINED,
                profile_id=profile_id
            ))
        
        change_repo.store_follower_changes(changes)
        
        # Retrieve with limit
        retrieved_changes = change_repo.get_recent_changes(profile_id=profile_id, limit=5)
        
        assert len(retrieved_changes) == 5
    
    def test_get_changes_by_date_range(self, profile_repo, change_repo):
        """Test retrieving changes by date range."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Create changes with different timestamps
        now = datetime.now()
        old_change = FollowerChange(
            profile_username="testuser",
            affected_username="olduser",
            change_type=ChangeType.GAINED,
            timestamp=now - timedelta(days=5),
            profile_id=profile_id
        )
        recent_change = FollowerChange(
            profile_username="testuser",
            affected_username="recentuser",
            change_type=ChangeType.GAINED,
            timestamp=now - timedelta(hours=1),
            profile_id=profile_id
        )
        
        change_repo.store_follower_changes([old_change, recent_change])
        
        # Query recent changes only
        start_date = now - timedelta(days=1)
        end_date = now
        
        recent_changes = change_repo.get_changes_by_date_range(start_date, end_date, profile_id)
        
        assert len(recent_changes) == 1
        assert recent_changes[0].affected_username == "recentuser"


class TestDataRetentionManager:
    """Test cases for DataRetentionManager."""
    
    def test_cleanup_old_data(self, profile_repo, change_repo, retention_manager):
        """Test cleaning up old data."""
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Create old and recent changes
        now = datetime.now()
        old_change = FollowerChange(
            profile_username="testuser",
            affected_username="olduser",
            change_type=ChangeType.GAINED,
            timestamp=now - timedelta(days=40),  # Older than retention period
            profile_id=profile_id
        )
        recent_change = FollowerChange(
            profile_username="testuser",
            affected_username="recentuser",
            change_type=ChangeType.GAINED,
            timestamp=now - timedelta(days=10),  # Within retention period
            profile_id=profile_id
        )
        
        change_repo.store_follower_changes([old_change, recent_change])
        
        # Run cleanup
        stats = retention_manager.cleanup_old_data()
        
        # Verify old data was removed
        remaining_changes = change_repo.get_recent_changes(profile_id=profile_id)
        assert len(remaining_changes) == 1
        assert remaining_changes[0].affected_username == "recentuser"
        
        # Verify stats
        assert stats['follower_changes_deleted'] == 1
    
    def test_get_data_size_stats(self, profile_repo, retention_manager):
        """Test getting data size statistics."""
        # Create some test data
        config = MonitoringConfig(profile_username="testuser")
        profile_repo.create_profile(config)
        
        # Get stats
        stats = retention_manager.get_data_size_stats()
        
        # Verify stats structure
        expected_keys = [
            'profiles_count', 'current_followers_count', 'current_following_count',
            'follower_changes_count', 'following_changes_count', 'settings_count'
        ]
        
        for key in expected_keys:
            assert key in stats
            assert isinstance(stats[key], int)
        
        # Verify we have at least one profile
        assert stats['profiles_count'] >= 1