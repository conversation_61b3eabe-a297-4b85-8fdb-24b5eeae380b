"""
API Documentation and Response Schemas for Instagram Follower Monitor REST API.

This module provides comprehensive documentation for all API endpoints,
including request/response schemas, authentication requirements, and usage examples.
"""

from typing import Dict, Any, List
from datetime import datetime

# API Documentation Structure
API_DOCUMENTATION = {
    "info": {
        "title": "Instagram Follower Monitor API",
        "version": "1.0.0",
        "description": "REST API for managing Instagram follower monitoring, retrieving change data, and system status",
        "base_url": "/api/v1"
    },
    "authentication": {
        "type": "API Key",
        "header": "X-API-Key",
        "description": "API key required for write operations (POST, PUT, DELETE). Read operations have rate limiting only."
    },
    "rate_limiting": {
        "read_operations": "100 requests per hour",
        "write_operations": "20 requests per hour",
        "search_operations": "50 requests per hour"
    },
    "endpoints": {
        "profiles": {
            "GET /profiles": {
                "description": "Get all profiles with optional filtering and pagination",
                "parameters": {
                    "enabled_only": {"type": "boolean", "default": False, "description": "Filter to enabled profiles only"},
                    "include_stats": {"type": "boolean", "default": False, "description": "Include profile statistics"},
                    "page": {"type": "integer", "default": 1, "description": "Page number for pagination"},
                    "per_page": {"type": "integer", "default": 20, "max": 100, "description": "Items per page"}
                },
                "response_schema": "ProfileListResponse",
                "rate_limit": "50 requests/hour"
            },
            "GET /profiles/{username}": {
                "description": "Get detailed information for a specific profile",
                "parameters": {
                    "username": {"type": "string", "required": True, "description": "Instagram username"}
                },
                "response_schema": "ProfileDetailResponse",
                "rate_limit": "100 requests/hour"
            },
            "POST /profiles": {
                "description": "Create a new profile for monitoring",
                "authentication": "required",
                "request_schema": "CreateProfileRequest",
                "response_schema": "ProfileResponse",
                "rate_limit": "10 requests/hour"
            },
            "PUT /profiles/{username}": {
                "description": "Update profile configuration",
                "authentication": "required",
                "parameters": {
                    "username": {"type": "string", "required": True, "description": "Instagram username"}
                },
                "request_schema": "UpdateProfileRequest",
                "response_schema": "ProfileResponse",
                "rate_limit": "20 requests/hour"
            },
            "DELETE /profiles/{username}": {
                "description": "Delete profile and all associated data",
                "authentication": "required",
                "parameters": {
                    "username": {"type": "string", "required": True, "description": "Instagram username"}
                },
                "response_schema": "MessageResponse",
                "rate_limit": "10 requests/hour"
            }
        },
        "changes": {
            "GET /changes": {
                "description": "Get follower/following changes with advanced filtering and pagination",
                "parameters": {
                    "profile": {"type": "string", "description": "Filter by profile username"},
                    "type": {"type": "string", "enum": ["gained", "lost", "started_following", "stopped_following"], "description": "Filter by change type"},
                    "search": {"type": "string", "description": "Search in affected usernames"},
                    "days": {"type": "integer", "default": 7, "description": "Number of days to look back"},
                    "page": {"type": "integer", "default": 1, "description": "Page number for pagination"},
                    "per_page": {"type": "integer", "default": 20, "max": 100, "description": "Items per page"},
                    "sort_by": {"type": "string", "enum": ["timestamp", "username", "profile"], "default": "timestamp", "description": "Sort field"},
                    "sort_order": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "description": "Sort order"}
                },
                "response_schema": "ChangeListResponse",
                "rate_limit": "100 requests/hour"
            },
            "GET /changes/statistics": {
                "description": "Get change statistics for specified period and profile",
                "parameters": {
                    "profile": {"type": "string", "description": "Filter by profile username"},
                    "days": {"type": "integer", "default": 30, "description": "Number of days for statistics"}
                },
                "response_schema": "ChangeStatisticsResponse",
                "rate_limit": "50 requests/hour"
            }
        },
        "monitoring": {
            "GET /monitoring/status": {
                "description": "Get comprehensive monitoring system status",
                "response_schema": "MonitoringStatusResponse",
                "rate_limit": "100 requests/hour"
            },
            "GET /monitoring/jobs": {
                "description": "Get current monitoring job status",
                "response_schema": "JobStatusResponse",
                "rate_limit": "50 requests/hour"
            }
        },
        "dashboard": {
            "GET /dashboard/summary": {
                "description": "Get dashboard summary data for real-time updates",
                "response_schema": "DashboardSummaryResponse",
                "rate_limit": "100 requests/hour"
            }
        },
        "followers": {
            "GET /profiles/{username}/followers": {
                "description": "Get current followers list for a profile",
                "parameters": {
                    "username": {"type": "string", "required": True, "description": "Instagram username"},
                    "page": {"type": "integer", "default": 1, "description": "Page number for pagination"},
                    "per_page": {"type": "integer", "default": 50, "max": 200, "description": "Items per page"}
                },
                "response_schema": "FollowerListResponse",
                "rate_limit": "50 requests/hour"
            },
            "GET /profiles/{username}/following": {
                "description": "Get current following list for a profile",
                "parameters": {
                    "username": {"type": "string", "required": True, "description": "Instagram username"},
                    "page": {"type": "integer", "default": 1, "description": "Page number for pagination"},
                    "per_page": {"type": "integer", "default": 50, "max": 200, "description": "Items per page"}
                },
                "response_schema": "FollowingListResponse",
                "rate_limit": "50 requests/hour"
            }
        },
        "settings": {
            "GET /settings": {
                "description": "Get current system settings",
                "authentication": "required",
                "response_schema": "SettingsResponse",
                "rate_limit": "50 requests/hour"
            },
            "PUT /settings": {
                "description": "Update system settings",
                "authentication": "required",
                "request_schema": "UpdateSettingsRequest",
                "response_schema": "MessageResponse",
                "rate_limit": "10 requests/hour"
            }
        }
    }
}

# Response Schemas
RESPONSE_SCHEMAS = {
    "ProfileListResponse": {
        "type": "object",
        "properties": {
            "profiles": {
                "type": "array",
                "items": {"$ref": "#/schemas/Profile"}
            },
            "pagination": {"$ref": "#/schemas/Pagination"}
        }
    },
    "ProfileDetailResponse": {
        "type": "object",
        "properties": {
            "id": {"type": "integer"},
            "username": {"type": "string"},
            "display_name": {"type": "string", "nullable": True},
            "is_private": {"type": "boolean"},
            "monitoring_enabled": {"type": "boolean"},
            "last_scan": {"type": "string", "format": "date-time", "nullable": True},
            "created_at": {"type": "string", "format": "date-time", "nullable": True},
            "is_due_for_scan": {"type": "boolean"},
            "stats": {"$ref": "#/schemas/ProfileStats"},
            "recent_changes": {
                "type": "array",
                "items": {"$ref": "#/schemas/Change"}
            }
        }
    },
    "ProfileResponse": {
        "type": "object",
        "properties": {
            "id": {"type": "integer"},
            "username": {"type": "string"},
            "display_name": {"type": "string", "nullable": True},
            "is_private": {"type": "boolean"},
            "monitoring_enabled": {"type": "boolean"},
            "created_at": {"type": "string", "format": "date-time"},
            "message": {"type": "string"}
        }
    },
    "ChangeListResponse": {
        "type": "object",
        "properties": {
            "changes": {
                "type": "array",
                "items": {"$ref": "#/schemas/Change"}
            },
            "pagination": {"$ref": "#/schemas/Pagination"},
            "filters": {"$ref": "#/schemas/ChangeFilters"}
        }
    },
    "ChangeStatisticsResponse": {
        "type": "object",
        "properties": {
            "period_days": {"type": "integer"},
            "profile": {"type": "string", "nullable": True},
            "statistics": {"$ref": "#/schemas/ChangeStatistics"},
            "generated_at": {"type": "string", "format": "date-time"}
        }
    },
    "MonitoringStatusResponse": {
        "type": "object",
        "properties": {
            "system_status": {"$ref": "#/schemas/SystemStatus"},
            "profile_status": {
                "type": "object",
                "additionalProperties": {"$ref": "#/schemas/ProfileStatus"}
            },
            "generated_at": {"type": "string", "format": "date-time"}
        }
    },
    "JobStatusResponse": {
        "type": "object",
        "properties": {
            "scheduler_running": {"type": "boolean"},
            "jobs": {
                "type": "array",
                "items": {"$ref": "#/schemas/Job"}
            },
            "job_count": {"type": "integer"},
            "error": {"type": "string", "nullable": True}
        }
    },
    "DashboardSummaryResponse": {
        "type": "object",
        "properties": {
            "summary": {"$ref": "#/schemas/DashboardSummary"},
            "recent_changes": {
                "type": "array",
                "items": {"$ref": "#/schemas/Change"}
            },
            "last_updated": {"type": "string", "format": "date-time"}
        }
    },
    "FollowerListResponse": {
        "type": "object",
        "properties": {
            "profile": {"type": "string"},
            "followers": {
                "type": "array",
                "items": {"type": "string"}
            },
            "pagination": {"$ref": "#/schemas/Pagination"}
        }
    },
    "FollowingListResponse": {
        "type": "object",
        "properties": {
            "profile": {"type": "string"},
            "following": {
                "type": "array",
                "items": {"type": "string"}
            },
            "pagination": {"$ref": "#/schemas/Pagination"}
        }
    },
    "SettingsResponse": {
        "type": "object",
        "properties": {
            "monitoring_settings": {"$ref": "#/schemas/MonitoringSettings"},
            "retrieved_at": {"type": "string", "format": "date-time"}
        }
    },
    "MessageResponse": {
        "type": "object",
        "properties": {
            "message": {"type": "string"}
        }
    },
    "ErrorResponse": {
        "type": "object",
        "properties": {
            "error": {"type": "string"},
            "message": {"type": "string", "nullable": True}
        }
    }
}

# Request Schemas
REQUEST_SCHEMAS = {
    "CreateProfileRequest": {
        "type": "object",
        "required": ["username"],
        "properties": {
            "username": {"type": "string", "description": "Instagram username (without @)"},
            "display_name": {"type": "string", "nullable": True, "description": "Display name for the profile"},
            "is_private": {"type": "boolean", "default": False, "description": "Whether the profile is private"},
            "enabled": {"type": "boolean", "default": True, "description": "Whether monitoring is enabled"},
            "interval_hours": {"type": "integer", "default": 2, "minimum": 1, "description": "Monitoring interval in hours"}
        }
    },
    "UpdateProfileRequest": {
        "type": "object",
        "properties": {
            "display_name": {"type": "string", "nullable": True, "description": "Display name for the profile"},
            "is_private": {"type": "boolean", "description": "Whether the profile is private"},
            "enabled": {"type": "boolean", "description": "Whether monitoring is enabled"},
            "interval_hours": {"type": "integer", "minimum": 1, "description": "Monitoring interval in hours"}
        }
    },
    "UpdateSettingsRequest": {
        "type": "object",
        "properties": {
            "monitoring_interval_hours": {"type": "integer", "minimum": 1, "description": "Default monitoring interval"},
            "data_retention_days": {"type": "integer", "minimum": 1, "description": "Data retention period"},
            "min_request_delay": {"type": "number", "minimum": 0, "description": "Minimum delay between requests"},
            "max_request_delay": {"type": "number", "minimum": 0, "description": "Maximum delay between requests"},
            "max_retries": {"type": "integer", "minimum": 0, "description": "Maximum retry attempts"},
            "profile_processing_delay": {"type": "number", "minimum": 0, "description": "Delay between profile processing"}
        }
    }
}

# Data Model Schemas
DATA_SCHEMAS = {
    "Profile": {
        "type": "object",
        "properties": {
            "id": {"type": "integer"},
            "username": {"type": "string"},
            "display_name": {"type": "string", "nullable": True},
            "is_private": {"type": "boolean"},
            "monitoring_enabled": {"type": "boolean"},
            "last_scan": {"type": "string", "format": "date-time", "nullable": True},
            "created_at": {"type": "string", "format": "date-time", "nullable": True},
            "is_due_for_scan": {"type": "boolean"},
            "stats": {"$ref": "#/schemas/ProfileStats", "nullable": True}
        }
    },
    "ProfileStats": {
        "type": "object",
        "properties": {
            "current_followers": {"type": "integer"},
            "current_following": {"type": "integer"},
            "followers_gained_30d": {"type": "integer"},
            "followers_lost_30d": {"type": "integer"},
            "following_started_30d": {"type": "integer"},
            "following_stopped_30d": {"type": "integer"}
        }
    },
    "Change": {
        "type": "object",
        "properties": {
            "id": {"type": "string"},
            "profile_username": {"type": "string"},
            "affected_username": {"type": "string"},
            "change_type": {"type": "string", "enum": ["gained", "lost", "started_following", "stopped_following"]},
            "timestamp": {"type": "string", "format": "date-time"},
            "is_follower_change": {"type": "boolean"},
            "is_following_change": {"type": "boolean"},
            "display_text": {"type": "string"}
        }
    },
    "ChangeStatistics": {
        "type": "object",
        "properties": {
            "followers_gained": {"type": "integer"},
            "followers_lost": {"type": "integer"},
            "following_started": {"type": "integer"},
            "following_stopped": {"type": "integer"}
        }
    },
    "ChangeFilters": {
        "type": "object",
        "properties": {
            "profile": {"type": "string", "nullable": True},
            "type": {"type": "string", "nullable": True},
            "search": {"type": "string"},
            "days": {"type": "integer"},
            "sort_by": {"type": "string"},
            "sort_order": {"type": "string"}
        }
    },
    "SystemStatus": {
        "type": "object",
        "properties": {
            "total_profiles": {"type": "integer"},
            "enabled_profiles": {"type": "integer"},
            "profiles_due_for_scan": {"type": "integer"},
            "profiles_with_recent_scans": {"type": "integer"},
            "authentication_configured": {"type": "boolean"}
        }
    },
    "ProfileStatus": {
        "type": "object",
        "properties": {
            "last_scan": {"type": "string", "format": "date-time", "nullable": True},
            "hours_ago": {"type": "number", "nullable": True},
            "is_due": {"type": "boolean"}
        }
    },
    "Job": {
        "type": "object",
        "properties": {
            "id": {"type": "string"},
            "name": {"type": "string"},
            "next_run_time": {"type": "string", "format": "date-time", "nullable": True},
            "trigger": {"type": "string"},
            "func_name": {"type": "string", "nullable": True}
        }
    },
    "DashboardSummary": {
        "type": "object",
        "properties": {
            "total_profiles": {"type": "integer"},
            "enabled_profiles": {"type": "integer"},
            "total_followers": {"type": "integer"},
            "total_following": {"type": "integer"},
            "changes_today": {"type": "integer"}
        }
    },
    "MonitoringSettings": {
        "type": "object",
        "properties": {
            "monitoring_interval_hours": {"type": "integer"},
            "data_retention_days": {"type": "integer"},
            "min_request_delay": {"type": "number"},
            "max_request_delay": {"type": "number"},
            "max_retries": {"type": "integer"},
            "profile_processing_delay": {"type": "number"}
        }
    },
    "Pagination": {
        "type": "object",
        "properties": {
            "page": {"type": "integer"},
            "per_page": {"type": "integer"},
            "total_count": {"type": "integer"},
            "total_pages": {"type": "integer"},
            "has_next": {"type": "boolean"},
            "has_prev": {"type": "boolean"}
        }
    }
}

# Usage Examples
USAGE_EXAMPLES = {
    "authentication": {
        "description": "API key authentication for write operations",
        "example": {
            "headers": {
                "X-API-Key": "your-api-key-here",
                "Content-Type": "application/json"
            }
        }
    },
    "create_profile": {
        "description": "Create a new profile for monitoring",
        "request": {
            "method": "POST",
            "url": "/api/v1/profiles",
            "headers": {
                "X-API-Key": "your-api-key-here",
                "Content-Type": "application/json"
            },
            "body": {
                "username": "example_user",
                "display_name": "Example User",
                "is_private": False,
                "enabled": True,
                "interval_hours": 2
            }
        },
        "response": {
            "status": 201,
            "body": {
                "id": 1,
                "username": "example_user",
                "display_name": "Example User",
                "is_private": False,
                "monitoring_enabled": True,
                "created_at": "2024-01-01T12:00:00Z",
                "message": "Profile @example_user created successfully"
            }
        }
    },
    "get_changes": {
        "description": "Get recent changes with filtering",
        "request": {
            "method": "GET",
            "url": "/api/v1/changes?profile=example_user&type=gained&days=7&page=1&per_page=20"
        },
        "response": {
            "status": 200,
            "body": {
                "changes": [
                    {
                        "id": "example_user_new_follower_2024-01-01T12:00:00Z",
                        "profile_username": "example_user",
                        "affected_username": "new_follower",
                        "change_type": "gained",
                        "timestamp": "2024-01-01T12:00:00Z",
                        "is_follower_change": True,
                        "is_following_change": False,
                        "display_text": "@new_follower started following @example_user"
                    }
                ],
                "pagination": {
                    "page": 1,
                    "per_page": 20,
                    "total_count": 1,
                    "total_pages": 1,
                    "has_next": False,
                    "has_prev": False
                },
                "filters": {
                    "profile": "example_user",
                    "type": "gained",
                    "search": "",
                    "days": 7,
                    "sort_by": "timestamp",
                    "sort_order": "desc"
                }
            }
        }
    },
    "monitoring_status": {
        "description": "Get monitoring system status",
        "request": {
            "method": "GET",
            "url": "/api/v1/monitoring/status"
        },
        "response": {
            "status": 200,
            "body": {
                "system_status": {
                    "total_profiles": 5,
                    "enabled_profiles": 4,
                    "profiles_due_for_scan": 2,
                    "profiles_with_recent_scans": 3,
                    "authentication_configured": True
                },
                "profile_status": {
                    "example_user": {
                        "last_scan": "2024-01-01T10:00:00Z",
                        "hours_ago": 2.5,
                        "is_due": False
                    }
                },
                "generated_at": "2024-01-01T12:30:00Z"
            }
        }
    }
}

def get_api_documentation() -> Dict[str, Any]:
    """Get complete API documentation."""
    return {
        "documentation": API_DOCUMENTATION,
        "schemas": {
            "responses": RESPONSE_SCHEMAS,
            "requests": REQUEST_SCHEMAS,
            "data": DATA_SCHEMAS
        },
        "examples": USAGE_EXAMPLES
    }

def get_openapi_spec() -> Dict[str, Any]:
    """Generate OpenAPI 3.0 specification."""
    return {
        "openapi": "3.0.0",
        "info": API_DOCUMENTATION["info"],
        "servers": [
            {"url": "/api/v1", "description": "API v1"}
        ],
        "security": [
            {"ApiKeyAuth": []}
        ],
        "components": {
            "securitySchemes": {
                "ApiKeyAuth": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "X-API-Key"
                }
            },
            "schemas": {**RESPONSE_SCHEMAS, **REQUEST_SCHEMAS, **DATA_SCHEMAS}
        },
        "paths": generate_openapi_paths()
    }

def generate_openapi_paths() -> Dict[str, Any]:
    """Generate OpenAPI paths from documentation."""
    paths = {}
    
    for category, endpoints in API_DOCUMENTATION["endpoints"].items():
        for endpoint, config in endpoints.items():
            method, path = endpoint.split(" ", 1)
            
            if path not in paths:
                paths[path] = {}
            
            paths[path][method.lower()] = {
                "summary": config["description"],
                "parameters": format_openapi_parameters(config.get("parameters", {})),
                "responses": {
                    "200": {
                        "description": "Success",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": f"#/components/schemas/{config.get('response_schema', 'MessageResponse')}"}
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                            }
                        }
                    },
                    "429": {
                        "description": "Rate Limit Exceeded",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                            }
                        }
                    }
                }
            }
            
            if config.get("authentication") == "required":
                paths[path][method.lower()]["security"] = [{"ApiKeyAuth": []}]
            
            if "request_schema" in config:
                paths[path][method.lower()]["requestBody"] = {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {"$ref": f"#/components/schemas/{config['request_schema']}"}
                        }
                    }
                }
    
    return paths

def format_openapi_parameters(params: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Format parameters for OpenAPI specification."""
    openapi_params = []
    
    for name, config in params.items():
        param = {
            "name": name,
            "in": "path" if config.get("required") and "{" + name + "}" in name else "query",
            "required": config.get("required", False),
            "schema": {
                "type": config["type"]
            }
        }
        
        if "description" in config:
            param["description"] = config["description"]
        if "default" in config:
            param["schema"]["default"] = config["default"]
        if "enum" in config:
            param["schema"]["enum"] = config["enum"]
        if "minimum" in config:
            param["schema"]["minimum"] = config["minimum"]
        if "maximum" in config:
            param["schema"]["maximum"] = config["maximum"]
        
        openapi_params.append(param)
    
    return openapi_params