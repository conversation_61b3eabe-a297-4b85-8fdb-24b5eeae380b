"""
Mock tests for Instagram API interactions using responses library.

This module provides comprehensive mock tests for Instagram API interactions,
simulating various scenarios including rate limiting, authentication failures,
and different response types.
"""

import pytest
import responses
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from services.instagram_client import InstagramClient, RateLimiter
from services.authentication import AuthenticationManager
from models.data_models import ProfileInfo
from config import Config


class TestInstagramClientMocks:
    """Mock tests for InstagramClient using responses library."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration."""
        config = Mock(spec=Config)
        config.MIN_REQUEST_DELAY = 1.0
        config.MAX_REQUEST_DELAY = 3.0
        config.MAX_RETRIES = 3
        config.ENCRYPTION_KEY = "test_key"
        return config
    
    @pytest.fixture
    def instagram_client(self, mock_config):
        """Create InstagramClient with mocked dependencies."""
        with patch('services.instagram_client.AuthenticationManager'):
            return InstagramClient(mock_config)
    
    @responses.activate
    def test_profile_info_retrieval_success(self, instagram_client):
        """Test successful profile information retrieval with mocked responses."""
        # Mock Instagram profile endpoint
        profile_data = {
            'username': 'testuser',
            'full_name': 'Test User',
            'follower_count': 1000,
            'following_count': 500,
            'is_private': False,
            'biography': 'Test biography',
            'external_url': 'https://example.com'
        }
        
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/users/web_profile_info/',
            json={'data': {'user': profile_data}},
            status=200
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader Profile.from_username
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            mock_profile = Mock()
            mock_profile.username = 'testuser'
            mock_profile.full_name = 'Test User'
            mock_profile.followers = 1000
            mock_profile.followees = 500
            mock_profile.is_private = False
            
            mock_profile_class.from_username.return_value = mock_profile
            
            result = instagram_client.get_profile_info('testuser')
            
            assert result is not None
            assert isinstance(result, ProfileInfo)
            assert result.username == 'testuser'
            assert result.display_name == 'Test User'
            assert result.follower_count == 1000
            assert result.following_count == 500
            assert result.is_private is False
    
    @responses.activate
    def test_profile_info_rate_limited(self, instagram_client):
        """Test profile info retrieval with rate limiting."""
        # Mock rate limit response
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/users/web_profile_info/',
            json={'message': 'rate limited'},
            status=429
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader to raise rate limit exception
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            from instaloader.exceptions import QueryReturnedBadRequestException
            mock_profile_class.from_username.side_effect = QueryReturnedBadRequestException(
                "rate limited"
            )
            
            result = instagram_client.get_profile_info('testuser')
            
            assert result is None
            # Should have incremented error count
            assert instagram_client.rate_limiter.consecutive_errors > 0
    
    @responses.activate
    def test_profile_not_found(self, instagram_client):
        """Test handling of non-existent profile."""
        # Mock 404 response
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/users/web_profile_info/',
            json={'message': 'User not found'},
            status=404
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader to raise profile not found exception
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            from instaloader.exceptions import ProfileNotExistsException
            mock_profile_class.from_username.side_effect = ProfileNotExistsException(
                "Profile does not exist"
            )
            
            result = instagram_client.get_profile_info('nonexistent')
            
            assert result is None
    
    @responses.activate
    def test_followers_retrieval_success(self, instagram_client):
        """Test successful followers retrieval."""
        # Mock followers endpoint
        followers_data = {
            'users': [
                {'username': 'follower1', 'full_name': 'Follower One'},
                {'username': 'follower2', 'full_name': 'Follower Two'},
                {'username': 'follower3', 'full_name': 'Follower Three'}
            ]
        }
        
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/friendships/followers/',
            json=followers_data,
            status=200
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader Profile and followers
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            mock_profile = Mock()
            mock_profile.is_private = False
            mock_profile.followed_by_viewer = False
            
            # Mock follower objects
            mock_follower1 = Mock()
            mock_follower1.username = 'follower1'
            mock_follower2 = Mock()
            mock_follower2.username = 'follower2'
            mock_follower3 = Mock()
            mock_follower3.username = 'follower3'
            
            mock_profile.get_followers.return_value = [
                mock_follower1, mock_follower2, mock_follower3
            ]
            mock_profile_class.from_username.return_value = mock_profile
            
            result = instagram_client.get_followers('testuser')
            
            assert result is not None
            assert isinstance(result, set)
            assert len(result) == 3
            assert 'follower1' in result
            assert 'follower2' in result
            assert 'follower3' in result
    
    @responses.activate
    def test_followers_private_profile_no_access(self, instagram_client):
        """Test followers retrieval for private profile without access."""
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock private profile not followed
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            mock_profile = Mock()
            mock_profile.is_private = True
            mock_profile.followed_by_viewer = False
            
            mock_profile_class.from_username.return_value = mock_profile
            
            result = instagram_client.get_followers('privateuser')
            
            assert result is None
    
    @responses.activate
    def test_following_retrieval_success(self, instagram_client):
        """Test successful following retrieval."""
        # Mock following endpoint
        following_data = {
            'users': [
                {'username': 'following1', 'full_name': 'Following One'},
                {'username': 'following2', 'full_name': 'Following Two'}
            ]
        }
        
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/friendships/following/',
            json=following_data,
            status=200
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader Profile and following
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            mock_profile = Mock()
            mock_profile.is_private = False
            mock_profile.followed_by_viewer = False
            
            # Mock following objects
            mock_following1 = Mock()
            mock_following1.username = 'following1'
            mock_following2 = Mock()
            mock_following2.username = 'following2'
            
            mock_profile.get_followees.return_value = [mock_following1, mock_following2]
            mock_profile_class.from_username.return_value = mock_profile
            
            result = instagram_client.get_following('testuser')
            
            assert result is not None
            assert isinstance(result, set)
            assert len(result) == 2
            assert 'following1' in result
            assert 'following2' in result
    
    @responses.activate
    def test_authentication_success(self, instagram_client):
        """Test successful authentication."""
        # Mock login endpoint
        responses.add(
            responses.POST,
            'https://www.instagram.com/accounts/login/ajax/',
            json={'authenticated': True, 'user': True, 'userId': '123456'},
            status=200
        )
        
        # Mock Instaloader login
        with patch('services.instagram_client.instaloader.Instaloader') as mock_instaloader_class:
            mock_loader = Mock()
            mock_loader.login.return_value = None  # Successful login
            mock_instaloader_class.return_value = mock_loader
            instagram_client.loader = mock_loader
            
            # Mock auth manager
            instagram_client.auth_manager.validate_credentials.return_value = True
            instagram_client.auth_manager.store_credentials.return_value = True
            
            result = instagram_client.authenticate('testuser', 'testpass')
            
            assert result is True
            assert instagram_client._authenticated is True
            assert instagram_client._current_username == 'testuser'
            mock_loader.login.assert_called_once_with('testuser', 'testpass')
    
    @responses.activate
    def test_authentication_bad_credentials(self, instagram_client):
        """Test authentication with bad credentials."""
        # Mock login endpoint with error
        responses.add(
            responses.POST,
            'https://www.instagram.com/accounts/login/ajax/',
            json={'authenticated': False, 'message': 'Invalid credentials'},
            status=400
        )
        
        # Mock Instaloader to raise bad credentials exception
        with patch('services.instagram_client.instaloader.Instaloader') as mock_instaloader_class:
            mock_loader = Mock()
            from instaloader.exceptions import BadCredentialsException
            mock_loader.login.side_effect = BadCredentialsException('Invalid credentials')
            mock_instaloader_class.return_value = mock_loader
            instagram_client.loader = mock_loader
            
            # Mock auth manager
            instagram_client.auth_manager.validate_credentials.return_value = True
            
            result = instagram_client.authenticate('testuser', 'wrongpass')
            
            assert result is False
            assert instagram_client._authenticated is False
    
    @responses.activate
    def test_authentication_two_factor_required(self, instagram_client):
        """Test authentication with two-factor authentication required."""
        # Mock login endpoint requiring 2FA
        responses.add(
            responses.POST,
            'https://www.instagram.com/accounts/login/ajax/',
            json={'two_factor_required': True, 'two_factor_info': {'username': 'testuser'}},
            status=400
        )
        
        # Mock Instaloader to raise 2FA exception
        with patch('services.instagram_client.instaloader.Instaloader') as mock_instaloader_class:
            mock_loader = Mock()
            from instaloader.exceptions import TwoFactorAuthRequiredException
            mock_loader.login.side_effect = TwoFactorAuthRequiredException('2FA required')
            mock_instaloader_class.return_value = mock_loader
            instagram_client.loader = mock_loader
            
            # Mock auth manager
            instagram_client.auth_manager.validate_credentials.return_value = True
            
            # Test non-interactive mode (should fail)
            result = instagram_client.authenticate('testuser', 'testpass', interactive=False)
            assert result is False
            
            # Test interactive mode (should also fail as 2FA is not implemented)
            result = instagram_client.authenticate('testuser', 'testpass', interactive=True)
            assert result is False
    
    @responses.activate
    def test_rate_limiting_behavior(self, instagram_client):
        """Test rate limiting behavior with multiple requests."""
        # Mock multiple rate limited responses
        for _ in range(5):
            responses.add(
                responses.GET,
                'https://www.instagram.com/api/v1/users/web_profile_info/',
                json={'message': 'rate limited'},
                status=429
            )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader to raise rate limit exceptions
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            from instaloader.exceptions import QueryReturnedBadRequestException
            mock_profile_class.from_username.side_effect = QueryReturnedBadRequestException(
                "rate limited"
            )
            
            # Make multiple requests
            for i in range(3):
                result = instagram_client.get_profile_info('testuser')
                assert result is None
            
            # Should have accumulated errors
            assert instagram_client.rate_limiter.consecutive_errors == 3
            
            # Should be in backoff period
            assert instagram_client.rate_limiter.backoff_until > 0
    
    @responses.activate
    def test_session_rotation(self, instagram_client):
        """Test session rotation functionality."""
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client._current_username = 'testuser'
        
        # Mock rate limiter to indicate rotation needed
        instagram_client.rate_limiter.should_rotate_session = Mock(return_value=True)
        instagram_client.rate_limiter.reset_session = Mock()
        instagram_client.rate_limiter.get_next_user_agent = Mock(return_value='new_agent')
        
        # Mock new loader creation
        with patch('services.instagram_client.instaloader.Instaloader') as mock_instaloader_class:
            mock_new_loader = Mock()
            mock_instaloader_class.return_value = mock_new_loader
            
            # Mock successful re-authentication
            with patch.object(instagram_client, 'authenticate') as mock_auth:
                mock_auth.return_value = True
                
                result = instagram_client.rotate_session_if_needed()
                
                assert result is True
                instagram_client.rate_limiter.should_rotate_session.assert_called_once()
                instagram_client.rate_limiter.reset_session.assert_called_once()
                mock_auth.assert_called_once()
                
                # Should have created new loader
                assert instagram_client.loader == mock_new_loader
    
    @responses.activate
    def test_large_follower_list_pagination(self, instagram_client):
        """Test handling of large follower lists with pagination."""
        # Mock multiple paginated responses
        page1_data = {
            'users': [{'username': f'follower{i}'} for i in range(1, 51)],  # 50 followers
            'page_info': {'has_next_page': True, 'end_cursor': 'cursor1'}
        }
        
        page2_data = {
            'users': [{'username': f'follower{i}'} for i in range(51, 101)],  # 50 more
            'page_info': {'has_next_page': False, 'end_cursor': None}
        }
        
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/friendships/followers/',
            json=page1_data,
            status=200
        )
        
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/friendships/followers/',
            json=page2_data,
            status=200
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader Profile with large follower list
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            mock_profile = Mock()
            mock_profile.is_private = False
            mock_profile.followed_by_viewer = False
            
            # Create mock follower objects
            mock_followers = []
            for i in range(1, 101):
                mock_follower = Mock()
                mock_follower.username = f'follower{i}'
                mock_followers.append(mock_follower)
            
            mock_profile.get_followers.return_value = mock_followers
            mock_profile_class.from_username.return_value = mock_profile
            
            result = instagram_client.get_followers('testuser')
            
            assert result is not None
            assert len(result) == 100
            assert all(f'follower{i}' in result for i in range(1, 101))
    
    @responses.activate
    def test_network_timeout_handling(self, instagram_client):
        """Test handling of network timeouts."""
        # Mock timeout response
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/users/web_profile_info/',
            body=responses.ConnectionError('Connection timeout'),
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader to raise connection error
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            mock_profile_class.from_username.side_effect = ConnectionError('Connection timeout')
            
            result = instagram_client.get_profile_info('testuser')
            
            assert result is None
    
    @responses.activate
    def test_malformed_response_handling(self, instagram_client):
        """Test handling of malformed API responses."""
        # Mock malformed JSON response
        responses.add(
            responses.GET,
            'https://www.instagram.com/api/v1/users/web_profile_info/',
            body='invalid json response',
            status=200
        )
        
        # Mock authentication
        instagram_client._authenticated = True
        instagram_client.loader = Mock()
        instagram_client.loader.context = Mock()
        instagram_client.loader.context.is_logged_in = True
        
        # Mock Instaloader to raise JSON decode error
        with patch('services.instagram_client.instaloader.Profile') as mock_profile_class:
            mock_profile_class.from_username.side_effect = json.JSONDecodeError(
                'Invalid JSON', 'response', 0
            )
            
            result = instagram_client.get_profile_info('testuser')
            
            assert result is None


class TestRateLimiterMocks:
    """Mock tests for RateLimiter functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration."""
        config = Mock(spec=Config)
        config.MIN_REQUEST_DELAY = 1.0
        config.MAX_REQUEST_DELAY = 3.0
        config.MAX_RETRIES = 3
        return config
    
    @pytest.fixture
    def rate_limiter(self, mock_config):
        """Create RateLimiter instance."""
        return RateLimiter(mock_config)
    
    def test_exponential_backoff_calculation(self, rate_limiter):
        """Test exponential backoff calculation."""
        initial_backoff = rate_limiter.backoff_until
        
        # First error
        rate_limiter.handle_rate_limit_error()
        first_backoff = rate_limiter.backoff_until - initial_backoff
        
        # Second error
        rate_limiter.handle_rate_limit_error()
        second_backoff = rate_limiter.backoff_until - initial_backoff
        
        # Third error
        rate_limiter.handle_rate_limit_error()
        third_backoff = rate_limiter.backoff_until - initial_backoff
        
        # Each backoff should be longer than the previous
        assert second_backoff > first_backoff
        assert third_backoff > second_backoff
        
        # Should follow exponential pattern
        assert second_backoff >= first_backoff * 2
        assert third_backoff >= second_backoff * 2
    
    @patch('services.instagram_client.time.sleep')
    @patch('services.instagram_client.time.time')
    def test_adaptive_delay_business_hours(self, mock_time, mock_sleep, rate_limiter):
        """Test adaptive delay during business hours."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Mock business hours (10 AM)
        with patch('services.instagram_client.time.localtime') as mock_localtime:
            mock_time_struct = Mock()
            mock_time_struct.tm_hour = 10
            mock_localtime.return_value = mock_time_struct
            
            delay = rate_limiter._calculate_adaptive_delay('test')
            
            # Should include business hours penalty
            assert delay >= 0.5  # Base business hours delay
    
    @patch('services.instagram_client.time.time')
    def test_adaptive_delay_high_frequency(self, mock_time, rate_limiter):
        """Test adaptive delay with high request frequency."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Add many recent requests
        recent_time = current_time - 300  # 5 minutes ago
        rate_limiter.request_history = [recent_time + i for i in range(30)]  # 30 requests
        
        delay = rate_limiter._calculate_adaptive_delay('test')
        
        # Should have high frequency penalty
        assert delay >= 2.0  # Significant delay for high frequency
    
    def test_user_agent_rotation(self, rate_limiter):
        """Test user agent rotation functionality."""
        # Get all available user agents
        all_agents = set()
        for _ in range(len(rate_limiter.user_agents) * 2):
            agent = rate_limiter.get_next_user_agent()
            all_agents.add(agent)
        
        # Should have rotated through all available agents
        assert len(all_agents) == len(rate_limiter.user_agents)
        
        # Should cycle back to first agent
        first_agent = rate_limiter.get_next_user_agent()
        for _ in range(len(rate_limiter.user_agents) - 1):
            rate_limiter.get_next_user_agent()
        
        cycled_agent = rate_limiter.get_next_user_agent()
        assert cycled_agent == first_agent
    
    @patch('services.instagram_client.time.time')
    def test_session_rotation_triggers(self, mock_time, rate_limiter):
        """Test various session rotation triggers."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Test time-based rotation (2+ hours)
        rate_limiter.session_start_time = current_time - 7300  # More than 2 hours
        assert rate_limiter.should_rotate_session() is True
        
        # Reset for next test
        rate_limiter.session_start_time = current_time
        
        # Test request count-based rotation (500+ requests)
        rate_limiter.request_count = 600
        assert rate_limiter.should_rotate_session() is True
        
        # Reset for next test
        rate_limiter.request_count = 0
        
        # Test error-based rotation (3+ consecutive errors)
        rate_limiter.consecutive_errors = 5
        assert rate_limiter.should_rotate_session() is True
        
        # Test no rotation needed
        rate_limiter.consecutive_errors = 1
        assert rate_limiter.should_rotate_session() is False
    
    @patch('services.instagram_client.time.time')
    def test_request_pattern_monitoring(self, mock_time, rate_limiter):
        """Test request pattern monitoring and cleanup."""
        current_time = 1000.0
        mock_time.return_value = current_time
        
        # Add requests over time
        for i in range(10):
            request_time = current_time - (i * 300)  # Every 5 minutes
            rate_limiter._update_request_patterns('profile_info', request_time)
        
        # Should have recorded all requests
        assert len(rate_limiter.request_history) == 10
        assert len(rate_limiter.request_types['profile_info']) == 10
        
        # Advance time significantly (more than 1 hour)
        future_time = current_time + 4000
        mock_time.return_value = future_time
        
        # Add new request - should trigger cleanup
        rate_limiter._update_request_patterns('profile_info', future_time)
        
        # Old requests should be cleaned up (only recent ones remain)
        assert len(rate_limiter.request_history) < 10
        assert len(rate_limiter.request_types['profile_info']) < 10
    
    def test_request_statistics_tracking(self, rate_limiter):
        """Test request statistics tracking."""
        # Initial stats
        stats = rate_limiter.get_request_stats()
        assert stats['total_requests'] == 0
        assert stats['recent_requests_10min'] == 0
        assert stats['consecutive_errors'] == 0
        
        # Simulate some requests and errors
        rate_limiter.request_count = 50
        rate_limiter.consecutive_errors = 2
        
        # Add some request history
        current_time = 1000.0
        with patch('services.instagram_client.time.time', return_value=current_time):
            # Add recent requests (within 10 minutes)
            recent_requests = [current_time - (i * 60) for i in range(5)]  # 5 requests in last 5 minutes
            rate_limiter.request_history = recent_requests
            
            stats = rate_limiter.get_request_stats()
            
            assert stats['total_requests'] == 50
            assert stats['recent_requests_10min'] == 5
            assert stats['consecutive_errors'] == 2