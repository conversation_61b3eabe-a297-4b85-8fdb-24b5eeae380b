#!/usr/bin/env python3
"""
Instagram Follower Monitor - Main Application Entry Point

This is the main entry point for the Instagram follower monitoring application.
It initializes the Flask web application, sets up the database, and starts
the monitoring scheduler.
"""

import os
import sys
import signal
import atexit
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.app import create_app
from config import Config
from services.logging_config import setup_logging, get_logger
from services.system_monitor import get_system_monitor, setup_default_alert_handlers
from services.log_manager import get_log_manager


def setup_signal_handlers():
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger = get_logger(__name__)
        logger.info("Received shutdown signal", signal=signum)
        
        # Stop monitoring services
        try:
            system_monitor = get_system_monitor()
            system_monitor.stop_monitoring()
            
            log_manager = get_log_manager()
            log_manager.stop_scheduler()
            
            logger.info("Application shutdown completed")
        except Exception as e:
            logger.error("Error during shutdown", error=str(e))
        
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def setup_monitoring_services(config: Config):
    """Set up system monitoring and log management services."""
    logger = get_logger(__name__)
    
    try:
        # Set up system monitoring
        system_monitor = get_system_monitor(config)
        setup_default_alert_handlers()
        system_monitor.start_monitoring()
        
        # Set up log management
        log_manager = get_log_manager(config)
        log_manager.start_scheduler()
        
        logger.info("Monitoring services started successfully")
        
    except Exception as e:
        logger.error("Failed to start monitoring services", error=str(e))
        raise


def main():
    """Main application entry point."""
    try:
        # Load configuration
        config = Config()
        
        # Set up logging first
        setup_logging(config)
        logger = get_logger(__name__)
        
        logger.info("Starting Instagram Follower Monitor", 
                   version="1.0.0",
                   debug_mode=config.DEBUG,
                   host=config.HOST,
                   port=config.PORT)
        
        # Set up signal handlers for graceful shutdown
        setup_signal_handlers()
        
        # Set up monitoring services
        setup_monitoring_services(config)
        
        # Create Flask application
        app = create_app(config)
        
        # Register cleanup function
        def cleanup():
            logger.info("Application cleanup initiated")
            try:
                system_monitor = get_system_monitor()
                system_monitor.stop_monitoring()
                
                log_manager = get_log_manager()
                log_manager.stop_scheduler()
            except Exception as e:
                logger.error("Error during cleanup", error=str(e))
        
        atexit.register(cleanup)
        
        logger.info("Application startup completed successfully")
        
        # Run the application
        if __name__ == '__main__':
            app.run(
                host=config.HOST,
                port=config.PORT,
                debug=config.DEBUG,
                use_reloader=False  # Disable reloader to prevent duplicate monitoring services
            )
    
    except Exception as e:
        # Use basic logging if structured logging isn't available yet
        try:
            logger = get_logger(__name__)
            logger.critical("Failed to start application", error=str(e))
        except:
            print(f"CRITICAL: Failed to start application: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()