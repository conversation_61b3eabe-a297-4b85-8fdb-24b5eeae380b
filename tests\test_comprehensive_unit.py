"""
Comprehensive unit tests for all core business logic components.

This module provides comprehensive unit tests for change detection, data processing,
profile scanning, and other core business logic components.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Set, List

from models.data_models import (
    Follower<PERSON><PERSON><PERSON>, ChangeType, ProfileIn<PERSON>, <PERSON>ingConfig, 
    User<PERSON><PERSON>, validate_username
)
from services.change_detector import ChangeDetector
from services.data_processor import DataProcessor
from services.profile_scanner import ProfileScanner
from config import Config


class TestChangeDetectorComprehensive:
    """Comprehensive tests for ChangeDetector class."""
    
    @pytest.fixture
    def change_detector(self):
        """Create a ChangeDetector instance for testing."""
        return ChangeDetector()
    
    def test_detect_changes_new_followers(self, change_detector):
        """Test detection of new followers."""
        current_followers = {"user1", "user2", "user3"}
        previous_followers = {"user1", "user2"}
        current_following = {"follow1"}
        previous_following = {"follow1"}
        
        changes = change_detector.detect_changes(
            "testuser", 1, current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect one new follower
        follower_changes = [c for c in changes if c.is_follower_change]
        assert len(follower_changes) == 1
        assert follower_changes[0].affected_username == "user3"
        assert follower_changes[0].change_type == ChangeType.GAINED
    
    def test_detect_changes_lost_followers(self, change_detector):
        """Test detection of lost followers."""
        current_followers = {"user1"}
        previous_followers = {"user1", "user2", "user3"}
        current_following = {"follow1"}
        previous_following = {"follow1"}
        
        changes = change_detector.detect_changes(
            "testuser", 1, current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect two lost followers
        follower_changes = [c for c in changes if c.is_follower_change]
        assert len(follower_changes) == 2
        lost_usernames = {c.affected_username for c in follower_changes}
        assert lost_usernames == {"user2", "user3"}
        assert all(c.change_type == ChangeType.LOST for c in follower_changes)
    
    def test_detect_changes_new_following(self, change_detector):
        """Test detection of new following."""
        current_followers = {"user1"}
        previous_followers = {"user1"}
        current_following = {"follow1", "follow2"}
        previous_following = {"follow1"}
        
        changes = change_detector.detect_changes(
            "testuser", 1, current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect one new following
        following_changes = [c for c in changes if c.is_following_change]
        assert len(following_changes) == 1
        assert following_changes[0].affected_username == "follow2"
        assert following_changes[0].change_type == ChangeType.STARTED_FOLLOWING
    
    def test_detect_changes_stopped_following(self, change_detector):
        """Test detection of stopped following."""
        current_followers = {"user1"}
        previous_followers = {"user1"}
        current_following = {"follow1"}
        previous_following = {"follow1", "follow2", "follow3"}
        
        changes = change_detector.detect_changes(
            "testuser", 1, current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect two stopped following
        following_changes = [c for c in changes if c.is_following_change]
        assert len(following_changes) == 2
        stopped_usernames = {c.affected_username for c in following_changes}
        assert stopped_usernames == {"follow2", "follow3"}
        assert all(c.change_type == ChangeType.STOPPED_FOLLOWING for c in following_changes)
    
    def test_detect_changes_mixed_changes(self, change_detector):
        """Test detection of mixed follower and following changes."""
        current_followers = {"user1", "user3"}  # lost user2, gained user3
        previous_followers = {"user1", "user2"}
        current_following = {"follow1", "follow3"}  # lost follow2, gained follow3
        previous_following = {"follow1", "follow2"}
        
        changes = change_detector.detect_changes(
            "testuser", 1, current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect 4 changes total
        assert len(changes) == 4
        
        # Check follower changes
        follower_changes = [c for c in changes if c.is_follower_change]
        assert len(follower_changes) == 2
        
        gained_followers = [c for c in follower_changes if c.change_type == ChangeType.GAINED]
        lost_followers = [c for c in follower_changes if c.change_type == ChangeType.LOST]
        assert len(gained_followers) == 1
        assert len(lost_followers) == 1
        assert gained_followers[0].affected_username == "user3"
        assert lost_followers[0].affected_username == "user2"
        
        # Check following changes
        following_changes = [c for c in changes if c.is_following_change]
        assert len(following_changes) == 2
        
        started_following = [c for c in following_changes if c.change_type == ChangeType.STARTED_FOLLOWING]
        stopped_following = [c for c in following_changes if c.change_type == ChangeType.STOPPED_FOLLOWING]
        assert len(started_following) == 1
        assert len(stopped_following) == 1
        assert started_following[0].affected_username == "follow3"
        assert stopped_following[0].affected_username == "follow2"
    
    def test_detect_changes_no_changes(self, change_detector):
        """Test detection when no changes occurred."""
        current_followers = {"user1", "user2"}
        previous_followers = {"user1", "user2"}
        current_following = {"follow1"}
        previous_following = {"follow1"}
        
        changes = change_detector.detect_changes(
            "testuser", 1, current_followers, previous_followers,
            current_following, previous_following
        )
        
        assert len(changes) == 0
    
    def test_detect_changes_invalid_usernames(self, change_detector):
        """Test detection with invalid usernames."""
        current_followers = {"user1", "", "user@invalid", "user3"}
        previous_followers = {"user1", "user2"}
        current_following = {"follow1"}
        previous_following = {"follow1"}
        
        with patch('services.change_detector.logger') as mock_logger:
            changes = change_detector.detect_changes(
                "testuser", 1, current_followers, previous_followers,
                current_following, previous_following
            )
            
            # Should still detect valid changes and log warnings for invalid usernames
            mock_logger.warning.assert_called()
            
            # Should detect user3 as gained (invalid usernames filtered out)
            follower_changes = [c for c in changes if c.is_follower_change]
            gained = [c for c in follower_changes if c.change_type == ChangeType.GAINED]
            assert len(gained) == 1
            assert gained[0].affected_username == "user3"
    
    def test_analyze_change_patterns(self, change_detector):
        """Test change pattern analysis."""
        changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1),
            FollowerChange("testuser", "user2", ChangeType.GAINED, profile_id=1),
            FollowerChange("testuser", "user3", ChangeType.LOST, profile_id=1),
            FollowerChange("testuser", "follow1", ChangeType.STARTED_FOLLOWING, profile_id=1),
            FollowerChange("testuser", "follow2", ChangeType.STOPPED_FOLLOWING, profile_id=1),
        ]
        
        analysis = change_detector.analyze_change_patterns(changes)
        
        assert analysis['total_changes'] == 5
        assert analysis['follower_changes'] == 3
        assert analysis['following_changes'] == 2
        assert analysis['net_follower_change'] == 1  # +2 -1
        assert analysis['net_following_change'] == 0  # +1 -1
        assert analysis['gained_followers'] == 2
        assert analysis['lost_followers'] == 1
        assert analysis['started_following'] == 1
        assert analysis['stopped_following'] == 1
        
        # Check change type breakdown
        assert analysis['change_types']['gained'] == 2
        assert analysis['change_types']['lost'] == 1
        assert analysis['change_types']['started_following'] == 1
        assert analysis['change_types']['stopped_following'] == 1
        
        # Check affected users
        expected_users = {"user1", "user2", "user3", "follow1", "follow2"}
        assert set(analysis['affected_users']) == expected_users
    
    def test_compare_lists(self, change_detector):
        """Test list comparison functionality."""
        current_list = {"user1", "user2", "user3"}
        previous_list = {"user1", "user4", "user5"}
        
        result = change_detector.compare_lists(current_list, previous_list)
        
        assert result['added'] == {"user2", "user3"}
        assert result['removed'] == {"user4", "user5"}
        assert result['unchanged'] == {"user1"}
    
    def test_filter_changes_by_type(self, change_detector):
        """Test filtering changes by type."""
        changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1),
            FollowerChange("testuser", "user2", ChangeType.LOST, profile_id=1),
            FollowerChange("testuser", "follow1", ChangeType.STARTED_FOLLOWING, profile_id=1),
        ]
        
        # Filter for only follower gains
        gained_changes = change_detector.filter_changes_by_type(changes, [ChangeType.GAINED])
        assert len(gained_changes) == 1
        assert gained_changes[0].change_type == ChangeType.GAINED
        
        # Filter for follower changes
        follower_changes = change_detector.filter_changes_by_type(
            changes, [ChangeType.GAINED, ChangeType.LOST]
        )
        assert len(follower_changes) == 2
    
    def test_group_changes_by_profile(self, change_detector):
        """Test grouping changes by profile."""
        changes = [
            FollowerChange("profile1", "user1", ChangeType.GAINED, profile_id=1),
            FollowerChange("profile1", "user2", ChangeType.LOST, profile_id=1),
            FollowerChange("profile2", "user3", ChangeType.GAINED, profile_id=2),
        ]
        
        grouped = change_detector.group_changes_by_profile(changes)
        
        assert len(grouped) == 2
        assert len(grouped['profile1']) == 2
        assert len(grouped['profile2']) == 1
        assert grouped['profile1'][0].profile_username == "profile1"
        assert grouped['profile2'][0].profile_username == "profile2"
    
    def test_is_significant_change(self, change_detector):
        """Test significance detection."""
        # Test with significant number of changes
        many_changes = [
            FollowerChange("testuser", f"user{i}", ChangeType.GAINED, profile_id=1)
            for i in range(15)
        ]
        assert change_detector.is_significant_change(many_changes, threshold_absolute=10) is True
        
        # Test with few changes
        few_changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1)
        ]
        assert change_detector.is_significant_change(few_changes, threshold_absolute=10) is False
        
        # Test with no changes
        assert change_detector.is_significant_change([], threshold_absolute=10) is False
    
    def test_detection_statistics(self, change_detector):
        """Test detection statistics tracking."""
        # Initial stats should be zero
        stats = change_detector.get_detection_statistics()
        assert stats['total_detections'] == 0
        assert stats['changes_detected'] == 0
        
        # Run some detections
        change_detector.detect_changes(
            "testuser", 1, {"user1", "user2"}, {"user1"},
            {"follow1"}, {"follow1"}
        )
        
        stats = change_detector.get_detection_statistics()
        assert stats['total_detections'] == 1
        assert stats['changes_detected'] == 1
        assert stats['follower_changes'] == 1
        assert stats['following_changes'] == 0
        assert stats['average_changes_per_detection'] == 1.0
        
        # Reset stats
        change_detector.reset_statistics()
        stats = change_detector.get_detection_statistics()
        assert stats['total_detections'] == 0


class TestDataProcessorComprehensive:
    """Comprehensive tests for DataProcessor class."""
    
    @pytest.fixture
    def mock_repositories(self):
        """Create mock repositories for testing."""
        follower_repo = Mock()
        change_repo = Mock()
        profile_repo = Mock()
        return follower_repo, change_repo, profile_repo
    
    @pytest.fixture
    def data_processor(self, mock_repositories):
        """Create a DataProcessor instance for testing."""
        follower_repo, change_repo, profile_repo = mock_repositories
        return DataProcessor(follower_repo, change_repo, profile_repo)
    
    def test_process_changes_success(self, data_processor, mock_repositories):
        """Test successful change processing."""
        follower_repo, change_repo, profile_repo = mock_repositories
        
        # Setup test data
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        current_followers = {"user1", "user2"}
        current_following = {"follow1"}
        
        changes = [
            FollowerChange("testuser", "user2", ChangeType.GAINED, profile_id=1)
        ]
        
        # Mock repository methods
        change_repo.store_follower_changes.return_value = None
        follower_repo.store_current_followers.return_value = None
        follower_repo.store_current_following.return_value = None
        profile_repo.update_profile.return_value = True
        
        # Process changes
        result = data_processor.process_changes(
            profile, profile_info, current_followers, current_following, changes
        )
        
        # Verify results
        assert result['success'] is True
        assert result['changes_stored'] == 1
        assert result['followers_updated'] == 2
        assert result['following_updated'] == 1
        assert result['profile_updated'] is True
        
        # Verify repository calls
        change_repo.store_follower_changes.assert_called_once_with(changes)
        follower_repo.store_current_followers.assert_called_once_with(1, current_followers)
        follower_repo.store_current_following.assert_called_once_with(1, current_following)
        profile_repo.update_profile.assert_called_once()
    
    def test_process_changes_no_profile_id(self, data_processor):
        """Test processing with missing profile ID."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=None,  # Missing ID
            enabled=True
        )
        
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        result = data_processor.process_changes(
            profile, profile_info, set(), set(), []
        )
        
        assert result['success'] is False
        assert 'Profile ID is required' in result['error']
        assert result['changes_stored'] == 0
    
    def test_process_changes_store_failure(self, data_processor, mock_repositories):
        """Test processing with change storage failure."""
        follower_repo, change_repo, profile_repo = mock_repositories
        
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1)
        ]
        
        # Mock storage failure
        change_repo.store_follower_changes.side_effect = Exception("Database error")
        
        result = data_processor.process_changes(
            profile, profile_info, set(), set(), changes
        )
        
        assert result['success'] is False
        assert 'Failed to store changes' in result['error']
        assert result['changes_stored'] == 0
    
    def test_store_initial_baseline(self, data_processor, mock_repositories):
        """Test storing initial baseline data."""
        follower_repo, change_repo, profile_repo = mock_repositories
        
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        followers = {"user1", "user2", "user3"}
        following = {"follow1", "follow2"}
        
        # Mock repository methods
        follower_repo.store_current_followers.return_value = None
        follower_repo.store_current_following.return_value = None
        profile_repo.update_profile.return_value = True
        
        result = data_processor.store_initial_baseline(profile, followers, following)
        
        assert result['success'] is True
        assert result['followers_stored'] == 3
        assert result['following_stored'] == 2
        
        # Verify repository calls
        follower_repo.store_current_followers.assert_called_once_with(1, followers)
        follower_repo.store_current_following.assert_called_once_with(1, following)
        profile_repo.update_profile.assert_called_once()
    
    def test_validate_data_integrity_success(self, data_processor, mock_repositories):
        """Test successful data integrity validation."""
        follower_repo, change_repo, profile_repo = mock_repositories
        
        # Mock valid data
        follower_repo.get_current_followers.return_value = {"user1", "user2"}
        follower_repo.get_current_following.return_value = {"follow1"}
        change_repo.get_recent_changes.return_value = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1)
        ]
        
        result = data_processor.validate_data_integrity(1)
        
        assert result['validation_passed'] is True
        assert result['current_followers_count'] == 2
        assert result['current_following_count'] == 1
        assert result['recent_changes_count'] == 1
        assert len(result['issues']) == 0
    
    def test_validate_data_integrity_with_issues(self, data_processor, mock_repositories):
        """Test data integrity validation with issues."""
        follower_repo, change_repo, profile_repo = mock_repositories
        
        # Mock data with issues (invalid usernames)
        follower_repo.get_current_followers.return_value = {"user1", "", "user@invalid"}
        follower_repo.get_current_following.return_value = {"follow1"}
        change_repo.get_recent_changes.return_value = []
        
        result = data_processor.validate_data_integrity(1)
        
        assert result['validation_passed'] is False
        assert len(result['issues']) > 0
        assert any('Invalid follower usernames' in issue for issue in result['issues'])
    
    def test_cleanup_invalid_data(self, data_processor, mock_repositories):
        """Test cleanup of invalid data."""
        follower_repo, change_repo, profile_repo = mock_repositories
        
        # Mock data with invalid usernames
        follower_repo.get_current_followers.return_value = {"user1", "", "user@invalid", "user2"}
        follower_repo.get_current_following.return_value = {"follow1", "follow@invalid"}
        
        # Mock store methods
        follower_repo.store_current_followers.return_value = None
        follower_repo.store_current_following.return_value = None
        
        result = data_processor.cleanup_invalid_data(1)
        
        assert result['followers_cleaned'] == 2  # Empty string and invalid username
        assert result['following_cleaned'] == 1  # Invalid username
        assert result['followers_remaining'] == 2  # user1, user2
        assert result['following_remaining'] == 1  # follow1
        
        # Verify cleanup was performed
        follower_repo.store_current_followers.assert_called_once()
        follower_repo.store_current_following.assert_called_once()
    
    def test_processing_statistics(self, data_processor):
        """Test processing statistics tracking."""
        # Initial stats should be zero
        stats = data_processor.get_processing_statistics()
        assert stats['total_processing_runs'] == 0
        assert stats['successful_runs'] == 0
        assert stats['failed_runs'] == 0
        assert stats['success_rate'] == 0.0
        
        # Simulate successful processing
        data_processor._update_processing_stats(True, 5)
        
        stats = data_processor.get_processing_statistics()
        assert stats['total_processing_runs'] == 1
        assert stats['successful_runs'] == 1
        assert stats['failed_runs'] == 0
        assert stats['success_rate'] == 1.0
        assert stats['average_changes_per_run'] == 5.0
        
        # Simulate failed processing
        data_processor._update_processing_stats(False, 0, "Test error")
        
        stats = data_processor.get_processing_statistics()
        assert stats['total_processing_runs'] == 2
        assert stats['successful_runs'] == 1
        assert stats['failed_runs'] == 1
        assert stats['success_rate'] == 0.5
        assert len(stats['errors']) == 1
        assert stats['errors'][0]['error'] == "Test error"


class TestProfileScannerComprehensive:
    """Comprehensive tests for ProfileScanner class."""
    
    @pytest.fixture
    def mock_instagram_client(self):
        """Create a mock Instagram client."""
        client = Mock()
        client.is_authenticated.return_value = True
        return client
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration."""
        return Mock(spec=Config)
    
    @pytest.fixture
    def profile_scanner(self, mock_instagram_client, mock_config):
        """Create a ProfileScanner instance for testing."""
        return ProfileScanner(mock_instagram_client, mock_config)
    
    def test_scan_profile_success(self, profile_scanner, mock_instagram_client):
        """Test successful profile scanning."""
        # Mock profile info
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        # Mock Instagram client responses
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_followers.return_value = {"user1", "user2", "user3"}
        mock_instagram_client.get_following.return_value = {"follow1", "follow2"}
        
        result = profile_scanner.scan_profile("testuser")
        
        assert result['success'] is True
        assert result['username'] == "testuser"
        assert result['followers_count'] == 3
        assert result['following_count'] == 2
        
        # Check scan data
        scan_data = result['data']
        assert scan_data['profile_info'] == profile_info
        assert scan_data['followers'] == {"user1", "user2", "user3"}
        assert scan_data['following'] == {"follow1", "follow2"}
    
    def test_scan_profile_not_authenticated(self, profile_scanner, mock_instagram_client):
        """Test scanning when not authenticated."""
        mock_instagram_client.is_authenticated.return_value = False
        
        result = profile_scanner.scan_profile("testuser")
        
        assert result['success'] is False
        assert 'not authenticated' in result['error']
    
    def test_scan_profile_not_found(self, profile_scanner, mock_instagram_client):
        """Test scanning non-existent profile."""
        mock_instagram_client.get_profile_info.return_value = None
        
        result = profile_scanner.scan_profile("nonexistent")
        
        assert result['success'] is False
        assert 'Could not retrieve profile information' in result['error']
    
    def test_scan_profile_followers_only(self, profile_scanner, mock_instagram_client):
        """Test scanning with followers only."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_followers.return_value = {"user1", "user2"}
        
        result = profile_scanner.scan_profile(
            "testuser", 
            include_followers=True, 
            include_following=False
        )
        
        assert result['success'] is True
        assert result['followers_count'] == 2
        assert result['following_count'] == 0
        
        # Should not have called get_following
        mock_instagram_client.get_following.assert_not_called()
    
    def test_scan_profile_following_only(self, profile_scanner, mock_instagram_client):
        """Test scanning with following only."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_following.return_value = {"follow1", "follow2"}
        
        result = profile_scanner.scan_profile(
            "testuser", 
            include_followers=False, 
            include_following=True
        )
        
        assert result['success'] is True
        assert result['followers_count'] == 0
        assert result['following_count'] == 2
        
        # Should not have called get_followers
        mock_instagram_client.get_followers.assert_not_called()
    
    def test_scan_profile_with_limits(self, profile_scanner, mock_instagram_client):
        """Test scanning with follower/following limits."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=1000,
            following_count=500,
            is_private=False
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_followers.return_value = {"user1", "user2"}
        mock_instagram_client.get_following.return_value = {"follow1"}
        
        result = profile_scanner.scan_profile(
            "testuser", 
            max_followers=100, 
            max_following=50
        )
        
        assert result['success'] is True
        
        # Verify limits were passed to Instagram client
        mock_instagram_client.get_followers.assert_called_once_with("testuser", 100)
        mock_instagram_client.get_following.assert_called_once_with("testuser", 50)
    
    def test_scan_profile_followers_fetch_failure(self, profile_scanner, mock_instagram_client):
        """Test scanning when followers fetch fails."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_followers.return_value = None  # Fetch failure
        mock_instagram_client.get_following.return_value = {"follow1"}
        
        result = profile_scanner.scan_profile("testuser")
        
        # Should still succeed but with empty followers
        assert result['success'] is True
        assert result['followers_count'] == 0
        assert result['following_count'] == 1
    
    def test_scan_profile_basic_info(self, profile_scanner, mock_instagram_client):
        """Test scanning basic profile info only."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        
        result = profile_scanner.scan_profile_basic_info("testuser")
        
        assert result['success'] is True
        assert result['username'] == "testuser"
        assert result['profile_info'] == profile_info
        
        # Should not have called followers/following methods
        mock_instagram_client.get_followers.assert_not_called()
        mock_instagram_client.get_following.assert_not_called()
    
    def test_validate_profile_access_public(self, profile_scanner, mock_instagram_client):
        """Test validating access to public profile."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        
        result = profile_scanner.validate_profile_access("testuser")
        
        assert result['success'] is True
        assert result['accessible'] is True
        assert result['access_level'] == 'public'
        assert result['is_private'] is False
    
    def test_validate_profile_access_private_accessible(self, profile_scanner, mock_instagram_client):
        """Test validating access to private profile that we can access."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=True
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_followers.return_value = {"user1"}  # Can access
        
        result = profile_scanner.validate_profile_access("testuser")
        
        assert result['success'] is True
        assert result['accessible'] is True
        assert result['access_level'] == 'private_accessible'
        assert result['is_private'] is True
    
    def test_validate_profile_access_private_no_access(self, profile_scanner, mock_instagram_client):
        """Test validating access to private profile that we cannot access."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=True
        )
        
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_followers.return_value = None  # Cannot access
        
        result = profile_scanner.validate_profile_access("testuser")
        
        assert result['success'] is True
        assert result['accessible'] is False
        assert result['access_level'] == 'private_no_access'
        assert result['is_private'] is True
    
    def test_scan_statistics(self, profile_scanner):
        """Test scanning statistics tracking."""
        # Initial stats should be zero
        stats = profile_scanner.get_scan_statistics()
        assert stats['total_scans'] == 0
        assert stats['successful_scans'] == 0
        assert stats['failed_scans'] == 0
        assert stats['success_rate'] == 0.0
        
        # Simulate successful scan
        profile_scanner._update_scan_stats("testuser", datetime.now(), True)
        
        stats = profile_scanner.get_scan_statistics()
        assert stats['total_scans'] == 1
        assert stats['successful_scans'] == 1
        assert stats['failed_scans'] == 0
        assert stats['success_rate'] == 1.0
        assert "testuser" in stats['profiles_scanned']
        
        # Simulate failed scan
        profile_scanner._update_scan_stats("failuser", datetime.now(), False)
        
        stats = profile_scanner.get_scan_statistics()
        assert stats['total_scans'] == 2
        assert stats['successful_scans'] == 1
        assert stats['failed_scans'] == 1
        assert stats['success_rate'] == 0.5
    
    def test_create_user_list(self, profile_scanner):
        """Test creating UserList objects."""
        usernames = {"user1", "user2", "user3"}
        
        user_list = profile_scanner.create_user_list(
            "testuser", usernames, "followers"
        )
        
        assert user_list.profile_username == "testuser"
        assert set(user_list.usernames) == usernames
        assert user_list.list_type == "followers"
        assert isinstance(user_list.retrieved_at, datetime)
    
    def test_invalid_username_handling(self, profile_scanner, mock_instagram_client):
        """Test handling of invalid usernames."""
        with pytest.raises(ValueError):
            profile_scanner.scan_profile("")  # Empty username
        
        with pytest.raises(ValueError):
            profile_scanner.scan_profile("user@invalid")  # Invalid characters
    
    def test_fetch_followers_with_invalid_usernames(self, profile_scanner, mock_instagram_client):
        """Test fetching followers with some invalid usernames."""
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        # Mock Instagram client to return some invalid usernames
        mock_instagram_client.get_profile_info.return_value = profile_info
        mock_instagram_client.get_followers.return_value = {"user1", "", "user@invalid", "user2"}
        mock_instagram_client.get_following.return_value = {"follow1"}
        
        with patch('services.profile_scanner.logger') as mock_logger:
            result = profile_scanner.scan_profile("testuser")
            
            # Should succeed but filter out invalid usernames
            assert result['success'] is True
            assert result['followers_count'] == 2  # Only user1 and user2
            
            # Should log warnings for invalid usernames
            mock_logger.warning.assert_called()