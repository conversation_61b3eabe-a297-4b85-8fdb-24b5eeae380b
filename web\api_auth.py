"""
API Authentication and Key Management for Instagram Follower Monitor.

This module provides API key generation, validation, and management functionality
for securing REST API endpoints.
"""

import secrets
import hashlib
import hmac
from typing import Optional
from datetime import datetime
import logging

from database.repositories import SettingsRepository

logger = logging.getLogger(__name__)

class APIKeyManager:
    """Manages API keys for REST API authentication."""
    
    def __init__(self):
        self.settings_repo = SettingsRepository()
    
    def generate_api_key(self) -> str:
        """Generate a new secure API key."""
        # Generate a 32-byte random key and encode as hex
        return secrets.token_hex(32)
    
    def store_api_key(self, api_key: str) -> bool:
        """Store API key securely in settings."""
        try:
            # Hash the API key before storing
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            # Store the hashed key and creation timestamp
            success1 = self.settings_repo.set_setting('api_key_hash', key_hash)
            success2 = self.settings_repo.set_setting('api_key_created', datetime.now().isoformat())
            
            return success1 and success2
        except Exception as e:
            logger.error(f"Failed to store API key: {e}")
            return False
    
    def validate_api_key(self, provided_key: str) -> bool:
        """Validate provided API key against stored hash."""
        try:
            stored_hash = self.settings_repo.get_setting('api_key_hash')
            if not stored_hash:
                return False
            
            # Hash the provided key and compare
            provided_hash = hashlib.sha256(provided_key.encode()).hexdigest()
            return hmac.compare_digest(stored_hash, provided_hash)
        
        except Exception as e:
            logger.error(f"Failed to validate API key: {e}")
            return False
    
    def has_api_key(self) -> bool:
        """Check if an API key is configured."""
        return self.settings_repo.get_setting('api_key_hash') is not None
    
    def get_api_key_info(self) -> Optional[dict]:
        """Get API key information (without the actual key)."""
        try:
            key_hash = self.settings_repo.get_setting('api_key_hash')
            created_at = self.settings_repo.get_setting('api_key_created')
            
            if not key_hash:
                return None
            
            return {
                'has_key': True,
                'created_at': created_at,
                'key_preview': key_hash[:8] + '...' if key_hash else None
            }
        
        except Exception as e:
            logger.error(f"Failed to get API key info: {e}")
            return None
    
    def revoke_api_key(self) -> bool:
        """Revoke the current API key."""
        try:
            success1 = self.settings_repo.delete_setting('api_key_hash')
            success2 = self.settings_repo.delete_setting('api_key_created')
            
            return success1 or success2  # Success if at least one was deleted
        
        except Exception as e:
            logger.error(f"Failed to revoke API key: {e}")
            return False
    
    def rotate_api_key(self) -> Optional[str]:
        """Generate and store a new API key, replacing the old one."""
        try:
            new_key = self.generate_api_key()
            
            if self.store_api_key(new_key):
                return new_key
            else:
                return None
        
        except Exception as e:
            logger.error(f"Failed to rotate API key: {e}")
            return None

# Global API key manager instance
api_key_manager = APIKeyManager()

def require_api_key():
    """Decorator factory for API key authentication."""
    def decorator(f):
        from functools import wraps
        from flask import request, jsonify
        
        @wraps(f)
        def decorated_function(*args, **kwargs):
            api_key = request.headers.get('X-API-Key')
            if not api_key:
                return jsonify({'error': 'API key required'}), 401
            
            if not api_key_manager.validate_api_key(api_key):
                return jsonify({'error': 'Invalid API key'}), 401
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def get_api_key_status() -> dict:
    """Get current API key status for dashboard display."""
    info = api_key_manager.get_api_key_info()
    if info:
        return {
            'configured': True,
            'created_at': info['created_at'],
            'key_preview': info['key_preview']
        }
    else:
        return {
            'configured': False,
            'created_at': None,
            'key_preview': None
        }