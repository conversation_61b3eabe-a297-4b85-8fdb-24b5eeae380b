"""
Tests for the backup and recovery system.

This module contains comprehensive tests for the BackupManager class and
related backup/recovery functionality.
"""

import pytest
import tempfile
import shutil
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from services.backup_manager import BackupManager, create_disaster_recovery_documentation
from config import Config
from models.data_models import MonitoringConfig, FollowerChange, ChangeType


class TestBackupManager:
    """Test cases for BackupManager class."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_config(self, temp_dir):
        """Create a test configuration."""
        config = Mock(spec=Config)
        config.ENCRYPTION_KEY = b'test_key_32_bytes_long_for_fernet'
        config.get_database_path.return_value = temp_dir / 'test.db'
        config.to_dict.return_value = {'test': 'config'}
        return config
    
    @pytest.fixture
    def backup_manager(self, test_config, temp_dir):
        """Create a BackupManager instance for testing."""
        with patch('services.backup_manager.Path') as mock_path:
            mock_path.return_value.parent.parent = temp_dir
            manager = BackupManager(test_config)
            manager.backup_dir = temp_dir / 'backups'
            manager.backup_dir.mkdir(exist_ok=True)
            return manager
    
    @pytest.fixture
    def sample_database(self, test_config):
        """Create a sample SQLite database for testing."""
        db_path = test_config.get_database_path()
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(str(db_path))
        conn.execute('''
            CREATE TABLE profiles (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                display_name TEXT,
                is_private BOOLEAN DEFAULT FALSE,
                monitoring_enabled BOOLEAN DEFAULT TRUE,
                last_scan TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.execute('''
            CREATE TABLE settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.execute('''
            CREATE TABLE follower_changes (
                id INTEGER PRIMARY KEY,
                profile_id INTEGER,
                username TEXT NOT NULL,
                change_type TEXT CHECK(change_type IN ('gained', 'lost')),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (profile_id) REFERENCES profiles(id)
            )
        ''')
        
        # Insert sample data
        conn.execute("INSERT INTO profiles (username, display_name) VALUES (?, ?)", 
                    ("test_user", "Test User"))
        conn.execute("INSERT INTO settings (key, value) VALUES (?, ?)", 
                    ("test_setting", "test_value"))
        conn.commit()
        conn.close()
        
        return db_path
    
    def test_backup_manager_initialization(self, test_config, temp_dir):
        """Test BackupManager initialization."""
        with patch('services.backup_manager.Path') as mock_path:
            mock_path.return_value.parent.parent = temp_dir
            manager = BackupManager(test_config)
            
            assert manager.config == test_config
            assert manager.cipher is not None
            assert manager.profile_repo is not None
            assert manager.change_repo is not None
            assert manager.settings_repo is not None
    
    @patch('services.backup_manager.db_manager')
    def test_create_full_backup_success(self, mock_db_manager, backup_manager, sample_database):
        """Test successful full backup creation."""
        # Mock database operations
        mock_db_manager.get_connection.return_value.__enter__.return_value = Mock()
        
        with patch.object(backup_manager, '_backup_database') as mock_backup_db, \
             patch.object(backup_manager, '_backup_configuration') as mock_backup_config, \
             patch.object(backup_manager, '_export_data') as mock_export_data, \
             patch.object(backup_manager, '_encrypt_backup') as mock_encrypt:
            
            mock_backup_db.return_value = Path('/fake/db/path')
            mock_backup_config.return_value = Path('/fake/config/path')
            mock_export_data.return_value = Path('/fake/export/path')
            mock_encrypt.return_value = Path('/fake/encrypted/path')
            
            result = backup_manager.create_full_backup("test_backup")
            
            assert result['success'] is True
            assert 'backup_path' in result
            assert 'backup_info' in result
            assert result['backup_info']['type'] == 'full'
            assert result['backup_info']['status'] == 'completed'
    
    def test_create_full_backup_failure(self, backup_manager):
        """Test full backup creation failure."""
        with patch.object(backup_manager, '_backup_database', side_effect=Exception("Test error")):
            result = backup_manager.create_full_backup("test_backup")
            
            assert result['success'] is False
            assert 'error' in result
            assert "Test error" in result['error']
    
    def test_create_incremental_backup(self, backup_manager):
        """Test incremental backup creation."""
        since_date = datetime.now() - timedelta(days=7)
        
        with patch.object(backup_manager, '_export_incremental_data') as mock_export, \
             patch.object(backup_manager, '_backup_configuration') as mock_config, \
             patch.object(backup_manager, '_encrypt_backup') as mock_encrypt:
            
            mock_export.return_value = Path('/fake/export/path')
            mock_config.return_value = Path('/fake/config/path')
            mock_encrypt.return_value = Path('/fake/encrypted/path')
            
            result = backup_manager.create_incremental_backup(since_date, "test_incremental")
            
            assert result['success'] is True
            assert result['backup_info']['type'] == 'incremental'
            assert result['backup_info']['since_date'] == since_date.isoformat()
    
    def test_validate_database_integrity_valid(self, backup_manager, sample_database):
        """Test database integrity validation with valid database."""
        with patch('services.backup_manager.db_manager') as mock_db_manager:
            # Mock successful integrity check
            mock_conn = Mock()
            mock_cursor = Mock()
            mock_cursor.fetchone.return_value = ['ok']
            mock_cursor.fetchall.return_value = []  # No foreign key violations
            mock_conn.execute.return_value = mock_cursor
            mock_db_manager.get_connection.return_value.__enter__.return_value = mock_conn
            mock_db_manager.execute_query.return_value = [{'count': 0}]
            
            backup_manager.config.get_database_path.return_value = sample_database
            
            with patch.object(backup_manager, '_validate_data_consistency') as mock_validate, \
                 patch.object(backup_manager, '_collect_database_statistics') as mock_stats:
                
                mock_validate.return_value = {
                    'valid': True,
                    'errors': [],
                    'warnings': [],
                    'checks_performed': ['data_consistency']
                }
                mock_stats.return_value = {'profiles_count': 1}
                
                result = backup_manager.validate_database_integrity()
                
                assert result['valid'] is True
                assert 'sqlite_integrity' in result['checks_performed']
                assert 'foreign_key_constraints' in result['checks_performed']
    
    def test_validate_database_integrity_invalid(self, backup_manager, sample_database):
        """Test database integrity validation with invalid database."""
        with patch('services.backup_manager.db_manager') as mock_db_manager:
            # Mock failed integrity check
            mock_conn = Mock()
            mock_cursor = Mock()
            mock_cursor.fetchone.return_value = ['corruption detected']
            mock_conn.execute.return_value = mock_cursor
            mock_db_manager.get_connection.return_value.__enter__.return_value = mock_conn
            
            backup_manager.config.get_database_path.return_value = sample_database
            
            result = backup_manager.validate_database_integrity()
            
            assert result['valid'] is False
            assert len(result['errors']) > 0
    
    def test_export_profile_data_success(self, backup_manager):
        """Test successful profile data export."""
        # Mock profile and data
        mock_profile = Mock()
        mock_profile.profile_username = "test_user"
        mock_profile.display_name = "Test User"
        mock_profile.is_private = False
        mock_profile.enabled = True
        mock_profile.last_scan = datetime.now()
        mock_profile.created_at = datetime.now()
        mock_profile.profile_id = 1
        
        with patch.object(backup_manager.profile_repo, 'get_profile_by_username', return_value=mock_profile), \
             patch.object(backup_manager.profile_repo, 'get_profile_stats', return_value={}), \
             patch.object(backup_manager.change_repo, 'get_current_followers', return_value=set()), \
             patch.object(backup_manager.change_repo, 'get_current_following', return_value=set()), \
             patch.object(backup_manager.change_repo, 'get_recent_changes', return_value=[]):
            
            result = backup_manager.export_profile_data("test_user")
            
            assert result['success'] is True
            assert 'output_path' in result
            assert 'records_exported' in result
    
    def test_export_profile_data_not_found(self, backup_manager):
        """Test profile data export for non-existent profile."""
        with patch.object(backup_manager.profile_repo, 'get_profile_by_username', return_value=None):
            result = backup_manager.export_profile_data("nonexistent_user")
            
            assert result['success'] is False
            assert "not found" in result['error']
    
    def test_import_profile_data_success(self, backup_manager, temp_dir):
        """Test successful profile data import."""
        # Create test import file
        import_data = {
            'profile': {
                'username': 'imported_user',
                'display_name': 'Imported User',
                'is_private': False,
                'monitoring_enabled': True
            },
            'current_followers': ['follower1', 'follower2'],
            'current_following': ['following1'],
            'change_history': []
        }
        
        import_file = temp_dir / 'test_import.json'
        with open(import_file, 'w') as f:
            json.dump(import_data, f)
        
        with patch.object(backup_manager.profile_repo, 'get_profile_by_username', return_value=None), \
             patch.object(backup_manager.profile_repo, 'create_profile', return_value=1), \
             patch.object(backup_manager.change_repo, 'store_current_followers'), \
             patch.object(backup_manager.change_repo, 'store_current_following'), \
             patch.object(backup_manager.change_repo, 'store_follower_changes'):
            
            result = backup_manager.import_profile_data(str(import_file))
            
            assert result['success'] is True
            assert result['profile_username'] == 'imported_user'
    
    def test_import_profile_data_existing_profile(self, backup_manager, temp_dir):
        """Test profile data import with existing profile (no overwrite)."""
        import_data = {
            'profile': {
                'username': 'existing_user',
                'display_name': 'Existing User',
                'is_private': False,
                'monitoring_enabled': True
            },
            'current_followers': [],
            'current_following': [],
            'change_history': []
        }
        
        import_file = temp_dir / 'test_import.json'
        with open(import_file, 'w') as f:
            json.dump(import_data, f)
        
        # Mock existing profile
        mock_profile = Mock()
        mock_profile.profile_username = 'existing_user'
        
        with patch.object(backup_manager.profile_repo, 'get_profile_by_username', return_value=mock_profile):
            result = backup_manager.import_profile_data(str(import_file), overwrite_existing=False)
            
            assert result['success'] is False
            assert "already exists" in result['error']
    
    def test_cleanup_old_backups(self, backup_manager, temp_dir):
        """Test cleanup of old backup files."""
        # Create some test backup files with different ages
        old_backup = backup_manager.backup_dir / 'old_backup.backup'
        recent_backup = backup_manager.backup_dir / 'recent_backup.backup'
        
        old_backup.touch()
        recent_backup.touch()
        
        # Set modification times
        old_time = (datetime.now() - timedelta(days=35)).timestamp()
        recent_time = datetime.now().timestamp()
        
        old_backup.stat = Mock()
        old_backup.stat.return_value.st_mtime = old_time
        old_backup.stat.return_value.st_size = 1024
        
        recent_backup.stat = Mock()
        recent_backup.stat.return_value.st_mtime = recent_time
        recent_backup.stat.return_value.st_size = 2048
        
        with patch.object(backup_manager.backup_dir, 'glob') as mock_glob:
            mock_glob.return_value = [old_backup, recent_backup]
            
            result = backup_manager.cleanup_old_backups(retention_days=30)
            
            assert result['success'] is True
            # Should delete old backup but keep recent one
            assert len(result['deleted_files']) >= 0  # Depends on mock behavior
    
    def test_list_available_backups(self, backup_manager, temp_dir):
        """Test listing available backup files."""
        # Create a test backup with manifest
        backup_dir = backup_manager.backup_dir / 'test_backup'
        backup_dir.mkdir()
        
        manifest_data = {
            'backup_name': 'test_backup',
            'timestamp': '2024-01-01T12:00:00',
            'type': 'full',
            'status': 'completed'
        }
        
        manifest_file = backup_dir / 'backup_manifest.json'
        with open(manifest_file, 'w') as f:
            json.dump(manifest_data, f)
        
        # Create encrypted backup file
        backup_file = backup_manager.backup_dir / 'test_backup.backup'
        backup_file.touch()
        
        with patch.object(backup_manager, '_decrypt_backup', return_value=backup_dir):
            result = backup_manager.list_available_backups()
            
            assert len(result) >= 0  # May be empty due to mocking
    
    def test_schedule_automated_backup(self, backup_manager):
        """Test scheduling automated backups."""
        with patch('services.backup_manager.scheduler_manager') as mock_scheduler:
            mock_scheduler.add_job.return_value = 'job_123'
            
            result = backup_manager.schedule_automated_backup(interval_hours=12)
            
            assert result['success'] is True
            assert result['interval_hours'] == 12
            assert 'job_id' in result
    
    def test_automated_backup_job(self, backup_manager):
        """Test the automated backup job function."""
        with patch.object(backup_manager, 'create_full_backup') as mock_create, \
             patch.object(backup_manager, 'cleanup_old_backups') as mock_cleanup, \
             patch.object(backup_manager.settings_repo, 'get_setting', return_value='30'):
            
            mock_create.return_value = {'success': True, 'backup_path': '/fake/path'}
            mock_cleanup.return_value = {'success': True, 'deleted_files': []}
            
            # Should not raise any exceptions
            backup_manager._automated_backup_job()
            
            mock_create.assert_called_once()
            mock_cleanup.assert_called_once_with(30)
    
    def test_calculate_checksum(self, backup_manager, temp_dir):
        """Test checksum calculation."""
        test_file = temp_dir / 'test_file.txt'
        test_content = b'test content for checksum'
        
        with open(test_file, 'wb') as f:
            f.write(test_content)
        
        checksum = backup_manager._calculate_checksum(test_file)
        
        assert isinstance(checksum, str)
        assert len(checksum) == 64  # SHA-256 hex digest length
    
    def test_validate_data_consistency(self, backup_manager):
        """Test data consistency validation."""
        with patch('services.backup_manager.db_manager') as mock_db_manager:
            # Mock query results
            mock_db_manager.execute_query.side_effect = [
                [{'count': 0}],  # orphaned followers
                [{'count': 0}],  # orphaned following
                [],              # duplicate profiles
                [{'count': 0}]   # invalid timestamps
            ]
            
            result = backup_manager._validate_data_consistency()
            
            assert result['valid'] is True
            assert len(result['checks_performed']) > 0
    
    def test_collect_database_statistics(self, backup_manager, sample_database):
        """Test database statistics collection."""
        with patch('services.backup_manager.db_manager') as mock_db_manager:
            mock_db_manager.execute_query.side_effect = [
                [{'count': 1}],  # profiles
                [{'count': 10}], # current_followers
                [{'count': 5}],  # current_following
                [{'count': 20}], # follower_changes
                [{'count': 15}], # following_changes
                [{'count': 3}],  # settings
                [{'oldest': '2024-01-01T00:00:00'}],  # oldest change
                [{'newest': '2024-01-31T23:59:59'}],  # newest change
                [{'count': 1}]   # active profiles
            ]
            
            backup_manager.config.get_database_path.return_value = sample_database
            
            result = backup_manager._collect_database_statistics()
            
            assert 'profiles_count' in result
            assert 'database_size_bytes' in result
            assert 'active_profiles' in result


class TestDisasterRecoveryDocumentation:
    """Test cases for disaster recovery documentation."""
    
    def test_create_disaster_recovery_documentation(self, temp_dir):
        """Test creation of disaster recovery documentation."""
        with patch('services.backup_manager.Path') as mock_path:
            mock_path.return_value.parent.parent = temp_dir
            
            doc_path = create_disaster_recovery_documentation()
            
            assert isinstance(doc_path, str)
            # In real scenario, would check if file exists and has content


class TestBackupIntegration:
    """Integration tests for backup functionality."""
    
    @pytest.fixture
    def integration_config(self, temp_dir):
        """Create integration test configuration."""
        config = Config()
        # Override paths for testing
        config.DATABASE_PATH = temp_dir / 'integration_test.db'
        return config
    
    def test_full_backup_restore_cycle(self, integration_config, temp_dir):
        """Test complete backup and restore cycle."""
        # This would be a more comprehensive integration test
        # that creates real data, backs it up, modifies data,
        # then restores and verifies the restoration
        pass


if __name__ == '__main__':
    pytest.main([__file__])