#!/usr/bin/env python3
"""
Demo script for backup and recovery functionality.

This script demonstrates the key features of the backup and recovery system
including creating backups, validating integrity, and restoring data.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from services.backup_manager import BackupManager, create_disaster_recovery_documentation
from config import Config
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def demo_backup_operations():
    """Demonstrate backup and recovery operations."""
    print("=" * 60)
    print("Instagram Follower Monitor - Backup System Demo")
    print("=" * 60)
    
    try:
        # Initialize configuration and backup manager
        config = Config()
        backup_manager = BackupManager(config)
        
        print(f"\n1. Backup Manager Initialized")
        print(f"   Backup Directory: {backup_manager.backup_dir}")
        print(f"   Database Path: {config.get_database_path()}")
        
        # Validate database integrity
        print(f"\n2. Validating Database Integrity...")
        integrity_result = backup_manager.validate_database_integrity()
        
        if integrity_result['valid']:
            print("   ✅ Database integrity check PASSED")
            print(f"   Checks performed: {', '.join(integrity_result['checks_performed'])}")
            
            if integrity_result.get('statistics'):
                print("   Database Statistics:")
                for key, value in integrity_result['statistics'].items():
                    if key.endswith('_size_bytes'):
                        print(f"     {key}: {value / 1024 / 1024:.2f} MB")
                    else:
                        print(f"     {key}: {value}")
        else:
            print("   ❌ Database integrity check FAILED")
            print(f"   Errors: {integrity_result.get('errors', [])}")
            if integrity_result.get('warnings'):
                print(f"   Warnings: {integrity_result.get('warnings', [])}")
        
        # List existing backups
        print(f"\n3. Listing Available Backups...")
        backups = backup_manager.list_available_backups()
        
        if backups:
            print(f"   Found {len(backups)} backup(s):")
            for backup in backups[:5]:  # Show first 5
                size_mb = backup.get('file_size', 0) / 1024 / 1024
                print(f"     - {backup.get('backup_name', 'Unknown')}")
                print(f"       Type: {backup.get('type', 'Unknown')}")
                print(f"       Date: {backup.get('timestamp', 'Unknown')[:19]}")
                print(f"       Size: {size_mb:.2f} MB")
        else:
            print("   No existing backups found")
        
        # Create a new full backup
        print(f"\n4. Creating Full Backup...")
        backup_name = f"demo_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_result = backup_manager.create_full_backup(backup_name)
        
        if backup_result['success']:
            print("   ✅ Full backup created successfully!")
            print(f"   Backup Path: {backup_result['backup_path']}")
            print(f"   Backup Info: {backup_result['backup_info']['type']} backup")
            print(f"   Timestamp: {backup_result['backup_info']['timestamp']}")
        else:
            print("   ❌ Full backup creation failed!")
            print(f"   Error: {backup_result.get('error', 'Unknown error')}")
        
        # Create an incremental backup
        print(f"\n5. Creating Incremental Backup...")
        since_date = datetime.now() - timedelta(days=7)
        incremental_name = f"demo_incremental_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        incremental_result = backup_manager.create_incremental_backup(since_date, incremental_name)
        
        if incremental_result['success']:
            print("   ✅ Incremental backup created successfully!")
            print(f"   Backup Path: {incremental_result['backup_path']}")
            print(f"   Since Date: {since_date.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("   ❌ Incremental backup creation failed!")
            print(f"   Error: {incremental_result.get('error', 'Unknown error')}")
        
        # Schedule automated backups
        print(f"\n6. Scheduling Automated Backups...")
        schedule_result = backup_manager.schedule_automated_backup(interval_hours=24)
        
        if schedule_result['success']:
            print("   ✅ Automated backup scheduled successfully!")
            print(f"   Interval: {schedule_result['interval_hours']} hours")
            print(f"   Job ID: {schedule_result.get('job_id', 'Unknown')}")
        else:
            print("   ❌ Automated backup scheduling failed!")
            print(f"   Error: {schedule_result.get('error', 'Unknown error')}")
        
        # Cleanup old backups (demo with 0 days to show functionality)
        print(f"\n7. Demonstrating Backup Cleanup...")
        print("   (This would normally clean up backups older than specified days)")
        cleanup_result = backup_manager.cleanup_old_backups(retention_days=365)  # Keep all for demo
        
        if cleanup_result['success']:
            print("   ✅ Backup cleanup completed!")
            print(f"   Files deleted: {len(cleanup_result['deleted_files'])}")
            print(f"   Space freed: {cleanup_result['total_size_freed']:,} bytes")
        else:
            print("   ❌ Backup cleanup failed!")
            print(f"   Error: {cleanup_result.get('error', 'Unknown error')}")
        
        # Create disaster recovery documentation
        print(f"\n8. Creating Disaster Recovery Documentation...")
        doc_path = create_disaster_recovery_documentation()
        print(f"   ✅ Documentation created: {doc_path}")
        
        # Summary
        print(f"\n" + "=" * 60)
        print("Demo Summary:")
        print(f"✅ Database integrity validation")
        print(f"✅ Backup listing and management")
        print(f"✅ Full backup creation")
        print(f"✅ Incremental backup creation")
        print(f"✅ Automated backup scheduling")
        print(f"✅ Backup cleanup procedures")
        print(f"✅ Disaster recovery documentation")
        print("=" * 60)
        
        print(f"\nBackup system is fully operational!")
        print(f"Web interface available at: /backup")
        print(f"CLI tool available: python backup_cli.py --help")
        
    except Exception as e:
        logger.error(f"Demo failed with error: {e}")
        print(f"\n❌ Demo failed: {e}")
        return 1
    
    return 0


def demo_profile_export_import():
    """Demonstrate profile data export and import."""
    print(f"\n" + "=" * 60)
    print("Profile Data Export/Import Demo")
    print("=" * 60)
    
    try:
        config = Config()
        backup_manager = BackupManager(config)
        
        # This would normally export real profile data
        print("Profile export/import functionality is available through:")
        print("- Web interface: /backup/export/<username>")
        print("- Web interface: /backup/import")
        print("- CLI tool: python backup_cli.py export <username>")
        print("- CLI tool: python backup_cli.py import <file_path>")
        
    except Exception as e:
        logger.error(f"Profile demo failed: {e}")
        print(f"❌ Profile demo failed: {e}")


if __name__ == '__main__':
    print("Starting Instagram Follower Monitor Backup System Demo...")
    
    # Run main backup demo
    result = demo_backup_operations()
    
    # Run profile demo
    demo_profile_export_import()
    
    print(f"\nDemo completed with exit code: {result}")
    sys.exit(result)