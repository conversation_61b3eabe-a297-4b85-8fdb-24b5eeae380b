"""Database repository classes for data access operations."""

from typing import List, Optional, Set, Dict, Any
from datetime import datetime, timedelta
import logging

from models.data_models import (
    ProfileInfo, FollowerChange, MonitoringConfig, 
    UserList, ChangeType, validate_username
)
from database.connection import db_manager
from web.security import SQLInjectionPrevention

logger = logging.getLogger(__name__)


class ProfileRepository:
    """Repository for profile-related database operations."""
    
    def create_profile(self, config: MonitoringConfig) -> int:
        """Create a new profile and return its ID."""
        query = """
            INSERT INTO profiles (username, display_name, is_private, monitoring_enabled)
            VALUES (?, ?, ?, ?)
        """
        
        with db_manager.get_connection() as conn:
            cursor = conn.execute(query, (
                config.profile_username,
                config.display_name,
                config.is_private,
                config.enabled
            ))
            conn.commit()
            return cursor.lastrowid
    
    def get_profile_by_username(self, username: str) -> Optional[MonitoringConfig]:
        """Get profile configuration by username."""
        username = validate_username(username)
        
        query = """
            SELECT id, username, display_name, is_private, monitoring_enabled,
                   last_scan, created_at
            FROM profiles
            WHERE username = ?
        """
        
        rows = db_manager.execute_query(query, (username,))
        if not rows:
            return None
        
        row = rows[0]
        return MonitoringConfig(
            profile_username=row['username'],
            enabled=bool(row['monitoring_enabled']),
            profile_id=row['id'],
            display_name=row['display_name'],
            is_private=bool(row['is_private']),
            last_scan=datetime.fromisoformat(row['last_scan']) if row['last_scan'] else None,
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
        )
    
    def get_all_profiles(self) -> List[MonitoringConfig]:
        """Get all profile configurations."""
        query = """
            SELECT id, username, display_name, is_private, monitoring_enabled,
                   last_scan, created_at
            FROM profiles
            ORDER BY created_at DESC
        """
        
        rows = db_manager.execute_query(query)
        profiles = []
        
        for row in rows:
            profiles.append(MonitoringConfig(
                profile_username=row['username'],
                enabled=bool(row['monitoring_enabled']),
                profile_id=row['id'],
                display_name=row['display_name'],
                is_private=bool(row['is_private']),
                last_scan=datetime.fromisoformat(row['last_scan']) if row['last_scan'] else None,
                created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
            ))
        
        return profiles
    
    def get_enabled_profiles(self) -> List[MonitoringConfig]:
        """Get all enabled profile configurations."""
        query = """
            SELECT id, username, display_name, is_private, monitoring_enabled,
                   last_scan, created_at
            FROM profiles
            WHERE monitoring_enabled = 1
            ORDER BY last_scan ASC NULLS FIRST
        """
        
        rows = db_manager.execute_query(query)
        profiles = []
        
        for row in rows:
            profiles.append(MonitoringConfig(
                profile_username=row['username'],
                enabled=bool(row['monitoring_enabled']),
                profile_id=row['id'],
                display_name=row['display_name'],
                is_private=bool(row['is_private']),
                last_scan=datetime.fromisoformat(row['last_scan']) if row['last_scan'] else None,
                created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
            ))
        
        return profiles
    
    def update_profile(self, config: MonitoringConfig) -> bool:
        """Update profile configuration."""
        if not config.profile_id:
            return False
        
        query = """
            UPDATE profiles
            SET display_name = ?, is_private = ?, monitoring_enabled = ?, last_scan = ?
            WHERE id = ?
        """
        
        last_scan_str = config.last_scan.isoformat() if config.last_scan else None
        affected_rows = db_manager.execute_update(query, (
            config.display_name,
            config.is_private,
            config.enabled,
            last_scan_str,
            config.profile_id
        ))
        
        return affected_rows > 0
    
    def delete_profile(self, username: str) -> bool:
        """Delete profile and all associated data."""
        username = validate_username(username)
        
        query = "DELETE FROM profiles WHERE username = ?"
        affected_rows = db_manager.execute_update(query, (username,))
        
        return affected_rows > 0
    
    def get_profile_stats(self, profile_id: int) -> Dict[str, Any]:
        """Get comprehensive statistics for a profile."""
        stats = {}
        
        # Get current follower/following counts
        follower_count_query = "SELECT COUNT(*) as count FROM current_followers WHERE profile_id = ?"
        following_count_query = "SELECT COUNT(*) as count FROM current_following WHERE profile_id = ?"
        
        follower_result = db_manager.execute_query(follower_count_query, (profile_id,))
        following_result = db_manager.execute_query(following_count_query, (profile_id,))
        
        stats['current_followers'] = follower_result[0]['count'] if follower_result else 0
        stats['current_following'] = following_result[0]['count'] if following_result else 0
        
        # Get change counts for last 30 days
        thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
        
        follower_changes_query = """
            SELECT 
                SUM(CASE WHEN change_type = 'gained' THEN 1 ELSE 0 END) as gained,
                SUM(CASE WHEN change_type = 'lost' THEN 1 ELSE 0 END) as lost
            FROM follower_changes 
            WHERE profile_id = ? AND timestamp >= ?
        """
        
        following_changes_query = """
            SELECT 
                SUM(CASE WHEN change_type = 'started_following' THEN 1 ELSE 0 END) as started,
                SUM(CASE WHEN change_type = 'stopped_following' THEN 1 ELSE 0 END) as stopped
            FROM following_changes 
            WHERE profile_id = ? AND timestamp >= ?
        """
        
        follower_changes_result = db_manager.execute_query(follower_changes_query, (profile_id, thirty_days_ago))
        following_changes_result = db_manager.execute_query(following_changes_query, (profile_id, thirty_days_ago))
        
        if follower_changes_result:
            stats['followers_gained_30d'] = follower_changes_result[0]['gained'] or 0
            stats['followers_lost_30d'] = follower_changes_result[0]['lost'] or 0
        else:
            stats['followers_gained_30d'] = 0
            stats['followers_lost_30d'] = 0
        
        if following_changes_result:
            stats['following_started_30d'] = following_changes_result[0]['started'] or 0
            stats['following_stopped_30d'] = following_changes_result[0]['stopped'] or 0
        else:
            stats['following_started_30d'] = 0
            stats['following_stopped_30d'] = 0
        
        return stats
    
    def bulk_update_last_scan(self, profile_updates: List[tuple]) -> int:
        """Bulk update last_scan timestamps for multiple profiles.
        
        Args:
            profile_updates: List of (profile_id, last_scan_datetime) tuples
        
        Returns:
            Number of profiles updated
        """
        if not profile_updates:
            return 0
        
        query = "UPDATE profiles SET last_scan = ? WHERE id = ?"
        update_data = [(scan_time.isoformat(), profile_id) for profile_id, scan_time in profile_updates]
        
        return db_manager.execute_many(query, update_data)


class FollowerRepository:
    """Repository for follower/following data operations."""
    
    def store_current_followers(self, profile_id: int, followers: Set[str]) -> None:
        """Store current followers list, replacing existing data."""
        with db_manager.get_connection() as conn:
            # Clear existing followers
            conn.execute("DELETE FROM current_followers WHERE profile_id = ?", (profile_id,))
            
            # Insert new followers
            if followers:
                follower_data = [(profile_id, username) for username in followers]
                conn.executemany(
                    "INSERT INTO current_followers (profile_id, follower_username) VALUES (?, ?)",
                    follower_data
                )
            
            conn.commit()
    
    def store_current_following(self, profile_id: int, following: Set[str]) -> None:
        """Store current following list, replacing existing data."""
        with db_manager.get_connection() as conn:
            # Clear existing following
            conn.execute("DELETE FROM current_following WHERE profile_id = ?", (profile_id,))
            
            # Insert new following
            if following:
                following_data = [(profile_id, username) for username in following]
                conn.executemany(
                    "INSERT INTO current_following (profile_id, following_username) VALUES (?, ?)",
                    following_data
                )
            
            conn.commit()
    
    def get_current_followers(self, profile_id: int) -> Set[str]:
        """Get current followers for a profile."""
        query = "SELECT follower_username FROM current_followers WHERE profile_id = ?"
        rows = db_manager.execute_query(query, (profile_id,))
        return {row['follower_username'] for row in rows}
    
    def get_current_following(self, profile_id: int) -> Set[str]:
        """Get current following for a profile."""
        query = "SELECT following_username FROM current_following WHERE profile_id = ?"
        rows = db_manager.execute_query(query, (profile_id,))
        return {row['following_username'] for row in rows}
    
    def get_follower_history(self, profile_id: int, username: str) -> List[Dict[str, Any]]:
        """Get the history of a specific follower for a profile."""
        username = validate_username(username)
        
        query = """
            SELECT change_type, timestamp
            FROM follower_changes
            WHERE profile_id = ? AND username = ?
            ORDER BY timestamp DESC
        """
        
        rows = db_manager.execute_query(query, (profile_id, username))
        return [{'change_type': row['change_type'], 'timestamp': datetime.fromisoformat(row['timestamp'])} for row in rows]
    
    def get_following_history(self, profile_id: int, username: str) -> List[Dict[str, Any]]:
        """Get the history of a specific following for a profile."""
        username = validate_username(username)
        
        query = """
            SELECT change_type, timestamp
            FROM following_changes
            WHERE profile_id = ? AND username = ?
            ORDER BY timestamp DESC
        """
        
        rows = db_manager.execute_query(query, (profile_id, username))
        return [{'change_type': row['change_type'], 'timestamp': datetime.fromisoformat(row['timestamp'])} for row in rows]
    
    def bulk_store_followers(self, profile_followers_map: Dict[int, Set[str]]) -> None:
        """Bulk store current followers for multiple profiles efficiently."""
        if not profile_followers_map:
            return
        
        with db_manager.get_connection() as conn:
            # Clear existing followers for all profiles
            profile_ids = list(profile_followers_map.keys())
            placeholders = ','.join('?' * len(profile_ids))
            conn.execute(f"DELETE FROM current_followers WHERE profile_id IN ({placeholders})", profile_ids)
            
            # Prepare bulk insert data
            follower_data = []
            for profile_id, followers in profile_followers_map.items():
                for username in followers:
                    follower_data.append((profile_id, username))
            
            # Bulk insert new followers
            if follower_data:
                conn.executemany(
                    "INSERT INTO current_followers (profile_id, follower_username) VALUES (?, ?)",
                    follower_data
                )
            
            conn.commit()
    
    def bulk_store_following(self, profile_following_map: Dict[int, Set[str]]) -> None:
        """Bulk store current following for multiple profiles efficiently."""
        if not profile_following_map:
            return
        
        with db_manager.get_connection() as conn:
            # Clear existing following for all profiles
            profile_ids = list(profile_following_map.keys())
            placeholders = ','.join('?' * len(profile_ids))
            conn.execute(f"DELETE FROM current_following WHERE profile_id IN ({placeholders})", profile_ids)
            
            # Prepare bulk insert data
            following_data = []
            for profile_id, following in profile_following_map.items():
                for username in following:
                    following_data.append((profile_id, username))
            
            # Bulk insert new following
            if following_data:
                conn.executemany(
                    "INSERT INTO current_following (profile_id, following_username) VALUES (?, ?)",
                    following_data
                )
            
            conn.commit()


class SettingsRepository:
    """Repository for system settings operations."""
    
    def get_setting(self, key: str) -> Optional[str]:
        """Get a setting value by key."""
        query = "SELECT value FROM settings WHERE key = ?"
        rows = db_manager.execute_query(query, (key,))
        return rows[0]['value'] if rows else None
    
    def set_setting(self, key: str, value: str) -> bool:
        """Set a setting value."""
        query = """
            INSERT OR REPLACE INTO settings (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        """
        affected_rows = db_manager.execute_update(query, (key, value))
        return affected_rows > 0
    
    def get_all_settings(self) -> Dict[str, str]:
        """Get all settings as a dictionary."""
        query = "SELECT key, value FROM settings"
        rows = db_manager.execute_query(query)
        return {row['key']: row['value'] for row in rows}
    
    def delete_setting(self, key: str) -> bool:
        """Delete a setting."""
        query = "DELETE FROM settings WHERE key = ?"
        affected_rows = db_manager.execute_update(query, (key,))
        return affected_rows > 0
    
    def get_monitoring_settings(self) -> Dict[str, Any]:
        """Get monitoring-specific settings with defaults."""
        settings = self.get_all_settings()
        return {
            'monitoring_interval_hours': int(settings.get('monitoring_interval_hours', '2')),
            'data_retention_days': int(settings.get('data_retention_days', '365')),
            'min_request_delay': float(settings.get('min_request_delay', '1.0')),
            'max_request_delay': float(settings.get('max_request_delay', '3.0')),
            'max_retries': int(settings.get('max_retries', '3')),
            'profile_processing_delay': float(settings.get('profile_processing_delay', '5.0')),
        }
    
    def update_monitoring_settings(self, settings: Dict[str, Any]) -> bool:
        """Update monitoring settings."""
        try:
            for key, value in settings.items():
                if not self.set_setting(key, str(value)):
                    return False
            return True
        except Exception as e:
            logger.error(f"Failed to update monitoring settings: {e}")
            return False
    
    def export_settings(self) -> Dict[str, Any]:
        """Export all settings for backup."""
        settings = self.get_all_settings()
        return {
            'settings': settings,
            'exported_at': datetime.now().isoformat(),
            'version': '1.0'
        }
    
    def import_settings(self, settings_data: Dict[str, Any]) -> bool:
        """Import settings from backup."""
        try:
            if 'settings' not in settings_data:
                return False
            
            for key, value in settings_data['settings'].items():
                if not self.set_setting(key, value):
                    return False
            
            return True
        except Exception as e:
            logger.error(f"Failed to import settings: {e}")
            return False


class ChangeRepository:
    """Repository for change tracking operations."""
    
    def store_follower_changes(self, changes: List[FollowerChange]) -> None:
        """Store follower changes in the database."""
        if not changes:
            return
        
        follower_changes = []
        following_changes = []
        
        for change in changes:
            if change.is_follower_change:
                follower_changes.append((
                    change.profile_id,
                    change.affected_username,
                    change.change_type.value,
                    change.timestamp.isoformat()
                ))
            elif change.is_following_change:
                following_changes.append((
                    change.profile_id,
                    change.affected_username,
                    change.change_type.value,
                    change.timestamp.isoformat()
                ))
        
        with db_manager.get_connection() as conn:
            if follower_changes:
                conn.executemany(
                    "INSERT INTO follower_changes (profile_id, username, change_type, timestamp) VALUES (?, ?, ?, ?)",
                    follower_changes
                )
            
            if following_changes:
                conn.executemany(
                    "INSERT INTO following_changes (profile_id, username, change_type, timestamp) VALUES (?, ?, ?, ?)",
                    following_changes
                )
            
            conn.commit()
    
    def get_recent_changes(self, profile_id: Optional[int] = None, limit: int = 100) -> List[FollowerChange]:
        """Get recent changes, optionally filtered by profile."""
        changes = []
        
        # Build unified query to get all changes sorted by timestamp
        base_conditions = []
        params = []
        
        if profile_id:
            base_conditions.append("profile_id = ?")
            params.append(profile_id)
        
        where_clause = " WHERE " + " AND ".join(base_conditions) if base_conditions else ""
        
        # Get follower changes
        follower_query = f"""
            SELECT fc.profile_id, p.username as profile_username, fc.username, fc.change_type, fc.timestamp
            FROM follower_changes fc
            JOIN profiles p ON fc.profile_id = p.id
            {where_clause}
        """
        
        follower_rows = db_manager.execute_query(follower_query, tuple(params))
        
        for row in follower_rows:
            changes.append(FollowerChange(
                profile_username=row['profile_username'],
                affected_username=row['username'],
                change_type=ChangeType(row['change_type']),
                timestamp=datetime.fromisoformat(row['timestamp']),
                profile_id=row['profile_id']
            ))
        
        # Get following changes
        following_query = f"""
            SELECT fc.profile_id, p.username as profile_username, fc.username, fc.change_type, fc.timestamp
            FROM following_changes fc
            JOIN profiles p ON fc.profile_id = p.id
            {where_clause}
        """
        
        following_rows = db_manager.execute_query(following_query, tuple(params))
        
        for row in following_rows:
            changes.append(FollowerChange(
                profile_username=row['profile_username'],
                affected_username=row['username'],
                change_type=ChangeType(row['change_type']),
                timestamp=datetime.fromisoformat(row['timestamp']),
                profile_id=row['profile_id']
            ))
        
        # Sort by timestamp descending and apply limit
        changes.sort(key=lambda x: x.timestamp, reverse=True)
        return changes[:limit]
    
    def get_changes_by_date_range(self, start_date: datetime, end_date: datetime, 
                                  profile_id: Optional[int] = None) -> List[FollowerChange]:
        """Get changes within a date range."""
        changes = []
        
        # Base conditions
        conditions = ["timestamp BETWEEN ? AND ?"]
        params = [start_date.isoformat(), end_date.isoformat()]
        
        if profile_id:
            conditions.append("profile_id = ?")
            params.append(profile_id)
        
        where_clause = " WHERE " + " AND ".join(conditions)
        
        # Get follower changes
        follower_query = f"""
            SELECT fc.profile_id, p.username as profile_username, fc.username, fc.change_type, fc.timestamp
            FROM follower_changes fc
            JOIN profiles p ON fc.profile_id = p.id
            {where_clause}
            ORDER BY fc.timestamp DESC
        """
        
        follower_rows = db_manager.execute_query(follower_query, tuple(params))
        
        for row in follower_rows:
            changes.append(FollowerChange(
                profile_username=row['profile_username'],
                affected_username=row['username'],
                change_type=ChangeType(row['change_type']),
                timestamp=datetime.fromisoformat(row['timestamp']),
                profile_id=row['profile_id']
            ))
        
        # Get following changes
        following_query = f"""
            SELECT fc.profile_id, p.username as profile_username, fc.username, fc.change_type, fc.timestamp
            FROM following_changes fc
            JOIN profiles p ON fc.profile_id = p.id
            {where_clause}
            ORDER BY fc.timestamp DESC
        """
        
        following_rows = db_manager.execute_query(following_query, tuple(params))
        
        for row in following_rows:
            changes.append(FollowerChange(
                profile_username=row['profile_username'],
                affected_username=row['username'],
                change_type=ChangeType(row['change_type']),
                timestamp=datetime.fromisoformat(row['timestamp']),
                profile_id=row['profile_id']
            ))
        
        # Sort by timestamp descending
        changes.sort(key=lambda x: x.timestamp, reverse=True)
        return changes
    
    def get_change_statistics(self, profile_id: Optional[int] = None, days: int = 30) -> Dict[str, int]:
        """Get change statistics for the specified period."""
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        base_conditions = ["timestamp >= ?"]
        params = [cutoff_date]
        
        if profile_id:
            base_conditions.append("profile_id = ?")
            params.append(profile_id)
        
        where_clause = " WHERE " + " AND ".join(base_conditions)
        
        # Get follower change stats
        follower_stats_query = f"""
            SELECT 
                change_type,
                COUNT(*) as count
            FROM follower_changes
            {where_clause}
            GROUP BY change_type
        """
        
        following_stats_query = f"""
            SELECT 
                change_type,
                COUNT(*) as count
            FROM following_changes
            {where_clause}
            GROUP BY change_type
        """
        
        stats = {
            'followers_gained': 0,
            'followers_lost': 0,
            'following_started': 0,
            'following_stopped': 0
        }
        
        # Process follower stats
        follower_rows = db_manager.execute_query(follower_stats_query, tuple(params))
        for row in follower_rows:
            if row['change_type'] == 'gained':
                stats['followers_gained'] = row['count']
            elif row['change_type'] == 'lost':
                stats['followers_lost'] = row['count']
        
        # Process following stats
        following_rows = db_manager.execute_query(following_stats_query, tuple(params))
        for row in following_rows:
            if row['change_type'] == 'started_following':
                stats['following_started'] = row['count']
            elif row['change_type'] == 'stopped_following':
                stats['following_stopped'] = row['count']
        
        return stats
    
    def search_changes(self, search_term: str, profile_id: Optional[int] = None, 
                      change_type: Optional[str] = None, start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None, limit: int = 50, offset: int = 0,
                      sort_by: str = 'timestamp', sort_order: str = 'desc') -> Dict[str, Any]:
        """Search for changes with advanced filtering and pagination."""
        # Validate and sanitize search term
        if search_term:
            search_term = SQLInjectionPrevention.escape_like_pattern(search_term.lower())
            search_term = f"%{search_term}%"
        changes = []
        
        # Build base conditions
        base_conditions = []
        params = []
        
        if search_term:
            base_conditions.append("LOWER(fc.username) LIKE ?")
            params.append(search_term)
        
        if profile_id:
            base_conditions.append("fc.profile_id = ?")
            params.append(profile_id)
        
        if start_date:
            base_conditions.append("fc.timestamp >= ?")
            params.append(start_date.isoformat())
        
        if end_date:
            base_conditions.append("fc.timestamp <= ?")
            params.append(end_date.isoformat())
        
        where_clause = " WHERE " + " AND ".join(base_conditions) if base_conditions else ""
        
        # Determine sort column and order
        valid_sort_columns = {'timestamp': 'fc.timestamp', 'username': 'fc.username', 'profile': 'p.username'}
        sort_column = valid_sort_columns.get(sort_by, 'fc.timestamp')
        sort_direction = 'DESC' if sort_order.lower() == 'desc' else 'ASC'
        
        # Search follower changes
        follower_conditions = base_conditions.copy()
        follower_params = params.copy()
        
        if change_type and change_type in ['gained', 'lost']:
            follower_conditions.append("fc.change_type = ?")
            follower_params.append(change_type)
        
        follower_where = " WHERE " + " AND ".join(follower_conditions) if follower_conditions else ""
        
        follower_query = f"""
            SELECT fc.profile_id, p.username as profile_username, fc.username, fc.change_type, fc.timestamp
            FROM follower_changes fc
            JOIN profiles p ON fc.profile_id = p.id
            {follower_where}
            ORDER BY {sort_column} {sort_direction}
            LIMIT ? OFFSET ?
        """
        
        follower_rows = db_manager.execute_query(follower_query, tuple(follower_params + [limit, offset]))
        
        for row in follower_rows:
            changes.append(FollowerChange(
                profile_username=row['profile_username'],
                affected_username=row['username'],
                change_type=ChangeType(row['change_type']),
                timestamp=datetime.fromisoformat(row['timestamp']),
                profile_id=row['profile_id']
            ))
        
        # Search following changes if we haven't reached the limit and change_type allows
        remaining_limit = limit - len(changes)
        if remaining_limit > 0 and (not change_type or change_type in ['started_following', 'stopped_following']):
            following_conditions = base_conditions.copy()
            following_params = params.copy()
            
            if change_type and change_type in ['started_following', 'stopped_following']:
                following_conditions.append("fc.change_type = ?")
                following_params.append(change_type)
            
            following_where = " WHERE " + " AND ".join(following_conditions) if following_conditions else ""
            
            following_query = f"""
                SELECT fc.profile_id, p.username as profile_username, fc.username, fc.change_type, fc.timestamp
                FROM following_changes fc
                JOIN profiles p ON fc.profile_id = p.id
                {following_where}
                ORDER BY {sort_column} {sort_direction}
                LIMIT ? OFFSET ?
            """
            
            following_rows = db_manager.execute_query(following_query, tuple(following_params + [remaining_limit, offset]))
            
            for row in following_rows:
                changes.append(FollowerChange(
                    profile_username=row['profile_username'],
                    affected_username=row['username'],
                    change_type=ChangeType(row['change_type']),
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    profile_id=row['profile_id']
                ))
        
        # Get total count for pagination
        total_count = self._get_search_count(search_term, profile_id, change_type, start_date, end_date)
        
        # Sort combined results if needed
        if sort_by == 'timestamp':
            changes.sort(key=lambda x: x.timestamp, reverse=(sort_order.lower() == 'desc'))
        elif sort_by == 'username':
            changes.sort(key=lambda x: x.affected_username.lower(), reverse=(sort_order.lower() == 'desc'))
        elif sort_by == 'profile':
            changes.sort(key=lambda x: x.profile_username.lower(), reverse=(sort_order.lower() == 'desc'))
        
        return {
            'changes': changes[:limit],
            'total_count': total_count,
            'has_more': total_count > offset + len(changes),
            'page': (offset // limit) + 1,
            'total_pages': (total_count + limit - 1) // limit
        }
    
    def _get_search_count(self, search_term: Optional[str], profile_id: Optional[int],
                         change_type: Optional[str], start_date: Optional[datetime],
                         end_date: Optional[datetime]) -> int:
        """Get total count of search results for pagination."""
        base_conditions = []
        params = []
        
        if search_term:
            base_conditions.append("LOWER(fc.username) LIKE ?")
            params.append(search_term)
        
        if profile_id:
            base_conditions.append("fc.profile_id = ?")
            params.append(profile_id)
        
        if start_date:
            base_conditions.append("fc.timestamp >= ?")
            params.append(start_date.isoformat())
        
        if end_date:
            base_conditions.append("fc.timestamp <= ?")
            params.append(end_date.isoformat())
        
        where_clause = " WHERE " + " AND ".join(base_conditions) if base_conditions else ""
        
        total_count = 0
        
        # Count follower changes
        if not change_type or change_type in ['gained', 'lost']:
            follower_conditions = base_conditions.copy()
            follower_params = params.copy()
            
            if change_type and change_type in ['gained', 'lost']:
                follower_conditions.append("fc.change_type = ?")
                follower_params.append(change_type)
            
            follower_where = " WHERE " + " AND ".join(follower_conditions) if follower_conditions else ""
            
            follower_count_query = f"""
                SELECT COUNT(*) as count
                FROM follower_changes fc
                JOIN profiles p ON fc.profile_id = p.id
                {follower_where}
            """
            
            follower_result = db_manager.execute_query(follower_count_query, tuple(follower_params))
            total_count += follower_result[0]['count'] if follower_result else 0
        
        # Count following changes
        if not change_type or change_type in ['started_following', 'stopped_following']:
            following_conditions = base_conditions.copy()
            following_params = params.copy()
            
            if change_type and change_type in ['started_following', 'stopped_following']:
                following_conditions.append("fc.change_type = ?")
                following_params.append(change_type)
            
            following_where = " WHERE " + " AND ".join(following_conditions) if following_conditions else ""
            
            following_count_query = f"""
                SELECT COUNT(*) as count
                FROM following_changes fc
                JOIN profiles p ON fc.profile_id = p.id
                {following_where}
            """
            
            following_result = db_manager.execute_query(following_count_query, tuple(following_params))
            total_count += following_result[0]['count'] if following_result else 0
        
        return total_count
        
        return total_count
    
    def search_current_users(self, search_term: str, profile_id: Optional[int] = None,
                           user_type: str = 'both', limit: int = 50, offset: int = 0,
                           sort_by: str = 'username', sort_order: str = 'asc') -> Dict[str, Any]:
        """Search current followers/following users."""
        search_term = f"%{search_term.lower()}%"
        users = []
        
        # Determine sort order
        sort_direction = 'DESC' if sort_order.lower() == 'desc' else 'ASC'
        
        # Search followers
        if user_type in ['both', 'followers']:
            follower_conditions = ["LOWER(cf.follower_username) LIKE ?"]
            follower_params = [search_term]
            
            if profile_id:
                follower_conditions.append("cf.profile_id = ?")
                follower_params.append(profile_id)
            
            follower_where = " WHERE " + " AND ".join(follower_conditions)
            
            follower_query = f"""
                SELECT cf.profile_id, p.username as profile_username, cf.follower_username as username, 
                       'follower' as user_type, cf.added_at
                FROM current_followers cf
                JOIN profiles p ON cf.profile_id = p.id
                {follower_where}
                ORDER BY cf.follower_username {sort_direction}
                LIMIT ? OFFSET ?
            """
            
            follower_rows = db_manager.execute_query(follower_query, tuple(follower_params + [limit, offset]))
            
            for row in follower_rows:
                users.append({
                    'profile_id': row['profile_id'],
                    'profile_username': row['profile_username'],
                    'username': row['username'],
                    'user_type': row['user_type'],
                    'added_at': datetime.fromisoformat(row['added_at']) if row['added_at'] else None
                })
        
        # Search following
        remaining_limit = limit - len(users)
        if remaining_limit > 0 and user_type in ['both', 'following']:
            following_conditions = ["LOWER(cf.following_username) LIKE ?"]
            following_params = [search_term]
            
            if profile_id:
                following_conditions.append("cf.profile_id = ?")
                following_params.append(profile_id)
            
            following_where = " WHERE " + " AND ".join(following_conditions)
            
            following_query = f"""
                SELECT cf.profile_id, p.username as profile_username, cf.following_username as username,
                       'following' as user_type, cf.added_at
                FROM current_following cf
                JOIN profiles p ON cf.profile_id = p.id
                {following_where}
                ORDER BY cf.following_username {sort_direction}
                LIMIT ? OFFSET ?
            """
            
            following_rows = db_manager.execute_query(following_query, tuple(following_params + [remaining_limit, offset]))
            
            for row in following_rows:
                users.append({
                    'profile_id': row['profile_id'],
                    'profile_username': row['profile_username'],
                    'username': row['username'],
                    'user_type': row['user_type'],
                    'added_at': datetime.fromisoformat(row['added_at']) if row['added_at'] else None
                })
        
        # Get total count
        total_count = self._get_current_users_count(search_term, profile_id, user_type)
        
        # Sort combined results
        if sort_by == 'username':
            users.sort(key=lambda x: x['username'].lower(), reverse=(sort_order.lower() == 'desc'))
        elif sort_by == 'added_at':
            users.sort(key=lambda x: x['added_at'] or datetime.min, reverse=(sort_order.lower() == 'desc'))
        
        return {
            'users': users[:limit],
            'total_count': total_count,
            'has_more': total_count > offset + len(users),
            'page': (offset // limit) + 1,
            'total_pages': (total_count + limit - 1) // limit
        }
    
    def _get_current_users_count(self, search_term: str, profile_id: Optional[int], user_type: str) -> int:
        """Get total count of current users search results."""
        total_count = 0
        
        # Count followers
        if user_type in ['both', 'followers']:
            follower_conditions = ["LOWER(cf.follower_username) LIKE ?"]
            follower_params = [search_term]
            
            if profile_id:
                follower_conditions.append("cf.profile_id = ?")
                follower_params.append(profile_id)
            
            follower_where = " WHERE " + " AND ".join(follower_conditions)
            
            follower_count_query = f"""
                SELECT COUNT(*) as count
                FROM current_followers cf
                {follower_where}
            """
            
            follower_result = db_manager.execute_query(follower_count_query, tuple(follower_params))
            total_count += follower_result[0]['count'] if follower_result else 0
        
        # Count following
        if user_type in ['both', 'following']:
            following_conditions = ["LOWER(cf.following_username) LIKE ?"]
            following_params = [search_term]
            
            if profile_id:
                following_conditions.append("cf.profile_id = ?")
                following_params.append(profile_id)
            
            following_where = " WHERE " + " AND ".join(following_conditions)
            
            following_count_query = f"""
                SELECT COUNT(*) as count
                FROM current_following cf
                {following_where}
            """
            
            following_result = db_manager.execute_query(following_count_query, tuple(following_params))
            total_count += following_result[0]['count'] if following_result else 0
        
        return total_count
    
    def get_trend_data(self, profile_id: int, start_date: datetime, end_date: datetime) -> Dict[str, List]:
        """Get trend data for charts showing follower/following counts over time."""
        # Get profile stats at different time points
        dates = []
        followers = []
        following = []
        
        # Calculate daily intervals
        current_date = start_date
        delta = timedelta(days=1)
        
        while current_date <= end_date:
            # Get follower count at this date
            follower_count = self._get_follower_count_at_date(profile_id, current_date)
            following_count = self._get_following_count_at_date(profile_id, current_date)
            
            dates.append(current_date.strftime('%Y-%m-%d'))
            followers.append(follower_count)
            following.append(following_count)
            
            current_date += delta
        
        return {
            'dates': dates,
            'followers': followers,
            'following': following
        }
    
    def _get_follower_count_at_date(self, profile_id: int, target_date: datetime) -> int:
        """Get follower count at a specific date by calculating from changes."""
        # Get current follower count
        current_count_query = """
            SELECT COUNT(*) as count
            FROM current_followers
            WHERE profile_id = ?
        """
        current_result = db_manager.execute_query(current_count_query, (profile_id,))
        current_count = current_result[0]['count'] if current_result else 0
        
        # Get changes after the target date and reverse them
        changes_query = """
            SELECT change_type, COUNT(*) as count
            FROM follower_changes
            WHERE profile_id = ? AND timestamp > ?
            GROUP BY change_type
        """
        
        changes_result = db_manager.execute_query(changes_query, (profile_id, target_date.isoformat()))
        
        # Reverse the changes to get count at target date
        gained_after = 0
        lost_after = 0
        
        for row in changes_result:
            if row['change_type'] == 'gained':
                gained_after = row['count']
            elif row['change_type'] == 'lost':
                lost_after = row['count']
        
        # Count at target date = current count - gained after + lost after
        count_at_date = current_count - gained_after + lost_after
        return max(0, count_at_date)  # Ensure non-negative
    
    def _get_following_count_at_date(self, profile_id: int, target_date: datetime) -> int:
        """Get following count at a specific date by calculating from changes."""
        # Get current following count
        current_count_query = """
            SELECT COUNT(*) as count
            FROM current_following
            WHERE profile_id = ?
        """
        current_result = db_manager.execute_query(current_count_query, (profile_id,))
        current_count = current_result[0]['count'] if current_result else 0
        
        # Get changes after the target date and reverse them
        changes_query = """
            SELECT change_type, COUNT(*) as count
            FROM following_changes
            WHERE profile_id = ? AND timestamp > ?
            GROUP BY change_type
        """
        
        changes_result = db_manager.execute_query(changes_query, (profile_id, target_date.isoformat()))
        
        # Reverse the changes to get count at target date
        started_after = 0
        stopped_after = 0
        
        for row in changes_result:
            if row['change_type'] == 'started_following':
                started_after = row['count']
            elif row['change_type'] == 'stopped_following':
                stopped_after = row['count']
        
        # Count at target date = current count - started after + stopped after
        count_at_date = current_count - started_after + stopped_after
        return max(0, count_at_date)  # Ensure non-negative


class DataRetentionManager:
    """Manages data retention and cleanup operations."""
    
    def __init__(self, retention_days: int = 365):
        """Initialize with retention period in days."""
        self.retention_days = retention_days
    
    def cleanup_old_data(self) -> Dict[str, int]:
        """Remove data older than retention period and return cleanup stats."""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        cutoff_str = cutoff_date.isoformat()
        
        stats = {}
        
        # Clean up follower changes
        follower_changes_deleted = db_manager.execute_update(
            "DELETE FROM follower_changes WHERE timestamp < ?",
            (cutoff_str,)
        )
        stats['follower_changes_deleted'] = follower_changes_deleted
        
        # Clean up following changes
        following_changes_deleted = db_manager.execute_update(
            "DELETE FROM following_changes WHERE timestamp < ?",
            (cutoff_str,)
        )
        stats['following_changes_deleted'] = following_changes_deleted
        
        # Clean up old current_followers entries (keep only recent)
        current_followers_deleted = db_manager.execute_update(
            "DELETE FROM current_followers WHERE added_at < ?",
            (cutoff_str,)
        )
        stats['current_followers_deleted'] = current_followers_deleted
        
        # Clean up old current_following entries (keep only recent)
        current_following_deleted = db_manager.execute_update(
            "DELETE FROM current_following WHERE added_at < ?",
            (cutoff_str,)
        )
        stats['current_following_deleted'] = current_following_deleted
        
        logger.info(f"Data cleanup completed: {stats}")
        return stats
    
    def get_data_size_stats(self) -> Dict[str, int]:
        """Get statistics about data size in database."""
        stats = {}
        
        # Count records in each table
        tables = ['profiles', 'current_followers', 'current_following', 
                 'follower_changes', 'following_changes', 'settings']
        
        for table in tables:
            count_query = f"SELECT COUNT(*) as count FROM {table}"
            result = db_manager.execute_query(count_query)
            stats[f'{table}_count'] = result[0]['count'] if result else 0
        
        return stats
    
    def archive_old_data(self, archive_days: int = 90) -> Dict[str, int]:
        """Archive data older than archive_days but newer than retention period."""
        if archive_days >= self.retention_days:
            raise ValueError("Archive days must be less than retention days")
        
        archive_cutoff = datetime.now() - timedelta(days=archive_days)
        retention_cutoff = datetime.now() - timedelta(days=self.retention_days)
        
        archive_cutoff_str = archive_cutoff.isoformat()
        retention_cutoff_str = retention_cutoff.isoformat()
        
        stats = {}
        
        # For now, we'll just count what would be archived
        # In a real implementation, you might move data to archive tables
        
        # Count follower changes to archive
        follower_archive_query = """
            SELECT COUNT(*) as count FROM follower_changes 
            WHERE timestamp BETWEEN ? AND ?
        """
        result = db_manager.execute_query(follower_archive_query, (retention_cutoff_str, archive_cutoff_str))
        stats['follower_changes_to_archive'] = result[0]['count'] if result else 0
        
        # Count following changes to archive
        following_archive_query = """
            SELECT COUNT(*) as count FROM following_changes 
            WHERE timestamp BETWEEN ? AND ?
        """
        result = db_manager.execute_query(following_archive_query, (retention_cutoff_str, archive_cutoff_str))
        stats['following_changes_to_archive'] = result[0]['count'] if result else 0
        
        logger.info(f"Archive analysis completed: {stats}")
        return stats
    
    def optimize_database(self) -> Dict[str, Any]:
        """Run database optimization operations."""
        stats = {}
        
        with db_manager.get_connection() as conn:
            # Run VACUUM to reclaim space
            conn.execute("VACUUM")
            
            # Analyze tables for query optimization
            conn.execute("ANALYZE")
            
            # Get database size info
            size_query = "SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()"
            result = conn.execute(size_query).fetchone()
            stats['database_size_bytes'] = result[0] if result else 0
            
            # Get table sizes
            tables = ['profiles', 'current_followers', 'current_following', 
                     'follower_changes', 'following_changes', 'settings']
            
            for table in tables:
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                result = conn.execute(count_query).fetchone()
                stats[f'{table}_rows'] = result[0] if result else 0
        
        logger.info(f"Database optimization completed: {stats}")
        return stats