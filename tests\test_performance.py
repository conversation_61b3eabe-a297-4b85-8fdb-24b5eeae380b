"""
Performance tests for large dataset handling.

This module provides performance tests to ensure the application can handle
large datasets efficiently, including large follower lists, many profiles,
and high-volume change detection.
"""

import pytest
import time
import tempfile
import os
import psutil
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, as_completed

from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, ChangeRepository, FollowerRepository, SettingsRepository
)
from models.data_models import (
    MonitoringConfig, FollowerChange, ChangeType, ProfileInfo
)
from services.change_detector import ChangeDetector
from services.data_processor import DataProcessor
from services.profile_scanner import ProfileScanner
from services.monitoring_service import MonitoringService
from config import Config


class PerformanceTimer:
    """Context manager for timing operations."""
    
    def __init__(self, operation_name):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        print(f"{self.operation_name}: {self.duration:.3f} seconds")


class MemoryMonitor:
    """Monitor memory usage during operations."""
    
    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = None
        self.peak_memory = None
    
    def start(self):
        """Start monitoring memory."""
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.initial_memory
    
    def update(self):
        """Update peak memory if current usage is higher."""
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory
    
    def get_stats(self):
        """Get memory usage statistics."""
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        return {
            'initial_mb': self.initial_memory,
            'current_mb': current_memory,
            'peak_mb': self.peak_memory,
            'increase_mb': current_memory - self.initial_memory
        }


class TestLargeDatasetPerformance:
    """Performance tests for large dataset operations."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database for performance testing."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        # Initialize database with performance optimizations
        db_manager = DatabaseManager(temp_file.name)
        db_manager.initialize_database()
        
        # Add performance indexes
        import sqlite3
        conn = sqlite3.connect(temp_file.name)
        cursor = conn.cursor()
        
        # Additional indexes for performance
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_follower_changes_profile_timestamp 
            ON follower_changes(profile_id, timestamp DESC)
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_current_followers_profile 
            ON current_followers(profile_id)
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_current_following_profile 
            ON current_following(profile_id)
        """)
        
        conn.commit()
        conn.close()
        
        yield temp_file.name
        
        # Cleanup
        os.unlink(temp_file.name)
    
    @pytest.fixture
    def repositories(self, temp_db):
        """Create repository instances."""
        return (
            ProfileRepository(temp_db),
            ChangeRepository(temp_db),
            FollowerRepository(temp_db),
            SettingsRepository(temp_db)
        )
    
    def test_large_follower_list_processing(self, repositories):
        """Test processing of large follower lists (10K+ followers)."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create test profile
        config = MonitoringConfig(
            profile_username="large_account",
            display_name="Large Account",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Generate large follower set (10,000 followers)
        large_follower_set = {f"follower_{i:05d}" for i in range(10000)}
        large_following_set = {f"following_{i:04d}" for i in range(1000)}
        
        memory_monitor = MemoryMonitor()
        memory_monitor.start()
        
        # Test storing large follower list
        with PerformanceTimer("Store 10K followers"):
            follower_repo.store_current_followers(profile_id, large_follower_set)
        
        memory_monitor.update()
        
        with PerformanceTimer("Store 1K following"):
            follower_repo.store_current_following(profile_id, large_following_set)
        
        memory_monitor.update()
        
        # Test retrieving large follower list
        with PerformanceTimer("Retrieve 10K followers"):
            retrieved_followers = follower_repo.get_current_followers(profile_id)
        
        memory_monitor.update()
        
        with PerformanceTimer("Retrieve 1K following"):
            retrieved_following = follower_repo.get_current_following(profile_id)
        
        # Verify data integrity
        assert len(retrieved_followers) == 10000
        assert len(retrieved_following) == 1000
        assert retrieved_followers == large_follower_set
        assert retrieved_following == large_following_set
        
        # Test change detection with large sets
        # Simulate 1% change (100 followers lost, 100 new followers)
        modified_followers = large_follower_set.copy()
        
        # Remove 100 followers
        followers_to_remove = {f"follower_{i:05d}" for i in range(100)}
        modified_followers -= followers_to_remove
        
        # Add 100 new followers
        new_followers = {f"new_follower_{i:03d}" for i in range(100)}
        modified_followers |= new_followers
        
        change_detector = ChangeDetector()
        
        with PerformanceTimer("Detect changes in 10K follower list"):
            changes = change_detector.detect_changes(
                "large_account", profile_id,
                modified_followers, large_follower_set,
                large_following_set, large_following_set
            )
        
        memory_monitor.update()
        
        # Verify change detection
        assert len(changes) == 200  # 100 lost + 100 gained
        
        gained_changes = [c for c in changes if c.change_type == ChangeType.GAINED]
        lost_changes = [c for c in changes if c.change_type == ChangeType.LOST]
        
        assert len(gained_changes) == 100
        assert len(lost_changes) == 100
        
        # Test storing large number of changes
        with PerformanceTimer("Store 200 changes"):
            change_repo.store_follower_changes(changes)
        
        memory_monitor.update()
        
        # Print memory usage statistics
        memory_stats = memory_monitor.get_stats()
        print(f"Memory usage - Initial: {memory_stats['initial_mb']:.1f}MB, "
              f"Peak: {memory_stats['peak_mb']:.1f}MB, "
              f"Increase: {memory_stats['increase_mb']:.1f}MB")
        
        # Memory increase should be reasonable (less than 100MB for this test)
        assert memory_stats['increase_mb'] < 100
    
    def test_many_profiles_processing(self, repositories):
        """Test processing many profiles simultaneously."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        num_profiles = 100
        followers_per_profile = 1000
        
        memory_monitor = MemoryMonitor()
        memory_monitor.start()
        
        # Create many profiles
        profile_ids = []
        with PerformanceTimer(f"Create {num_profiles} profiles"):
            for i in range(num_profiles):
                config = MonitoringConfig(
                    profile_username=f"user_{i:03d}",
                    display_name=f"User {i}",
                    enabled=True
                )
                profile_id = profile_repo.create_profile(config)
                profile_ids.append(profile_id)
        
        memory_monitor.update()
        
        # Add follower data for each profile
        with PerformanceTimer(f"Store follower data for {num_profiles} profiles"):
            for i, profile_id in enumerate(profile_ids):
                followers = {f"follower_{i}_{j:04d}" for j in range(followers_per_profile)}
                following = {f"following_{i}_{j:03d}" for j in range(100)}
                
                follower_repo.store_current_followers(profile_id, followers)
                follower_repo.store_current_following(profile_id, following)
        
        memory_monitor.update()
        
        # Test retrieving all profiles
        with PerformanceTimer(f"Retrieve all {num_profiles} profiles"):
            all_profiles = profile_repo.get_all_profiles()
        
        assert len(all_profiles) == num_profiles
        
        # Test retrieving enabled profiles
        with PerformanceTimer(f"Retrieve enabled profiles"):
            enabled_profiles = profile_repo.get_enabled_profiles()
        
        assert len(enabled_profiles) == num_profiles
        
        # Test concurrent profile processing
        def process_profile(profile_id):
            """Simulate processing a single profile."""
            followers = follower_repo.get_current_followers(profile_id)
            following = follower_repo.get_current_following(profile_id)
            
            # Simulate some changes
            modified_followers = followers.copy()
            if len(modified_followers) > 10:
                # Remove 5 followers, add 5 new ones
                to_remove = list(modified_followers)[:5]
                for username in to_remove:
                    modified_followers.remove(username)
                
                for j in range(5):
                    modified_followers.add(f"new_follower_{profile_id}_{j}")
            
            # Detect changes
            change_detector = ChangeDetector()
            changes = change_detector.detect_changes(
                f"user_{profile_id:03d}", profile_id,
                modified_followers, followers,
                following, following
            )
            
            return len(changes)
        
        # Process profiles concurrently
        with PerformanceTimer(f"Process {num_profiles} profiles concurrently"):
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(process_profile, pid) for pid in profile_ids[:20]]  # Test with subset
                
                total_changes = 0
                for future in as_completed(futures):
                    total_changes += future.result()
        
        memory_monitor.update()
        
        print(f"Total changes detected across profiles: {total_changes}")
        
        # Print memory usage
        memory_stats = memory_monitor.get_stats()
        print(f"Memory usage - Initial: {memory_stats['initial_mb']:.1f}MB, "
              f"Peak: {memory_stats['peak_mb']:.1f}MB, "
              f"Increase: {memory_stats['increase_mb']:.1f}MB")
        
        # Memory increase should be reasonable
        assert memory_stats['increase_mb'] < 200
    
    def test_high_volume_change_detection(self, repositories):
        """Test change detection with high volume of changes."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create profile
        config = MonitoringConfig(
            profile_username="high_volume_user",
            display_name="High Volume User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Generate large datasets with significant changes
        base_followers = {f"base_follower_{i:05d}" for i in range(5000)}
        base_following = {f"base_following_{i:04d}" for i in range(500)}
        
        # Create modified sets with 20% churn
        churn_count = 1000  # 20% of 5000
        
        # Remove 1000 followers, add 1000 new ones
        modified_followers = base_followers.copy()
        followers_to_remove = {f"base_follower_{i:05d}" for i in range(churn_count)}
        modified_followers -= followers_to_remove
        
        new_followers = {f"new_follower_{i:05d}" for i in range(churn_count)}
        modified_followers |= new_followers
        
        # Similar for following (10% churn)
        following_churn = 50
        modified_following = base_following.copy()
        following_to_remove = {f"base_following_{i:04d}" for i in range(following_churn)}
        modified_following -= following_to_remove
        
        new_following = {f"new_following_{i:04d}" for i in range(following_churn)}
        modified_following |= new_following
        
        memory_monitor = MemoryMonitor()
        memory_monitor.start()
        
        # Test change detection performance
        change_detector = ChangeDetector()
        
        with PerformanceTimer("Detect 2100 changes (high volume)"):
            changes = change_detector.detect_changes(
                "high_volume_user", profile_id,
                modified_followers, base_followers,
                modified_following, base_following
            )
        
        memory_monitor.update()
        
        # Verify change count
        expected_changes = (churn_count * 2) + (following_churn * 2)  # Lost + gained for both
        assert len(changes) == expected_changes
        
        # Test change analysis performance
        with PerformanceTimer("Analyze change patterns"):
            analysis = change_detector.analyze_change_patterns(changes)
        
        assert analysis['total_changes'] == expected_changes
        assert analysis['follower_changes'] == churn_count * 2
        assert analysis['following_changes'] == following_churn * 2
        
        # Test storing high volume of changes
        with PerformanceTimer("Store 2100 changes"):
            change_repo.store_follower_changes(changes)
        
        memory_monitor.update()
        
        # Test retrieving changes
        with PerformanceTimer("Retrieve recent changes"):
            recent_changes = change_repo.get_recent_changes(profile_id=profile_id, limit=1000)
        
        assert len(recent_changes) == 1000  # Limited by query
        
        # Test change statistics calculation
        with PerformanceTimer("Calculate change statistics"):
            stats = change_repo.get_change_statistics(profile_id=profile_id, days=1)
        
        assert stats['total_changes'] == expected_changes
        
        # Print memory usage
        memory_stats = memory_monitor.get_stats()
        print(f"Memory usage - Initial: {memory_stats['initial_mb']:.1f}MB, "
              f"Peak: {memory_stats['peak_mb']:.1f}MB, "
              f"Increase: {memory_stats['increase_mb']:.1f}MB")
    
    def test_database_query_performance(self, repositories):
        """Test database query performance with large datasets."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create multiple profiles with data
        num_profiles = 50
        changes_per_profile = 200
        
        profile_ids = []
        for i in range(num_profiles):
            config = MonitoringConfig(
                profile_username=f"perf_user_{i:03d}",
                display_name=f"Performance User {i}",
                enabled=True
            )
            profile_id = profile_repo.create_profile(config)
            profile_ids.append(profile_id)
        
        # Generate changes for each profile over time
        all_changes = []
        base_time = datetime.now() - timedelta(days=30)
        
        for profile_id in profile_ids:
            for j in range(changes_per_profile):
                change_time = base_time + timedelta(
                    hours=j * 3.6  # Spread over 30 days
                )
                
                change = FollowerChange(
                    profile_username=f"perf_user_{profile_id:03d}",
                    affected_username=f"affected_user_{j:04d}",
                    change_type=ChangeType.GAINED if j % 2 == 0 else ChangeType.LOST,
                    timestamp=change_time,
                    profile_id=profile_id
                )
                all_changes.append(change)
        
        # Store all changes
        with PerformanceTimer(f"Store {len(all_changes)} changes"):
            change_repo.store_follower_changes(all_changes)
        
        # Test various query patterns
        test_profile_id = profile_ids[0]
        
        # Test recent changes query
        with PerformanceTimer("Query recent changes (limit 100)"):
            recent = change_repo.get_recent_changes(profile_id=test_profile_id, limit=100)
        
        assert len(recent) == 100
        
        # Test date range query
        start_date = base_time + timedelta(days=10)
        end_date = base_time + timedelta(days=20)
        
        with PerformanceTimer("Query changes by date range"):
            date_range_changes = change_repo.get_changes_by_date_range(
                start_date, end_date, test_profile_id
            )
        
        # Test statistics query
        with PerformanceTimer("Calculate statistics for profile"):
            stats = change_repo.get_change_statistics(profile_id=test_profile_id, days=30)
        
        assert stats['total_changes'] == changes_per_profile
        
        # Test all profiles query
        with PerformanceTimer("Query all profiles"):
            all_profiles = profile_repo.get_all_profiles()
        
        assert len(all_profiles) == num_profiles
        
        # Test global statistics
        with PerformanceTimer("Calculate global statistics"):
            global_stats = change_repo.get_change_statistics(profile_id=None, days=30)
        
        expected_total = num_profiles * changes_per_profile
        assert global_stats['total_changes'] == expected_total
    
    def test_concurrent_operations_performance(self, repositories):
        """Test performance under concurrent operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create test profiles
        num_profiles = 20
        profile_ids = []
        
        for i in range(num_profiles):
            config = MonitoringConfig(
                profile_username=f"concurrent_user_{i:02d}",
                display_name=f"Concurrent User {i}",
                enabled=True
            )
            profile_id = profile_repo.create_profile(config)
            profile_ids.append(profile_id)
        
        def concurrent_operation(profile_id, operation_id):
            """Simulate concurrent database operations."""
            try:
                # Store follower data
                followers = {f"follower_{profile_id}_{j:03d}" for j in range(100)}
                following = {f"following_{profile_id}_{j:02d}" for j in range(20)}
                
                follower_repo.store_current_followers(profile_id, followers)
                follower_repo.store_current_following(profile_id, following)
                
                # Generate and store changes
                changes = []
                for k in range(10):
                    change = FollowerChange(
                        profile_username=f"concurrent_user_{profile_id:02d}",
                        affected_username=f"change_user_{operation_id}_{k:02d}",
                        change_type=ChangeType.GAINED if k % 2 == 0 else ChangeType.LOST,
                        timestamp=datetime.now(),
                        profile_id=profile_id
                    )
                    changes.append(change)
                
                change_repo.store_follower_changes(changes)
                
                # Read operations
                stored_followers = follower_repo.get_current_followers(profile_id)
                recent_changes = change_repo.get_recent_changes(profile_id=profile_id, limit=50)
                
                return {
                    'profile_id': profile_id,
                    'operation_id': operation_id,
                    'followers_stored': len(stored_followers),
                    'changes_stored': len(changes),
                    'success': True
                }
                
            except Exception as e:
                return {
                    'profile_id': profile_id,
                    'operation_id': operation_id,
                    'error': str(e),
                    'success': False
                }
        
        # Run concurrent operations
        memory_monitor = MemoryMonitor()
        memory_monitor.start()
        
        with PerformanceTimer("Concurrent operations (20 profiles, 5 threads)"):
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = []
                
                # Submit operations for each profile
                for i, profile_id in enumerate(profile_ids):
                    future = executor.submit(concurrent_operation, profile_id, i)
                    futures.append(future)
                
                # Collect results
                results = []
                for future in as_completed(futures):
                    result = future.result()
                    results.append(result)
        
        memory_monitor.update()
        
        # Verify all operations succeeded
        successful_operations = [r for r in results if r['success']]
        failed_operations = [r for r in results if not r['success']]
        
        print(f"Successful operations: {len(successful_operations)}")
        print(f"Failed operations: {len(failed_operations)}")
        
        if failed_operations:
            for failure in failed_operations:
                print(f"Failed operation: {failure}")
        
        # Should have high success rate
        success_rate = len(successful_operations) / len(results)
        assert success_rate >= 0.95  # At least 95% success rate
        
        # Verify data integrity
        final_profiles = profile_repo.get_all_profiles()
        assert len(final_profiles) == num_profiles
        
        # Print memory usage
        memory_stats = memory_monitor.get_stats()
        print(f"Memory usage - Initial: {memory_stats['initial_mb']:.1f}MB, "
              f"Peak: {memory_stats['peak_mb']:.1f}MB, "
              f"Increase: {memory_stats['increase_mb']:.1f}MB")
    
    def test_data_cleanup_performance(self, repositories):
        """Test performance of data cleanup operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create profile
        config = MonitoringConfig(
            profile_username="cleanup_test_user",
            display_name="Cleanup Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Generate large amount of old data
        num_old_changes = 10000
        old_changes = []
        
        # Create changes spanning 2 years (some old, some recent)
        base_time = datetime.now() - timedelta(days=730)  # 2 years ago
        
        for i in range(num_old_changes):
            # 80% old data (> 1 year), 20% recent data
            if i < num_old_changes * 0.8:
                change_time = base_time + timedelta(days=i * 0.3)  # Spread over ~8 months
            else:
                change_time = datetime.now() - timedelta(days=i % 300)  # Recent data
            
            change = FollowerChange(
                profile_username="cleanup_test_user",
                affected_username=f"old_user_{i:05d}",
                change_type=ChangeType.GAINED if i % 2 == 0 else ChangeType.LOST,
                timestamp=change_time,
                profile_id=profile_id
            )
            old_changes.append(change)
        
        # Store all changes
        with PerformanceTimer(f"Store {num_old_changes} changes for cleanup test"):
            change_repo.store_follower_changes(old_changes)
        
        # Verify all changes are stored
        all_changes_before = change_repo.get_changes_for_profile(profile_id)
        assert len(all_changes_before) == num_old_changes
        
        # Test cleanup performance
        cutoff_date = datetime.now() - timedelta(days=365)  # 1 year ago
        
        memory_monitor = MemoryMonitor()
        memory_monitor.start()
        
        with PerformanceTimer("Cleanup old changes (1+ year old)"):
            deleted_count = change_repo.cleanup_old_changes(cutoff_date)
        
        memory_monitor.update()
        
        # Verify cleanup results
        remaining_changes = change_repo.get_changes_for_profile(profile_id)
        expected_remaining = int(num_old_changes * 0.2)  # 20% should remain
        
        print(f"Changes before cleanup: {len(all_changes_before)}")
        print(f"Changes deleted: {deleted_count}")
        print(f"Changes remaining: {len(remaining_changes)}")
        print(f"Expected remaining: ~{expected_remaining}")
        
        # Should have deleted approximately 80% of changes
        assert deleted_count >= num_old_changes * 0.7  # At least 70% deleted
        assert len(remaining_changes) <= num_old_changes * 0.3  # At most 30% remaining
        
        # Verify remaining changes are recent
        for change in remaining_changes:
            days_old = (datetime.now() - change.timestamp).days
            assert days_old <= 365  # Should be within last year
        
        # Print memory usage
        memory_stats = memory_monitor.get_stats()
        print(f"Memory usage - Initial: {memory_stats['initial_mb']:.1f}MB, "
              f"Peak: {memory_stats['peak_mb']:.1f}MB, "
              f"Increase: {memory_stats['increase_mb']:.1f}MB")


class TestScalabilityLimits:
    """Tests to determine scalability limits."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        db_manager = DatabaseManager(temp_file.name)
        db_manager.initialize_database()
        
        yield temp_file.name
        os.unlink(temp_file.name)
    
    @pytest.mark.slow
    def test_maximum_follower_list_size(self, temp_db):
        """Test maximum practical follower list size."""
        follower_repo = FollowerRepository(temp_db)
        profile_repo = ProfileRepository(temp_db)
        
        # Create test profile
        config = MonitoringConfig(
            profile_username="max_size_test",
            display_name="Max Size Test",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Test increasingly large follower sets
        sizes_to_test = [10000, 50000, 100000, 250000]
        
        for size in sizes_to_test:
            print(f"\nTesting follower list size: {size:,}")
            
            # Generate follower set
            followers = {f"follower_{i:07d}" for i in range(size)}
            
            memory_monitor = MemoryMonitor()
            memory_monitor.start()
            
            try:
                # Test storage
                start_time = time.time()
                follower_repo.store_current_followers(profile_id, followers)
                store_time = time.time() - start_time
                
                memory_monitor.update()
                
                # Test retrieval
                start_time = time.time()
                retrieved = follower_repo.get_current_followers(profile_id)
                retrieve_time = time.time() - start_time
                
                memory_monitor.update()
                
                # Verify data integrity
                assert len(retrieved) == size
                
                memory_stats = memory_monitor.get_stats()
                
                print(f"  Store time: {store_time:.3f}s")
                print(f"  Retrieve time: {retrieve_time:.3f}s")
                print(f"  Memory increase: {memory_stats['increase_mb']:.1f}MB")
                
                # Performance thresholds (adjust based on requirements)
                if store_time > 30:  # More than 30 seconds to store
                    print(f"  WARNING: Store time exceeded threshold at size {size:,}")
                
                if retrieve_time > 10:  # More than 10 seconds to retrieve
                    print(f"  WARNING: Retrieve time exceeded threshold at size {size:,}")
                
                if memory_stats['increase_mb'] > 500:  # More than 500MB memory increase
                    print(f"  WARNING: Memory usage exceeded threshold at size {size:,}")
                
            except Exception as e:
                print(f"  FAILED at size {size:,}: {e}")
                break
    
    @pytest.mark.slow
    def test_maximum_concurrent_profiles(self, temp_db):
        """Test maximum number of profiles that can be processed concurrently."""
        repositories = (
            ProfileRepository(temp_db),
            ChangeRepository(temp_db),
            FollowerRepository(temp_db),
            SettingsRepository(temp_db)
        )
        
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Test with increasing numbers of profiles
        profile_counts = [100, 500, 1000, 2000]
        
        for count in profile_counts:
            print(f"\nTesting {count} profiles")
            
            # Create profiles
            profile_ids = []
            for i in range(count):
                config = MonitoringConfig(
                    profile_username=f"scale_user_{i:05d}",
                    display_name=f"Scale User {i}",
                    enabled=True
                )
                profile_id = profile_repo.create_profile(config)
                profile_ids.append(profile_id)
            
            memory_monitor = MemoryMonitor()
            memory_monitor.start()
            
            try:
                # Test querying all profiles
                start_time = time.time()
                all_profiles = profile_repo.get_all_profiles()
                query_time = time.time() - start_time
                
                assert len(all_profiles) == count
                
                memory_monitor.update()
                memory_stats = memory_monitor.get_stats()
                
                print(f"  Query time: {query_time:.3f}s")
                print(f"  Memory increase: {memory_stats['increase_mb']:.1f}MB")
                
                # Performance thresholds
                if query_time > 5:  # More than 5 seconds to query
                    print(f"  WARNING: Query time exceeded threshold at {count} profiles")
                
                if memory_stats['increase_mb'] > 200:  # More than 200MB
                    print(f"  WARNING: Memory usage exceeded threshold at {count} profiles")
                
            except Exception as e:
                print(f"  FAILED at {count} profiles: {e}")
                break
            
            # Cleanup for next iteration
            for profile_id in profile_ids:
                profile_repo.delete_profile(f"scale_user_{profile_id:05d}")
    
    def test_change_detection_scalability(self, temp_db):
        """Test change detection with very large datasets."""
        change_detector = ChangeDetector()
        
        # Test with increasingly large datasets
        sizes_to_test = [1000, 5000, 10000, 25000, 50000]
        
        for size in sizes_to_test:
            print(f"\nTesting change detection with {size:,} items")
            
            # Generate large datasets
            base_set = {f"user_{i:06d}" for i in range(size)}
            
            # Create modified set with 10% churn
            churn_size = size // 10
            modified_set = base_set.copy()
            
            # Remove 10%
            to_remove = {f"user_{i:06d}" for i in range(churn_size)}
            modified_set -= to_remove
            
            # Add 10% new
            new_items = {f"new_user_{i:06d}" for i in range(churn_size)}
            modified_set |= new_items
            
            memory_monitor = MemoryMonitor()
            memory_monitor.start()
            
            try:
                # Test change detection performance
                start_time = time.time()
                changes = change_detector.detect_changes(
                    "scale_test", 1,
                    modified_set, base_set,
                    set(), set()  # No following changes for this test
                )
                detection_time = time.time() - start_time
                
                memory_monitor.update()
                
                # Verify results
                expected_changes = churn_size * 2  # Removed + added
                assert len(changes) == expected_changes
                
                memory_stats = memory_monitor.get_stats()
                
                print(f"  Detection time: {detection_time:.3f}s")
                print(f"  Changes detected: {len(changes):,}")
                print(f"  Memory increase: {memory_stats['increase_mb']:.1f}MB")
                
                # Performance thresholds
                if detection_time > 10:  # More than 10 seconds
                    print(f"  WARNING: Detection time exceeded threshold at size {size:,}")
                
                if memory_stats['increase_mb'] > 100:  # More than 100MB
                    print(f"  WARNING: Memory usage exceeded threshold at size {size:,}")
                
            except Exception as e:
                print(f"  FAILED at size {size:,}: {e}")
                break