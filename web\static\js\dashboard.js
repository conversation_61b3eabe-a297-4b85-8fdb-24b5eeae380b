/**
 * Dashboard JavaScript functionality
 * Handles real-time data updates, interactive features, and AJAX requests
 */

// Global variables
let refreshInterval;
let isRefreshing = false;

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    startAutoRefresh();
});

/**
 * Initialize dashboard components
 */
function initializeDashboard() {
    updateLastUpdatedTime();
    loadDashboardStats();
    loadRecentChanges();
}

/**
 * Set up event listeners for interactive elements
 */
function setupEventListeners() {
    // Profile toggle switches
    document.querySelectorAll('.profile-toggle').forEach(toggle => {
        toggle.addEventListener('change', handleProfileToggle);
    });
    
    // Search form submission
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', handleSearchSubmit);
    }
    
    // Filter changes
    document.querySelectorAll('.filter-select').forEach(select => {
        select.addEventListener('change', handleFilterChange);
    });
    
    // Sort buttons
    document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.addEventListener('click', handleSortChange);
    });
}

/**
 * Start automatic refresh of dashboard data
 */
function startAutoRefresh() {
    // Refresh every 5 minutes
    refreshInterval = setInterval(() => {
        if (!isRefreshing) {
            refreshData();
        }
    }, 300000);
}

/**
 * Stop automatic refresh
 */
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

/**
 * Global refresh function called from base template
 */
async function pageRefresh() {
    return refreshData();
}

/**
 * Refresh dashboard data
 */
async function refreshData() {
    if (isRefreshing) return;
    
    isRefreshing = true;
    const refreshIcon = document.getElementById('refreshIcon');
    
    try {
        // Show loading state
        if (refreshIcon) {
            refreshIcon.classList.add('spinning');
        }
        
        // Load fresh data
        await Promise.all([
            loadDashboardStats(),
            loadRecentChanges(),
            loadMonitoringStatus()
        ]);
        
        updateLastUpdatedTime();
        showNotification('Data refreshed successfully', 'success');
        
    } catch (error) {
        console.error('Error refreshing data:', error);
        showNotification('Failed to refresh data', 'error');
    } finally {
        isRefreshing = false;
        if (refreshIcon) {
            refreshIcon.classList.remove('spinning');
        }
    }
}

/**
 * Load dashboard statistics
 */
async function loadDashboardStats() {
    try {
        const response = await fetch('/api/dashboard/stats');
        if (!response.ok) throw new Error('Failed to load stats');
        
        const stats = await response.json();
        updateStatsDisplay(stats);
        
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

/**
 * Load recent changes
 */
async function loadRecentChanges() {
    try {
        const response = await fetch('/api/dashboard/recent-changes?limit=10');
        if (!response.ok) throw new Error('Failed to load changes');
        
        const changes = await response.json();
        updateChangesDisplay(changes);
        
    } catch (error) {
        console.error('Error loading recent changes:', error);
    }
}

/**
 * Load monitoring status
 */
async function loadMonitoringStatus() {
    try {
        const response = await fetch('/api/monitoring/status');
        if (!response.ok) throw new Error('Failed to load status');
        
        const status = await response.json();
        updateStatusDisplay(status);
        
    } catch (error) {
        console.error('Error loading monitoring status:', error);
    }
}

/**
 * Update statistics display
 */
function updateStatsDisplay(stats) {
    const elements = {
        'totalProfiles': stats.total_profiles,
        'enabledProfiles': stats.enabled_profiles,
        'totalFollowers': stats.total_followers,
        'totalFollowing': stats.total_following,
        'changesToday': stats.changes_today
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            animateNumber(element, value);
        }
    });
}

/**
 * Update recent changes display
 */
function updateChangesDisplay(changes) {
    const container = document.getElementById('recentChanges');
    if (!container) return;
    
    if (changes.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent changes</p>';
        return;
    }
    
    const html = changes.map(change => `
        <div class="change-item change-${change.change_type} fade-in">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>@${change.profile_username}</strong>
                    <span class="text-muted">${change.display_text}</span>
                    <br>
                    <small class="text-muted">
                        <i class="bi bi-clock"></i> ${timeAgo(change.timestamp)}
                    </small>
                </div>
                <span class="badge bg-${getChangeTypeBadgeColor(change.change_type)}">
                    ${formatChangeType(change.change_type)}
                </span>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

/**
 * Update monitoring status display
 */
function updateStatusDisplay(status) {
    const statusElement = document.getElementById('monitoringStatus');
    if (!statusElement) return;
    
    const dueCount = status.profiles_due_for_scan;
    const statusClass = dueCount > 0 ? 'status-pending' : 'status-active';
    const statusText = dueCount > 0 ? `${dueCount} profiles due for scan` : 'All profiles up to date';
    
    statusElement.innerHTML = `
        <span class="status-indicator ${statusClass}"></span>
        ${statusText}
    `;
}

/**
 * Handle profile toggle switch changes
 */
async function handleProfileToggle(event) {
    const toggle = event.target;
    const username = toggle.dataset.username;
    const enabled = toggle.checked;
    
    try {
        const response = await fetch(`/profiles/${username}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            }
        });
        
        if (!response.ok) throw new Error('Failed to toggle profile');
        
        const result = await response.json();
        showNotification(result.message, 'success');
        
        // Update UI
        const statusIndicator = toggle.closest('.profile-card').querySelector('.status-indicator');
        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${enabled ? 'status-active' : 'status-inactive'}`;
        }
        
    } catch (error) {
        console.error('Error toggling profile:', error);
        showNotification('Failed to update profile status', 'error');
        
        // Revert toggle state
        toggle.checked = !enabled;
    }
}

/**
 * Handle search form submission
 */
function handleSearchSubmit(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const searchParams = new URLSearchParams(formData);
    
    // Redirect to search results
    window.location.href = `/search?${searchParams.toString()}`;
}

/**
 * Handle filter changes
 */
function handleFilterChange(event) {
    const form = event.target.closest('form');
    if (form) {
        form.submit();
    }
}

/**
 * Handle sort button clicks
 */
function handleSortChange(event) {
    const btn = event.target.closest('.sort-btn');
    const sortBy = btn.dataset.sortBy;
    const currentOrder = btn.dataset.sortOrder || 'desc';
    const newOrder = currentOrder === 'desc' ? 'asc' : 'desc';
    
    // Update URL with new sort parameters
    const url = new URL(window.location);
    url.searchParams.set('sort_by', sortBy);
    url.searchParams.set('sort_order', newOrder);
    
    window.location.href = url.toString();
}

/**
 * Animate number changes
 */
function animateNumber(element, targetValue) {
    const currentValue = parseInt(element.textContent) || 0;
    const increment = (targetValue - currentValue) / 20;
    let current = currentValue;
    
    const animation = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
            element.textContent = targetValue.toLocaleString();
            clearInterval(animation);
        } else {
            element.textContent = Math.round(current).toLocaleString();
        }
    }, 50);
}

/**
 * Show notification to user
 */
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Get CSRF token from meta tag or cookie
 */
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
}

/**
 * Get badge color for change type
 */
function getChangeTypeBadgeColor(changeType) {
    const colors = {
        'gained': 'success',
        'lost': 'danger',
        'started_following': 'primary',
        'stopped_following': 'warning'
    };
    return colors[changeType] || 'secondary';
}

/**
 * Format change type for display
 */
function formatChangeType(changeType) {
    const formats = {
        'gained': 'New Follower',
        'lost': 'Lost Follower',
        'started_following': 'Started Following',
        'stopped_following': 'Stopped Following'
    };
    return formats[changeType] || changeType;
}

/**
 * Update last updated time display
 */
function updateLastUpdatedTime() {
    const element = document.getElementById('lastUpdatedTime');
    if (element) {
        const now = new Date();
        element.textContent = `Updated at ${now.toLocaleTimeString()}`;
    }
}

/**
 * Format time ago string
 */
function timeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
}

// Export functions for use in other scripts
window.dashboardUtils = {
    refreshData,
    showNotification,
    timeAgo,
    getCSRFToken
};