"""
Backup and Recovery Manager for Instagram Follower Monitor

This module provides comprehensive backup and recovery functionality including:
- Automated database backup with encryption
- Data export/import utilities for profiles and changes
- Configuration backup and restore
- Data integrity validation and corruption detection
- Disaster recovery procedures
"""

import os
import json
import sqlite3
import shutil
import hashlib
import gzip
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from cryptography.fernet import Fernet
import logging

from database.connection import db_manager
from database.repositories import ProfileRepository, ChangeRepository, SettingsRepository
from config import Config

logger = logging.getLogger(__name__)


class BackupManager:
    """Manages backup and recovery operations for the application."""
    
    def __init__(self, config: Config):
        """Initialize backup manager with configuration."""
        self.config = config
        self.backup_dir = Path(__file__).parent.parent / 'backups'
        self.backup_dir.mkdir(exist_ok=True)
        
        # Initialize repositories
        self.profile_repo = ProfileRepository()
        self.change_repo = ChangeRepository()
        self.settings_repo = SettingsRepository()
        
        # Setup encryption for backups
        self.encryption_key = config.ENCRYPTION_KEY.encode() if isinstance(config.ENCRYPTION_KEY, str) else config.ENCRYPTION_KEY
        self.cipher = Fernet(self.encryption_key)
    
    def create_full_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a complete backup of the system including database, configuration, and metadata.
        
        Args:
            backup_name: Optional custom name for the backup
            
        Returns:
            Dictionary with backup information and file paths
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = backup_name or f"full_backup_{timestamp}"
            
            backup_info = {
                'backup_name': backup_name,
                'timestamp': timestamp,
                'type': 'full',
                'files': {},
                'checksums': {},
                'status': 'in_progress'
            }
            
            logger.info(f"Starting full backup: {backup_name}")
            
            # Create backup directory
            backup_path = self.backup_dir / backup_name
            backup_path.mkdir(exist_ok=True)
            
            # 1. Backup database
            db_backup_path = self._backup_database(backup_path)
            backup_info['files']['database'] = str(db_backup_path)
            backup_info['checksums']['database'] = self._calculate_checksum(db_backup_path)
            
            # 2. Backup configuration
            config_backup_path = self._backup_configuration(backup_path)
            backup_info['files']['configuration'] = str(config_backup_path)
            backup_info['checksums']['configuration'] = self._calculate_checksum(config_backup_path)
            
            # 3. Export data in JSON format
            data_export_path = self._export_data(backup_path)
            backup_info['files']['data_export'] = str(data_export_path)
            backup_info['checksums']['data_export'] = self._calculate_checksum(data_export_path)
            
            # 4. Create backup manifest
            backup_info['status'] = 'completed'
            manifest_path = backup_path / 'backup_manifest.json'
            with open(manifest_path, 'w') as f:
                json.dump(backup_info, f, indent=2)
            
            # 5. Encrypt the entire backup
            encrypted_backup_path = self._encrypt_backup(backup_path)
            
            logger.info(f"Full backup completed successfully: {encrypted_backup_path}")
            
            return {
                'success': True,
                'backup_path': str(encrypted_backup_path),
                'backup_info': backup_info
            }
            
        except Exception as e:
            logger.error(f"Full backup failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_incremental_backup(self, since_date: datetime, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Create an incremental backup containing only changes since the specified date.
        
        Args:
            since_date: Date from which to include changes
            backup_name: Optional custom name for the backup
            
        Returns:
            Dictionary with backup information and file paths
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = backup_name or f"incremental_backup_{timestamp}"
            
            backup_info = {
                'backup_name': backup_name,
                'timestamp': timestamp,
                'type': 'incremental',
                'since_date': since_date.isoformat(),
                'files': {},
                'checksums': {},
                'status': 'in_progress'
            }
            
            logger.info(f"Starting incremental backup since {since_date}: {backup_name}")
            
            # Create backup directory
            backup_path = self.backup_dir / backup_name
            backup_path.mkdir(exist_ok=True)
            
            # Export incremental data
            data_export_path = self._export_incremental_data(backup_path, since_date)
            backup_info['files']['incremental_data'] = str(data_export_path)
            backup_info['checksums']['incremental_data'] = self._calculate_checksum(data_export_path)
            
            # Backup current configuration
            config_backup_path = self._backup_configuration(backup_path)
            backup_info['files']['configuration'] = str(config_backup_path)
            backup_info['checksums']['configuration'] = self._calculate_checksum(config_backup_path)
            
            # Create backup manifest
            backup_info['status'] = 'completed'
            manifest_path = backup_path / 'backup_manifest.json'
            with open(manifest_path, 'w') as f:
                json.dump(backup_info, f, indent=2)
            
            # Encrypt the backup
            encrypted_backup_path = self._encrypt_backup(backup_path)
            
            logger.info(f"Incremental backup completed successfully: {encrypted_backup_path}")
            
            return {
                'success': True,
                'backup_path': str(encrypted_backup_path),
                'backup_info': backup_info
            }
            
        except Exception as e:
            logger.error(f"Incremental backup failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def restore_from_backup(self, backup_path: str, restore_options: Optional[Dict[str, bool]] = None) -> Dict[str, Any]:
        """
        Restore system from a backup file.
        
        Args:
            backup_path: Path to the encrypted backup file
            restore_options: Dictionary specifying what to restore (database, configuration, etc.)
            
        Returns:
            Dictionary with restoration results
        """
        try:
            restore_options = restore_options or {
                'database': True,
                'configuration': True,
                'validate_integrity': True
            }
            
            logger.info(f"Starting restore from backup: {backup_path}")
            
            # Decrypt and extract backup
            extracted_path = self._decrypt_backup(backup_path)
            
            # Load backup manifest
            manifest_path = extracted_path / 'backup_manifest.json'
            with open(manifest_path, 'r') as f:
                backup_info = json.load(f)
            
            restore_results = {
                'success': True,
                'restored_components': [],
                'errors': []
            }
            
            # Validate backup integrity before restoration
            if restore_options.get('validate_integrity', True):
                integrity_check = self._validate_backup_integrity(extracted_path, backup_info)
                if not integrity_check['valid']:
                    return {
                        'success': False,
                        'error': f"Backup integrity validation failed: {integrity_check['errors']}"
                    }
            
            # Create backup of current state before restoration
            current_backup = self.create_full_backup(f"pre_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            # Restore database
            if restore_options.get('database', True) and 'database' in backup_info['files']:
                db_restore_result = self._restore_database(extracted_path, backup_info)
                if db_restore_result['success']:
                    restore_results['restored_components'].append('database')
                else:
                    restore_results['errors'].append(f"Database restore failed: {db_restore_result['error']}")
            
            # Restore configuration
            if restore_options.get('configuration', True) and 'configuration' in backup_info['files']:
                config_restore_result = self._restore_configuration(extracted_path, backup_info)
                if config_restore_result['success']:
                    restore_results['restored_components'].append('configuration')
                else:
                    restore_results['errors'].append(f"Configuration restore failed: {config_restore_result['error']}")
            
            # Clean up extracted files
            shutil.rmtree(extracted_path)
            
            if restore_results['errors']:
                restore_results['success'] = False
            
            logger.info(f"Restore completed. Components restored: {restore_results['restored_components']}")
            
            return restore_results
            
        except Exception as e:
            logger.error(f"Restore from backup failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_profile_data(self, profile_username: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Export all data for a specific profile.
        
        Args:
            profile_username: Username of the profile to export
            output_path: Optional custom output path
            
        Returns:
            Dictionary with export results
        """
        try:
            profile = self.profile_repo.get_profile_by_username(profile_username)
            if not profile:
                return {
                    'success': False,
                    'error': f"Profile '{profile_username}' not found"
                }
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = output_path or str(self.backup_dir / f"profile_export_{profile_username}_{timestamp}.json")
            
            # Collect all profile data
            export_data = {
                'profile': {
                    'username': profile.profile_username,
                    'display_name': profile.display_name,
                    'is_private': profile.is_private,
                    'monitoring_enabled': profile.enabled,
                    'last_scan': profile.last_scan.isoformat() if profile.last_scan else None,
                    'created_at': profile.created_at.isoformat() if profile.created_at else None
                },
                'current_followers': list(self.change_repo.get_current_followers(profile.profile_id)),
                'current_following': list(self.change_repo.get_current_following(profile.profile_id)),
                'change_history': [],
                'statistics': self.profile_repo.get_profile_stats(profile.profile_id),
                'exported_at': datetime.now().isoformat(),
                'export_version': '1.0'
            }
            
            # Get all changes for this profile
            all_changes = self.change_repo.get_recent_changes(profile.profile_id, limit=10000)
            for change in all_changes:
                export_data['change_history'].append({
                    'affected_username': change.affected_username,
                    'change_type': change.change_type.value,
                    'timestamp': change.timestamp.isoformat()
                })
            
            # Write to file
            with open(output_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Profile data exported successfully: {output_path}")
            
            return {
                'success': True,
                'output_path': output_path,
                'records_exported': {
                    'followers': len(export_data['current_followers']),
                    'following': len(export_data['current_following']),
                    'changes': len(export_data['change_history'])
                }
            }
            
        except Exception as e:
            logger.error(f"Profile data export failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def import_profile_data(self, import_path: str, overwrite_existing: bool = False) -> Dict[str, Any]:
        """
        Import profile data from an export file.
        
        Args:
            import_path: Path to the export file
            overwrite_existing: Whether to overwrite existing profile data
            
        Returns:
            Dictionary with import results
        """
        try:
            with open(import_path, 'r') as f:
                import_data = json.load(f)
            
            profile_data = import_data['profile']
            profile_username = profile_data['username']
            
            # Check if profile already exists
            existing_profile = self.profile_repo.get_profile_by_username(profile_username)
            if existing_profile and not overwrite_existing:
                return {
                    'success': False,
                    'error': f"Profile '{profile_username}' already exists. Use overwrite_existing=True to replace."
                }
            
            # Import or update profile
            if existing_profile:
                # Update existing profile
                existing_profile.display_name = profile_data.get('display_name')
                existing_profile.is_private = profile_data.get('is_private', False)
                existing_profile.enabled = profile_data.get('monitoring_enabled', True)
                self.profile_repo.update_profile(existing_profile)
                profile_id = existing_profile.profile_id
            else:
                # Create new profile
                from models.data_models import MonitoringConfig
                new_profile = MonitoringConfig(
                    profile_username=profile_username,
                    enabled=profile_data.get('monitoring_enabled', True),
                    display_name=profile_data.get('display_name'),
                    is_private=profile_data.get('is_private', False)
                )
                profile_id = self.profile_repo.create_profile(new_profile)
            
            # Import current followers and following
            if 'current_followers' in import_data:
                followers_set = set(import_data['current_followers'])
                self.change_repo.store_current_followers(profile_id, followers_set)
            
            if 'current_following' in import_data:
                following_set = set(import_data['current_following'])
                self.change_repo.store_current_following(profile_id, following_set)
            
            # Import change history
            imported_changes = 0
            if 'change_history' in import_data:
                from models.data_models import FollowerChange, ChangeType
                changes = []
                for change_data in import_data['change_history']:
                    change = FollowerChange(
                        profile_username=profile_username,
                        affected_username=change_data['affected_username'],
                        change_type=ChangeType(change_data['change_type']),
                        timestamp=datetime.fromisoformat(change_data['timestamp']),
                        profile_id=profile_id
                    )
                    changes.append(change)
                
                self.change_repo.store_follower_changes(changes)
                imported_changes = len(changes)
            
            logger.info(f"Profile data imported successfully: {profile_username}")
            
            return {
                'success': True,
                'profile_username': profile_username,
                'records_imported': {
                    'followers': len(import_data.get('current_followers', [])),
                    'following': len(import_data.get('current_following', [])),
                    'changes': imported_changes
                }
            }
            
        except Exception as e:
            logger.error(f"Profile data import failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_database_integrity(self) -> Dict[str, Any]:
        """
        Perform comprehensive database integrity validation.
        
        Returns:
            Dictionary with validation results
        """
        try:
            validation_results = {
                'valid': True,
                'checks_performed': [],
                'errors': [],
                'warnings': [],
                'statistics': {}
            }
            
            logger.info("Starting database integrity validation")
            
            # 1. Check database file integrity
            db_path = self.config.get_database_path()
            if not db_path.exists():
                validation_results['valid'] = False
                validation_results['errors'].append("Database file does not exist")
                return validation_results
            
            validation_results['checks_performed'].append('file_existence')
            
            # 2. Check SQLite database integrity
            try:
                with db_manager.get_connection() as conn:
                    cursor = conn.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()
                    if integrity_result[0] != 'ok':
                        validation_results['valid'] = False
                        validation_results['errors'].append(f"SQLite integrity check failed: {integrity_result[0]}")
                    else:
                        validation_results['checks_performed'].append('sqlite_integrity')
            except Exception as e:
                validation_results['valid'] = False
                validation_results['errors'].append(f"SQLite integrity check error: {e}")
            
            # 3. Check foreign key constraints
            try:
                with db_manager.get_connection() as conn:
                    cursor = conn.execute("PRAGMA foreign_key_check")
                    fk_violations = cursor.fetchall()
                    if fk_violations:
                        validation_results['valid'] = False
                        validation_results['errors'].append(f"Foreign key violations found: {len(fk_violations)}")
                    else:
                        validation_results['checks_performed'].append('foreign_key_constraints')
            except Exception as e:
                validation_results['errors'].append(f"Foreign key check error: {e}")
            
            # 4. Validate data consistency
            consistency_check = self._validate_data_consistency()
            validation_results['checks_performed'].extend(consistency_check['checks_performed'])
            validation_results['errors'].extend(consistency_check['errors'])
            validation_results['warnings'].extend(consistency_check['warnings'])
            if not consistency_check['valid']:
                validation_results['valid'] = False
            
            # 5. Collect database statistics
            validation_results['statistics'] = self._collect_database_statistics()
            validation_results['checks_performed'].append('statistics_collection')
            
            logger.info(f"Database integrity validation completed. Valid: {validation_results['valid']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Database integrity validation failed: {e}")
            return {
                'valid': False,
                'error': str(e)
            }
    
    def cleanup_old_backups(self, retention_days: int = 30) -> Dict[str, Any]:
        """
        Clean up old backup files based on retention policy.
        
        Args:
            retention_days: Number of days to retain backups
            
        Returns:
            Dictionary with cleanup results
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            deleted_files = []
            total_size_freed = 0
            
            logger.info(f"Starting backup cleanup for files older than {retention_days} days")
            
            for backup_file in self.backup_dir.glob('*.backup'):
                file_mtime = datetime.fromtimestamp(backup_file.stat().st_mtime)
                if file_mtime < cutoff_date:
                    file_size = backup_file.stat().st_size
                    backup_file.unlink()
                    deleted_files.append(str(backup_file))
                    total_size_freed += file_size
            
            # Also clean up unencrypted backup directories
            for backup_dir in self.backup_dir.iterdir():
                if backup_dir.is_dir():
                    dir_mtime = datetime.fromtimestamp(backup_dir.stat().st_mtime)
                    if dir_mtime < cutoff_date:
                        shutil.rmtree(backup_dir)
                        deleted_files.append(str(backup_dir))
            
            logger.info(f"Backup cleanup completed. Deleted {len(deleted_files)} files, freed {total_size_freed} bytes")
            
            return {
                'success': True,
                'deleted_files': deleted_files,
                'total_size_freed': total_size_freed,
                'retention_days': retention_days
            }
            
        except Exception as e:
            logger.error(f"Backup cleanup failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def list_available_backups(self) -> List[Dict[str, Any]]:
        """
        List all available backup files with metadata.
        
        Returns:
            List of backup information dictionaries
        """
        try:
            backups = []
            
            for backup_file in self.backup_dir.glob('*.backup'):
                try:
                    # Try to decrypt and read manifest
                    temp_extract_path = self._decrypt_backup(str(backup_file))
                    manifest_path = temp_extract_path / 'backup_manifest.json'
                    
                    if manifest_path.exists():
                        with open(manifest_path, 'r') as f:
                            backup_info = json.load(f)
                        
                        backup_info['file_path'] = str(backup_file)
                        backup_info['file_size'] = backup_file.stat().st_size
                        backup_info['file_modified'] = datetime.fromtimestamp(backup_file.stat().st_mtime).isoformat()
                        
                        backups.append(backup_info)
                    
                    # Clean up temporary extraction
                    shutil.rmtree(temp_extract_path)
                    
                except Exception as e:
                    logger.warning(f"Could not read backup metadata for {backup_file}: {e}")
                    # Add basic file info even if we can't read the manifest
                    backups.append({
                        'backup_name': backup_file.stem,
                        'file_path': str(backup_file),
                        'file_size': backup_file.stat().st_size,
                        'file_modified': datetime.fromtimestamp(backup_file.stat().st_mtime).isoformat(),
                        'status': 'metadata_unavailable'
                    })
            
            # Sort by timestamp/modification date
            backups.sort(key=lambda x: x.get('timestamp', x.get('file_modified', '')), reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"Failed to list available backups: {e}")
            return []
    
    def schedule_automated_backup(self, interval_hours: int = 24) -> Dict[str, Any]:
        """
        Schedule automated backup creation.
        
        Args:
            interval_hours: Interval between automated backups in hours
            
        Returns:
            Dictionary with scheduling results
        """
        try:
            from services.scheduler import scheduler_manager
            
            # Remove existing backup job if it exists
            try:
                scheduler_manager.remove_job('automated_backup')
            except:
                pass  # Job might not exist
            
            # Schedule new backup job
            job_id = scheduler_manager.add_job(
                func=self._automated_backup_job,
                trigger='interval',
                hours=interval_hours,
                id='automated_backup',
                name='Automated System Backup',
                replace_existing=True
            )
            
            # Store backup schedule in settings
            self.settings_repo.set_setting('automated_backup_enabled', 'true')
            self.settings_repo.set_setting('automated_backup_interval_hours', str(interval_hours))
            
            logger.info(f"Automated backup scheduled every {interval_hours} hours")
            
            return {
                'success': True,
                'job_id': job_id,
                'interval_hours': interval_hours
            }
            
        except Exception as e:
            logger.error(f"Failed to schedule automated backup: {e}")
            return {
                'success': False,
                'error': str(e)
            }    
    
    def _backup_database(self, backup_path: Path) -> Path:
        """Create a backup of the SQLite database."""
        db_source = self.config.get_database_path()
        db_backup_path = backup_path / 'database.db'
        
        # Use SQLite backup API for consistent backup
        with sqlite3.connect(str(db_source)) as source_conn:
            with sqlite3.connect(str(db_backup_path)) as backup_conn:
                source_conn.backup(backup_conn)
        
        logger.debug(f"Database backed up to: {db_backup_path}")
        return db_backup_path
    
    def _backup_configuration(self, backup_path: Path) -> Path:
        """Backup system configuration and settings."""
        config_backup_path = backup_path / 'configuration.json'
        
        # Export all settings
        settings_data = self.settings_repo.export_settings()
        
        # Add application configuration (non-sensitive)
        config_data = {
            'settings': settings_data,
            'app_config': self.config.to_dict(),
            'backup_timestamp': datetime.now().isoformat()
        }
        
        with open(config_backup_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        logger.debug(f"Configuration backed up to: {config_backup_path}")
        return config_backup_path
    
    def _export_data(self, backup_path: Path) -> Path:
        """Export all application data in JSON format."""
        data_export_path = backup_path / 'data_export.json'
        
        # Collect all data
        export_data = {
            'profiles': [],
            'follower_data': {},
            'following_data': {},
            'change_history': [],
            'export_metadata': {
                'timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'total_profiles': 0,
                'total_changes': 0
            }
        }
        
        # Export all profiles
        profiles = self.profile_repo.get_all_profiles()
        for profile in profiles:
            profile_data = {
                'username': profile.profile_username,
                'display_name': profile.display_name,
                'is_private': profile.is_private,
                'monitoring_enabled': profile.enabled,
                'last_scan': profile.last_scan.isoformat() if profile.last_scan else None,
                'created_at': profile.created_at.isoformat() if profile.created_at else None,
                'profile_id': profile.profile_id
            }
            export_data['profiles'].append(profile_data)
            
            # Export current followers and following
            export_data['follower_data'][profile.profile_id] = list(
                self.change_repo.get_current_followers(profile.profile_id)
            )
            export_data['following_data'][profile.profile_id] = list(
                self.change_repo.get_current_following(profile.profile_id)
            )
        
        # Export change history (limit to prevent huge files)
        all_changes = self.change_repo.get_recent_changes(limit=50000)
        for change in all_changes:
            change_data = {
                'profile_id': change.profile_id,
                'profile_username': change.profile_username,
                'affected_username': change.affected_username,
                'change_type': change.change_type.value,
                'timestamp': change.timestamp.isoformat()
            }
            export_data['change_history'].append(change_data)
        
        # Update metadata
        export_data['export_metadata']['total_profiles'] = len(profiles)
        export_data['export_metadata']['total_changes'] = len(export_data['change_history'])
        
        # Write compressed JSON
        with gzip.open(data_export_path, 'wt') as f:
            json.dump(export_data, f, indent=2)
        
        logger.debug(f"Data exported to: {data_export_path}")
        return data_export_path
    
    def _export_incremental_data(self, backup_path: Path, since_date: datetime) -> Path:
        """Export only data changed since the specified date."""
        data_export_path = backup_path / 'incremental_data.json'
        
        export_data = {
            'since_date': since_date.isoformat(),
            'changes': [],
            'updated_profiles': [],
            'export_metadata': {
                'timestamp': datetime.now().isoformat(),
                'type': 'incremental',
                'version': '1.0'
            }
        }
        
        # Get changes since the specified date
        end_date = datetime.now()
        changes = self.change_repo.get_changes_by_date_range(since_date, end_date)
        
        for change in changes:
            change_data = {
                'profile_id': change.profile_id,
                'profile_username': change.profile_username,
                'affected_username': change.affected_username,
                'change_type': change.change_type.value,
                'timestamp': change.timestamp.isoformat()
            }
            export_data['changes'].append(change_data)
        
        # Get profiles updated since the date
        profiles = self.profile_repo.get_all_profiles()
        for profile in profiles:
            if profile.last_scan and profile.last_scan >= since_date:
                profile_data = {
                    'username': profile.profile_username,
                    'display_name': profile.display_name,
                    'is_private': profile.is_private,
                    'monitoring_enabled': profile.enabled,
                    'last_scan': profile.last_scan.isoformat(),
                    'profile_id': profile.profile_id,
                    'current_followers': list(self.change_repo.get_current_followers(profile.profile_id)),
                    'current_following': list(self.change_repo.get_current_following(profile.profile_id))
                }
                export_data['updated_profiles'].append(profile_data)
        
        export_data['export_metadata']['total_changes'] = len(export_data['changes'])
        export_data['export_metadata']['total_updated_profiles'] = len(export_data['updated_profiles'])
        
        with gzip.open(data_export_path, 'wt') as f:
            json.dump(export_data, f, indent=2)
        
        logger.debug(f"Incremental data exported to: {data_export_path}")
        return data_export_path
    
    def _encrypt_backup(self, backup_path: Path) -> Path:
        """Encrypt the entire backup directory into a single file."""
        encrypted_path = backup_path.with_suffix('.backup')
        
        # Create tar.gz of the backup directory
        import tarfile
        temp_tar_path = backup_path.with_suffix('.tar.gz')
        
        with tarfile.open(temp_tar_path, 'w:gz') as tar:
            tar.add(backup_path, arcname=backup_path.name)
        
        # Encrypt the tar.gz file
        with open(temp_tar_path, 'rb') as f:
            encrypted_data = self.cipher.encrypt(f.read())
        
        with open(encrypted_path, 'wb') as f:
            f.write(encrypted_data)
        
        # Clean up temporary files
        temp_tar_path.unlink()
        shutil.rmtree(backup_path)
        
        logger.debug(f"Backup encrypted to: {encrypted_path}")
        return encrypted_path
    
    def _decrypt_backup(self, backup_path: str) -> Path:
        """Decrypt and extract a backup file."""
        backup_file = Path(backup_path)
        extract_path = backup_file.parent / f"temp_extract_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Decrypt the file
        with open(backup_file, 'rb') as f:
            encrypted_data = f.read()
        
        decrypted_data = self.cipher.decrypt(encrypted_data)
        
        # Extract tar.gz
        import tarfile
        import io
        
        with tarfile.open(fileobj=io.BytesIO(decrypted_data), mode='r:gz') as tar:
            tar.extractall(extract_path)
        
        # Return path to the extracted backup directory
        extracted_backup_path = extract_path / backup_file.stem
        return extracted_backup_path
    
    def _restore_database(self, backup_path: Path, backup_info: Dict[str, Any]) -> Dict[str, Any]:
        """Restore database from backup."""
        try:
            db_backup_file = backup_path / 'database.db'
            if not db_backup_file.exists():
                return {'success': False, 'error': 'Database backup file not found'}
            
            # Validate backup database integrity
            try:
                with sqlite3.connect(str(db_backup_file)) as conn:
                    cursor = conn.execute("PRAGMA integrity_check")
                    result = cursor.fetchone()
                    if result[0] != 'ok':
                        return {'success': False, 'error': f'Backup database integrity check failed: {result[0]}'}
            except Exception as e:
                return {'success': False, 'error': f'Backup database validation failed: {e}'}
            
            # Create backup of current database
            current_db_path = self.config.get_database_path()
            backup_current_path = current_db_path.with_suffix('.pre_restore.db')
            if current_db_path.exists():
                shutil.copy2(current_db_path, backup_current_path)
            
            # Replace current database with backup
            shutil.copy2(db_backup_file, current_db_path)
            
            logger.info("Database restored successfully")
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Database restore failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _restore_configuration(self, backup_path: Path, backup_info: Dict[str, Any]) -> Dict[str, Any]:
        """Restore configuration from backup."""
        try:
            config_backup_file = backup_path / 'configuration.json'
            if not config_backup_file.exists():
                return {'success': False, 'error': 'Configuration backup file not found'}
            
            with open(config_backup_file, 'r') as f:
                config_data = json.load(f)
            
            # Restore settings
            if 'settings' in config_data:
                success = self.settings_repo.import_settings(config_data['settings'])
                if not success:
                    return {'success': False, 'error': 'Failed to import settings'}
            
            logger.info("Configuration restored successfully")
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Configuration restore failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _validate_backup_integrity(self, backup_path: Path, backup_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the integrity of a backup."""
        validation_result = {
            'valid': True,
            'errors': []
        }
        
        try:
            # Check if all expected files exist
            for file_type, file_path in backup_info.get('files', {}).items():
                backup_file = backup_path / Path(file_path).name
                if not backup_file.exists():
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Missing backup file: {file_type}")
                    continue
                
                # Verify checksums
                expected_checksum = backup_info.get('checksums', {}).get(file_type)
                if expected_checksum:
                    actual_checksum = self._calculate_checksum(backup_file)
                    if actual_checksum != expected_checksum:
                        validation_result['valid'] = False
                        validation_result['errors'].append(f"Checksum mismatch for {file_type}")
            
            return validation_result
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Backup validation error: {e}"]
            }
    
    def _validate_data_consistency(self) -> Dict[str, Any]:
        """Validate data consistency within the database."""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'checks_performed': []
        }
        
        try:
            # Check for orphaned records
            orphaned_followers = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM current_followers cf
                LEFT JOIN profiles p ON cf.profile_id = p.id
                WHERE p.id IS NULL
            """)
            
            if orphaned_followers[0]['count'] > 0:
                validation_result['warnings'].append(f"Found {orphaned_followers[0]['count']} orphaned follower records")
            
            validation_result['checks_performed'].append('orphaned_followers')
            
            # Check for orphaned following records
            orphaned_following = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM current_following cf
                LEFT JOIN profiles p ON cf.profile_id = p.id
                WHERE p.id IS NULL
            """)
            
            if orphaned_following[0]['count'] > 0:
                validation_result['warnings'].append(f"Found {orphaned_following[0]['count']} orphaned following records")
            
            validation_result['checks_performed'].append('orphaned_following')
            
            # Check for duplicate profiles
            duplicate_profiles = db_manager.execute_query("""
                SELECT username, COUNT(*) as count FROM profiles
                GROUP BY username HAVING COUNT(*) > 1
            """)
            
            if duplicate_profiles:
                validation_result['valid'] = False
                validation_result['errors'].append(f"Found {len(duplicate_profiles)} duplicate profile usernames")
            
            validation_result['checks_performed'].append('duplicate_profiles')
            
            # Check for invalid timestamps
            invalid_timestamps = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM follower_changes
                WHERE timestamp > datetime('now')
            """)
            
            if invalid_timestamps[0]['count'] > 0:
                validation_result['warnings'].append(f"Found {invalid_timestamps[0]['count']} future timestamps in follower changes")
            
            validation_result['checks_performed'].append('invalid_timestamps')
            
            return validation_result
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Data consistency validation error: {e}"],
                'checks_performed': validation_result['checks_performed']
            }
    
    def _collect_database_statistics(self) -> Dict[str, Any]:
        """Collect comprehensive database statistics."""
        try:
            stats = {}
            
            # Table row counts
            tables = ['profiles', 'current_followers', 'current_following', 'follower_changes', 'following_changes', 'settings']
            for table in tables:
                count_result = db_manager.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                stats[f"{table}_count"] = count_result[0]['count']
            
            # Database file size
            db_path = self.config.get_database_path()
            if db_path.exists():
                stats['database_size_bytes'] = db_path.stat().st_size
            
            # Date ranges
            oldest_change = db_manager.execute_query("""
                SELECT MIN(timestamp) as oldest FROM (
                    SELECT timestamp FROM follower_changes
                    UNION ALL
                    SELECT timestamp FROM following_changes
                )
            """)
            
            newest_change = db_manager.execute_query("""
                SELECT MAX(timestamp) as newest FROM (
                    SELECT timestamp FROM follower_changes
                    UNION ALL
                    SELECT timestamp FROM following_changes
                )
            """)
            
            if oldest_change[0]['oldest']:
                stats['oldest_change'] = oldest_change[0]['oldest']
            if newest_change[0]['newest']:
                stats['newest_change'] = newest_change[0]['newest']
            
            # Active profiles
            active_profiles = db_manager.execute_query("""
                SELECT COUNT(*) as count FROM profiles WHERE monitoring_enabled = 1
            """)
            stats['active_profiles'] = active_profiles[0]['count']
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to collect database statistics: {e}")
            return {'error': str(e)}
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate SHA-256 checksum of a file."""
        sha256_hash = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def _automated_backup_job(self) -> None:
        """Job function for automated backups."""
        try:
            logger.info("Starting automated backup job")
            
            # Create full backup
            backup_result = self.create_full_backup()
            
            if backup_result['success']:
                logger.info(f"Automated backup completed successfully: {backup_result['backup_path']}")
                
                # Clean up old backups
                retention_days = int(self.settings_repo.get_setting('backup_retention_days') or '30')
                cleanup_result = self.cleanup_old_backups(retention_days)
                
                if cleanup_result['success']:
                    logger.info(f"Backup cleanup completed: {len(cleanup_result['deleted_files'])} files removed")
            else:
                logger.error(f"Automated backup failed: {backup_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Automated backup job failed: {e}")


# Disaster Recovery Procedures Documentation
DISASTER_RECOVERY_PROCEDURES = """
# Disaster Recovery Procedures for Instagram Follower Monitor

## Overview
This document outlines the procedures for recovering from various disaster scenarios
that may affect the Instagram Follower Monitor application.

## Backup Strategy
- **Full Backups**: Complete system backup including database, configuration, and data
- **Incremental Backups**: Changes since last backup for efficient storage
- **Automated Backups**: Scheduled backups with configurable retention
- **Encryption**: All backups are encrypted using Fernet symmetric encryption

## Recovery Scenarios

### 1. Database Corruption
**Symptoms**: SQLite integrity check failures, application crashes, data inconsistencies

**Recovery Steps**:
1. Stop the application immediately
2. Run database integrity validation: `backup_manager.validate_database_integrity()`
3. If corruption is confirmed, restore from latest backup:
   ```python
   backup_manager.restore_from_backup('path/to/backup.backup', {'database': True})
   ```
4. Verify restored database integrity
5. Restart application and monitor for issues

### 2. Complete Data Loss
**Symptoms**: Database file missing, storage device failure

**Recovery Steps**:
1. Identify the most recent full backup
2. Restore complete system:
   ```python
   backup_manager.restore_from_backup('path/to/backup.backup')
   ```
3. Verify all components are restored correctly
4. Update configuration if necessary
5. Restart monitoring services

### 3. Configuration Loss
**Symptoms**: Settings reset to defaults, authentication failures

**Recovery Steps**:
1. Restore configuration from backup:
   ```python
   backup_manager.restore_from_backup('path/to/backup.backup', {'configuration': True})
   ```
2. Verify Instagram credentials are restored
3. Check monitoring intervals and other settings
4. Restart application

### 4. Partial Data Loss (Profile-Specific)
**Symptoms**: Missing data for specific profiles

**Recovery Steps**:
1. Export profile data from backup if available
2. Import specific profile data:
   ```python
   backup_manager.import_profile_data('profile_export.json')
   ```
3. Verify data integrity for affected profiles
4. Resume monitoring for restored profiles

## Prevention Measures

### Regular Backups
- Enable automated backups with appropriate frequency
- Test backup restoration procedures regularly
- Monitor backup job success/failure
- Maintain multiple backup copies in different locations

### Monitoring
- Set up database integrity checks
- Monitor disk space and storage health
- Log all critical operations
- Implement alerting for backup failures

### Best Practices
- Keep backup encryption keys secure and separate from backups
- Document all configuration changes
- Test disaster recovery procedures in non-production environment
- Maintain offline copies of critical documentation

## Emergency Contacts and Resources
- Application logs: `logs/` directory
- Backup location: `backups/` directory
- Configuration files: `.env`, `config.py`
- Database file: `instagram_monitor.db`

## Recovery Validation Checklist
After any recovery operation, verify:
- [ ] Database integrity check passes
- [ ] All profiles are present and configured correctly
- [ ] Recent changes data is available
- [ ] Authentication credentials work
- [ ] Monitoring jobs are scheduled and running
- [ ] Web interface is accessible
- [ ] API endpoints respond correctly
- [ ] Logs show normal operation

## Backup File Formats
- **Full Backup**: Encrypted tar.gz containing database, configuration, and data export
- **Profile Export**: JSON file with complete profile data and history
- **Configuration Backup**: JSON file with all system settings

## Recovery Time Objectives (RTO)
- Database corruption: 15-30 minutes
- Complete data loss: 30-60 minutes
- Configuration loss: 5-15 minutes
- Profile-specific loss: 10-20 minutes

## Recovery Point Objectives (RPO)
- With daily backups: Up to 24 hours of data loss
- With hourly backups: Up to 1 hour of data loss
- With real-time replication: Minimal data loss

For additional support or complex recovery scenarios, consult the application
documentation and logs for detailed error information.
"""


def create_disaster_recovery_documentation() -> str:
    """Create disaster recovery documentation file."""
    doc_path = Path(__file__).parent.parent / 'docs' / 'disaster_recovery.md'
    doc_path.parent.mkdir(exist_ok=True)
    
    with open(doc_path, 'w') as f:
        f.write(DISASTER_RECOVERY_PROCEDURES)
    
    return str(doc_path)