# Instagram Follower Monitor - Operations Guide

This guide provides comprehensive information for operating and maintaining the Instagram Follower Monitor in production environments.

## Table of Contents

1. [Daily Operations](#daily-operations)
2. [Monitoring and Alerting](#monitoring-and-alerting)
3. [Backup and Recovery](#backup-and-recovery)
4. [Performance Management](#performance-management)
5. [Maintenance Procedures](#maintenance-procedures)
6. [Scaling and Capacity Planning](#scaling-and-capacity-planning)
7. [Incident Management](#incident-management)
8. [Compliance and Auditing](#compliance-and-auditing)

## Daily Operations

### Health Checks

#### Automated Health Monitoring
```bash
#!/bin/bash
# Daily health check script

APP_URL="http://localhost"
LOG_FILE="/var/log/instagram-monitor/health-check.log"

# Function to log with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check application health
check_app_health() {
    if curl -f -s "$APP_URL/health" > /dev/null; then
        log_message "✓ Application health check passed"
        return 0
    else
        log_message "✗ Application health check failed"
        return 1
    fi
}

# Check database connectivity
check_database() {
    if sudo -u www-data /var/www/instagram-monitor/venv/bin/python -c "
from database.connection import DatabaseManager
db = DatabaseManager()
assert db.test_connection()
print('Database OK')
" 2>/dev/null; then
        log_message "✓ Database connectivity check passed"
        return 0
    else
        log_message "✗ Database connectivity check failed"
        return 1
    fi
}

# Check service status
check_services() {
    services=("instagram-monitor" "nginx")
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log_message "✓ Service $service is running"
        else
            log_message "✗ Service $service is not running"
            return 1
        fi
    done
    return 0
}

# Check disk space
check_disk_space() {
    usage=$(df /var/www/instagram-monitor | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$usage" -lt 80 ]; then
        log_message "✓ Disk usage is $usage% (OK)"
        return 0
    else
        log_message "⚠ Disk usage is $usage% (WARNING)"
        return 1
    fi
}

# Check memory usage
check_memory() {
    mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$mem_usage" -lt 80 ]; then
        log_message "✓ Memory usage is $mem_usage% (OK)"
        return 0
    else
        log_message "⚠ Memory usage is $mem_usage% (WARNING)"
        return 1
    fi
}

# Main health check
main() {
    log_message "Starting daily health check"
    
    failed_checks=0
    
    check_app_health || ((failed_checks++))
    check_database || ((failed_checks++))
    check_services || ((failed_checks++))
    check_disk_space || ((failed_checks++))
    check_memory || ((failed_checks++))
    
    if [ $failed_checks -eq 0 ]; then
        log_message "All health checks passed"
    else
        log_message "$failed_checks health checks failed"
        # Send alert email
        echo "Health check failures detected. Check $LOG_FILE for details." | \
            mail -s "Instagram Monitor Health Alert" <EMAIL>
    fi
}

main
```

#### Manual Health Verification
```bash
# Quick health check commands
curl -f http://localhost/health
systemctl status instagram-monitor nginx
df -h /var/www/instagram-monitor
free -h
tail -n 20 /var/www/instagram-monitor/logs/application.log
```

### Log Management

#### Daily Log Review
```bash
#!/bin/bash
# Daily log analysis script

LOG_DIR="/var/www/instagram-monitor/logs"
REPORT_FILE="/tmp/daily-log-report-$(date +%Y%m%d).txt"

{
    echo "Instagram Follower Monitor - Daily Log Report"
    echo "Date: $(date)"
    echo "=============================================="
    echo
    
    # Error summary
    echo "ERROR SUMMARY:"
    echo "--------------"
    grep -h "ERROR" "$LOG_DIR"/*.log | tail -20
    echo
    
    # Warning summary
    echo "WARNING SUMMARY:"
    echo "----------------"
    grep -h "WARNING" "$LOG_DIR"/*.log | tail -10
    echo
    
    # Authentication failures
    echo "AUTHENTICATION FAILURES:"
    echo "------------------------"
    grep -h "authentication.*failed" "$LOG_DIR"/*.log | tail -10
    echo
    
    # Rate limiting events
    echo "RATE LIMITING EVENTS:"
    echo "---------------------"
    grep -h "rate.limit\|429" "$LOG_DIR"/*.log | tail -10
    echo
    
    # Performance metrics
    echo "PERFORMANCE METRICS:"
    echo "--------------------"
    echo "Log file sizes:"
    ls -lh "$LOG_DIR"/*.log
    echo
    
    # Database size
    echo "Database size:"
    ls -lh /var/www/instagram-monitor/data/instagram_monitor.db
    
} > "$REPORT_FILE"

# Email report if there are errors or warnings
if grep -q "ERROR\|WARNING" "$REPORT_FILE"; then
    mail -s "Daily Log Report - Issues Found" <EMAIL> < "$REPORT_FILE"
fi
```

### Monitoring Dashboard

#### Key Metrics to Monitor
1. **Application Metrics**:
   - Response time
   - Request rate
   - Error rate
   - Active monitoring jobs

2. **System Metrics**:
   - CPU usage
   - Memory usage
   - Disk space
   - Network I/O

3. **Business Metrics**:
   - Number of monitored profiles
   - Changes detected per day
   - Authentication success rate
   - Data retention compliance

## Monitoring and Alerting

### Prometheus Configuration

#### Application Metrics Export
```python
# metrics.py - Application metrics for Prometheus
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from flask import Response
import time

# Metrics definitions
REQUEST_COUNT = Counter('instagram_monitor_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('instagram_monitor_request_duration_seconds', 'Request latency')
ACTIVE_PROFILES = Gauge('instagram_monitor_active_profiles', 'Number of active profiles')
MONITORING_ERRORS = Counter('instagram_monitor_errors_total', 'Total monitoring errors', ['type'])
LAST_SCAN_TIME = Gauge('instagram_monitor_last_scan_timestamp', 'Last successful scan timestamp')

class MetricsMiddleware:
    def __init__(self, app):
        self.app = app
        
    def __call__(self, environ, start_response):
        start_time = time.time()
        
        def new_start_response(status, response_headers, exc_info=None):
            REQUEST_COUNT.labels(
                method=environ['REQUEST_METHOD'],
                endpoint=environ['PATH_INFO']
            ).inc()
            
            REQUEST_LATENCY.observe(time.time() - start_time)
            return start_response(status, response_headers, exc_info)
        
        return self.app(environ, new_start_response)

@app.route('/metrics')
def metrics():
    # Update business metrics
    from database.repositories import ProfileRepository
    profile_repo = ProfileRepository()
    ACTIVE_PROFILES.set(profile_repo.get_active_profile_count())
    
    return Response(generate_latest(), mimetype='text/plain')
```

#### Grafana Dashboard Configuration
```json
{
  "dashboard": {
    "title": "Instagram Follower Monitor",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(instagram_monitor_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(instagram_monitor_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Active Profiles",
        "type": "singlestat",
        "targets": [
          {
            "expr": "instagram_monitor_active_profiles",
            "legendFormat": "Profiles"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(instagram_monitor_errors_total[5m])",
            "legendFormat": "{{type}}"
          }
        ]
      }
    ]
  }
}
```

### Alert Rules

#### Prometheus Alert Rules
```yaml
# alerts.yml
groups:
  - name: instagram_monitor
    rules:
      - alert: HighErrorRate
        expr: rate(instagram_monitor_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"
      
      - alert: ApplicationDown
        expr: up{job="instagram-monitor"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Instagram Monitor application is down"
          description: "The application has been down for more than 1 minute"
      
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(instagram_monitor_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"
      
      - alert: DiskSpaceHigh
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space usage high"
          description: "Disk usage is above 80%"
```

### Notification Channels

#### Email Alerts
```python
# alerting.py
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging

class AlertManager:
    def __init__(self, smtp_server, smtp_port, username, password):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.logger = logging.getLogger(__name__)
    
    def send_alert(self, subject, message, recipients, severity='info'):
        try:
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = f"[{severity.upper()}] {subject}"
            
            body = f"""
            Instagram Follower Monitor Alert
            
            Severity: {severity.upper()}
            Time: {datetime.now().isoformat()}
            
            Message:
            {message}
            
            Please check the application logs for more details.
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"Alert sent: {subject}")
            
        except Exception as e:
            self.logger.error(f"Failed to send alert: {e}")
```

#### Slack Integration
```python
# slack_alerts.py
import requests
import json

class SlackAlerter:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
    
    def send_alert(self, message, severity='info'):
        color_map = {
            'info': '#36a64f',
            'warning': '#ff9900',
            'error': '#ff0000',
            'critical': '#8B0000'
        }
        
        payload = {
            "attachments": [
                {
                    "color": color_map.get(severity, '#36a64f'),
                    "title": "Instagram Follower Monitor Alert",
                    "text": message,
                    "fields": [
                        {
                            "title": "Severity",
                            "value": severity.upper(),
                            "short": True
                        },
                        {
                            "title": "Time",
                            "value": datetime.now().isoformat(),
                            "short": True
                        }
                    ]
                }
            ]
        }
        
        try:
            response = requests.post(self.webhook_url, json=payload)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to send Slack alert: {e}")
```

## Backup and Recovery

### Automated Backup System

#### Comprehensive Backup Script
```bash
#!/bin/bash
# comprehensive-backup.sh

set -e

# Configuration
BACKUP_DIR="/var/backups/instagram-monitor"
APP_DIR="/var/www/instagram-monitor"
RETENTION_DAYS=30
ENCRYPTION_KEY_FILE="/etc/instagram-monitor/backup.key"
S3_BUCKET="your-backup-bucket"
DATE=$(date +%Y%m%d_%H%M%S)

# Logging
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$BACKUP_DIR/backup.log"
}

# Create backup directory
mkdir -p "$BACKUP_DIR"

log "Starting backup process"

# Stop application for consistent backup
log "Stopping application"
systemctl stop instagram-monitor

# Backup database
log "Backing up database"
sqlite3 "$APP_DIR/data/instagram_monitor.db" ".backup $BACKUP_DIR/database_$DATE.db"

# Backup configuration
log "Backing up configuration"
cp "$APP_DIR/.env" "$BACKUP_DIR/config_$DATE.env"

# Backup logs (last 7 days)
log "Backing up recent logs"
find "$APP_DIR/logs" -name "*.log" -mtime -7 -exec cp {} "$BACKUP_DIR/" \;

# Create application code snapshot
log "Creating code snapshot"
tar -czf "$BACKUP_DIR/code_$DATE.tar.gz" -C "$APP_DIR" \
    --exclude='logs/*' \
    --exclude='data/*' \
    --exclude='backups/*' \
    --exclude='venv/*' \
    --exclude='.git/*' \
    .

# Restart application
log "Restarting application"
systemctl start instagram-monitor

# Create encrypted archive
log "Creating encrypted backup archive"
tar -czf - \
    "$BACKUP_DIR/database_$DATE.db" \
    "$BACKUP_DIR/config_$DATE.env" \
    "$BACKUP_DIR/code_$DATE.tar.gz" \
    "$BACKUP_DIR"/*.log | \
    gpg --cipher-algo AES256 --compress-algo 1 --s2k-mode 3 \
        --s2k-digest-algo SHA512 --s2k-count 65536 --force-mdc \
        --quiet --no-greeting --batch --yes \
        --passphrase-file "$ENCRYPTION_KEY_FILE" \
        --output "$BACKUP_DIR/backup_$DATE.tar.gz.gpg" \
        --symmetric

# Upload to S3 (if configured)
if command -v aws &> /dev/null && [ -n "$S3_BUCKET" ]; then
    log "Uploading backup to S3"
    aws s3 cp "$BACKUP_DIR/backup_$DATE.tar.gz.gpg" "s3://$S3_BUCKET/instagram-monitor/"
fi

# Clean up temporary files
rm -f "$BACKUP_DIR/database_$DATE.db" \
      "$BACKUP_DIR/config_$DATE.env" \
      "$BACKUP_DIR/code_$DATE.tar.gz"

# Remove old backups
log "Cleaning up old backups"
find "$BACKUP_DIR" -name "backup_*.tar.gz.gpg" -mtime +$RETENTION_DAYS -delete

# Verify backup integrity
log "Verifying backup integrity"
if gpg --quiet --batch --yes --passphrase-file "$ENCRYPTION_KEY_FILE" \
       --decrypt "$BACKUP_DIR/backup_$DATE.tar.gz.gpg" | tar -tzf - > /dev/null; then
    log "Backup verification successful"
else
    log "ERROR: Backup verification failed"
    exit 1
fi

log "Backup process completed successfully"

# Send notification
echo "Backup completed successfully at $(date)" | \
    mail -s "Instagram Monitor Backup Success" <EMAIL>
```

### Recovery Procedures

#### Database Recovery
```bash
#!/bin/bash
# database-recovery.sh

BACKUP_FILE="$1"
APP_DIR="/var/www/instagram-monitor"
ENCRYPTION_KEY_FILE="/etc/instagram-monitor/backup.key"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file.tar.gz.gpg>"
    exit 1
fi

# Stop application
systemctl stop instagram-monitor

# Backup current database
cp "$APP_DIR/data/instagram_monitor.db" "$APP_DIR/data/instagram_monitor.db.pre-recovery"

# Extract and restore database
gpg --quiet --batch --yes --passphrase-file "$ENCRYPTION_KEY_FILE" \
    --decrypt "$BACKUP_FILE" | tar -xzf - -C /tmp

# Find database file in extracted backup
DB_FILE=$(find /tmp -name "database_*.db" | head -1)

if [ -n "$DB_FILE" ]; then
    cp "$DB_FILE" "$APP_DIR/data/instagram_monitor.db"
    chown www-data:www-data "$APP_DIR/data/instagram_monitor.db"
    chmod 640 "$APP_DIR/data/instagram_monitor.db"
    
    echo "Database restored successfully"
else
    echo "ERROR: Database file not found in backup"
    exit 1
fi

# Verify database integrity
if sqlite3 "$APP_DIR/data/instagram_monitor.db" "PRAGMA integrity_check;" | grep -q "ok"; then
    echo "Database integrity check passed"
else
    echo "ERROR: Database integrity check failed"
    exit 1
fi

# Restart application
systemctl start instagram-monitor

# Clean up
rm -rf /tmp/database_* /tmp/config_* /tmp/code_*

echo "Recovery completed successfully"
```

#### Point-in-Time Recovery
```bash
#!/bin/bash
# point-in-time-recovery.sh

TARGET_DATE="$1"  # Format: YYYY-MM-DD
BACKUP_DIR="/var/backups/instagram-monitor"

if [ -z "$TARGET_DATE" ]; then
    echo "Usage: $0 <YYYY-MM-DD>"
    echo "Available backups:"
    ls -la "$BACKUP_DIR"/backup_*.tar.gz.gpg | awk '{print $9}' | sed 's/.*backup_//' | sed 's/.tar.gz.gpg//'
    exit 1
fi

# Find closest backup to target date
BACKUP_FILE=$(find "$BACKUP_DIR" -name "backup_${TARGET_DATE}*.tar.gz.gpg" | sort | tail -1)

if [ -z "$BACKUP_FILE" ]; then
    echo "No backup found for date $TARGET_DATE"
    exit 1
fi

echo "Restoring from backup: $BACKUP_FILE"
./database-recovery.sh "$BACKUP_FILE"
```

## Performance Management

### Performance Monitoring

#### Database Performance
```sql
-- Database performance queries

-- Check table sizes
SELECT 
    name,
    COUNT(*) as row_count
FROM sqlite_master 
WHERE type='table' 
AND name NOT LIKE 'sqlite_%';

-- Check index usage
EXPLAIN QUERY PLAN 
SELECT * FROM follower_changes 
WHERE profile_id = 1 
ORDER BY timestamp DESC 
LIMIT 100;

-- Analyze database statistics
ANALYZE;

-- Check database integrity
PRAGMA integrity_check;

-- Check database size
SELECT page_count * page_size as size_bytes 
FROM pragma_page_count(), pragma_page_size();
```

#### Application Performance Monitoring
```python
# performance_monitor.py
import time
import psutil
import logging
from functools import wraps

class PerformanceMonitor:
    def __init__(self):
        self.logger = logging.getLogger('performance')
    
    def monitor_function(self, func_name=None):
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss
                
                try:
                    result = func(*args, **kwargs)
                    success = True
                except Exception as e:
                    result = None
                    success = False
                    raise
                finally:
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss
                    
                    self.logger.info({
                        'function': func_name or func.__name__,
                        'duration': end_time - start_time,
                        'memory_delta': end_memory - start_memory,
                        'success': success
                    })
                
                return result
            return wrapper
        return decorator
    
    def get_system_metrics(self):
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'load_average': psutil.getloadavg()
        }
```

### Performance Optimization

#### Database Optimization
```sql
-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_follower_changes_profile_timestamp 
ON follower_changes(profile_id, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_following_changes_profile_timestamp 
ON following_changes(profile_id, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_current_followers_profile 
ON current_followers(profile_id);

CREATE INDEX IF NOT EXISTS idx_current_following_profile 
ON current_following(profile_id);

-- Enable WAL mode for better concurrency
PRAGMA journal_mode=WAL;

-- Optimize SQLite settings
PRAGMA synchronous=NORMAL;
PRAGMA cache_size=10000;
PRAGMA temp_store=MEMORY;
```

#### Application Optimization
```python
# optimization.py
from functools import lru_cache
import redis
from flask_caching import Cache

# Redis cache configuration
cache = Cache(config={'CACHE_TYPE': 'redis'})

class OptimizedDataAccess:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
    
    @lru_cache(maxsize=1000)
    def get_profile_info(self, profile_id):
        """Cached profile information retrieval"""
        # Implementation with caching
        pass
    
    @cache.memoize(timeout=300)  # 5 minute cache
    def get_recent_changes(self, profile_id, limit=100):
        """Cached recent changes retrieval"""
        # Implementation with caching
        pass
    
    def batch_process_changes(self, changes, batch_size=100):
        """Process changes in batches for better performance"""
        for i in range(0, len(changes), batch_size):
            batch = changes[i:i + batch_size]
            self.process_change_batch(batch)
```

## Maintenance Procedures

### Regular Maintenance Tasks

#### Weekly Maintenance Script
```bash
#!/bin/bash
# weekly-maintenance.sh

APP_DIR="/var/www/instagram-monitor"
LOG_FILE="/var/log/instagram-monitor/maintenance.log"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "Starting weekly maintenance"

# Update system packages
log "Updating system packages"
apt update && apt upgrade -y

# Clean up old logs
log "Cleaning up old logs"
find "$APP_DIR/logs" -name "*.log.*" -mtime +30 -delete

# Optimize database
log "Optimizing database"
sudo -u www-data "$APP_DIR/venv/bin/python" -c "
import sqlite3
conn = sqlite3.connect('$APP_DIR/data/instagram_monitor.db')
conn.execute('VACUUM;')
conn.execute('ANALYZE;')
conn.close()
print('Database optimization complete')
"

# Check for application updates
log "Checking for application updates"
cd "$APP_DIR"
if git fetch && [ $(git rev-list HEAD...origin/main --count) != 0 ]; then
    log "Updates available - manual review required"
    git log HEAD..origin/main --oneline | mail -s "Instagram Monitor Updates Available" <EMAIL>
fi

# Restart services for fresh start
log "Restarting services"
systemctl restart instagram-monitor
systemctl restart nginx

# Run health checks
log "Running post-maintenance health checks"
sleep 10
if curl -f -s http://localhost/health > /dev/null; then
    log "Health check passed"
else
    log "Health check failed - investigation required"
    systemctl status instagram-monitor | mail -s "Post-Maintenance Health Check Failed" <EMAIL>
fi

log "Weekly maintenance completed"
```

#### Monthly Security Updates
```bash
#!/bin/bash
# monthly-security-update.sh

# Security-focused maintenance
log "Starting monthly security maintenance"

# Update all packages with security fixes
apt update
apt list --upgradable | grep -i security
apt upgrade -y

# Update Python dependencies
cd /var/www/instagram-monitor
sudo -u www-data venv/bin/pip list --outdated
sudo -u www-data venv/bin/pip install --upgrade pip
# Review and update requirements.txt as needed

# Rotate encryption keys (quarterly)
if [ $(date +%m) -eq 1 ] || [ $(date +%m) -eq 4 ] || [ $(date +%m) -eq 7 ] || [ $(date +%m) -eq 10 ]; then
    log "Quarterly key rotation due - manual intervention required"
    echo "Quarterly encryption key rotation is due" | mail -s "Key Rotation Required" <EMAIL>
fi

# Security scan
if command -v lynis &> /dev/null; then
    lynis audit system --quick
fi

log "Monthly security maintenance completed"
```

### Data Retention Management

#### Automated Data Cleanup
```python
# data_retention.py
from datetime import datetime, timedelta
import logging

class DataRetentionManager:
    def __init__(self, db_manager):
        self.db = db_manager
        self.logger = logging.getLogger(__name__)
        self.retention_days = 365  # 1 year retention
    
    def cleanup_old_data(self):
        """Remove data older than retention period"""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        
        # Clean up follower changes
        deleted_follower_changes = self.db.execute(
            "DELETE FROM follower_changes WHERE timestamp < ?",
            (cutoff_date,)
        ).rowcount
        
        # Clean up following changes
        deleted_following_changes = self.db.execute(
            "DELETE FROM following_changes WHERE timestamp < ?",
            (cutoff_date,)
        ).rowcount
        
        # Clean up old log entries
        deleted_logs = self.db.execute(
            "DELETE FROM system_logs WHERE timestamp < ?",
            (cutoff_date,)
        ).rowcount
        
        self.logger.info(f"Data cleanup completed: "
                        f"follower_changes={deleted_follower_changes}, "
                        f"following_changes={deleted_following_changes}, "
                        f"logs={deleted_logs}")
        
        # Vacuum database to reclaim space
        self.db.execute("VACUUM")
        
        return {
            'follower_changes_deleted': deleted_follower_changes,
            'following_changes_deleted': deleted_following_changes,
            'logs_deleted': deleted_logs
        }
    
    def archive_old_data(self, archive_path):
        """Archive old data before deletion"""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        
        # Export old data to CSV
        old_data = self.db.execute("""
            SELECT * FROM follower_changes 
            WHERE timestamp < ?
            ORDER BY timestamp
        """, (cutoff_date,)).fetchall()
        
        if old_data:
            import csv
            with open(f"{archive_path}/archived_data_{datetime.now().strftime('%Y%m%d')}.csv", 'w') as f:
                writer = csv.writer(f)
                writer.writerow(['id', 'profile_id', 'username', 'change_type', 'timestamp'])
                writer.writerows(old_data)
            
            self.logger.info(f"Archived {len(old_data)} records to {archive_path}")
```

## Scaling and Capacity Planning

### Horizontal Scaling

#### Load Balancer Configuration (Nginx)
```nginx
# load-balancer.conf
upstream instagram_monitor_backend {
    least_conn;
    server *********:8000 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8000 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8000 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name instagram-monitor.example.com;
    
    location / {
        proxy_pass http://instagram_monitor_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Health check
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        proxy_pass http://instagram_monitor_backend;
    }
}
```

#### Database Scaling Strategy
```python
# database_scaling.py
import sqlite3
from contextlib import contextmanager

class ScalableDatabase:
    def __init__(self, read_replicas=None):
        self.master_db = "instagram_monitor.db"
        self.read_replicas = read_replicas or []
        self.current_replica = 0
    
    @contextmanager
    def get_read_connection(self):
        """Get connection for read operations (can use replica)"""
        if self.read_replicas:
            db_path = self.read_replicas[self.current_replica % len(self.read_replicas)]
            self.current_replica += 1
        else:
            db_path = self.master_db
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        try:
            yield conn
        finally:
            conn.close()
    
    @contextmanager
    def get_write_connection(self):
        """Get connection for write operations (always master)"""
        conn = sqlite3.connect(self.master_db, timeout=30.0)
        try:
            yield conn
        finally:
            conn.close()
```

### Capacity Planning

#### Resource Usage Monitoring
```python
# capacity_monitor.py
import psutil
import sqlite3
from datetime import datetime, timedelta

class CapacityMonitor:
    def __init__(self):
        self.metrics = []
    
    def collect_metrics(self):
        """Collect current system metrics"""
        metrics = {
            'timestamp': datetime.now(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage_percent': psutil.disk_usage('/').percent,
            'network_io': psutil.net_io_counters(),
            'process_count': len(psutil.pids()),
            'load_average': psutil.getloadavg()[0]  # 1-minute load average
        }
        
        # Database-specific metrics
        db_size = self.get_database_size()
        metrics['database_size_mb'] = db_size / (1024 * 1024)
        
        # Application-specific metrics
        metrics.update(self.get_application_metrics())
        
        self.metrics.append(metrics)
        return metrics
    
    def get_database_size(self):
        """Get current database size"""
        try:
            conn = sqlite3.connect('instagram_monitor.db')
            cursor = conn.execute("SELECT page_count * page_size FROM pragma_page_count(), pragma_page_size()")
            size = cursor.fetchone()[0]
            conn.close()
            return size
        except:
            return 0
    
    def get_application_metrics(self):
        """Get application-specific metrics"""
        # This would integrate with your application to get:
        # - Number of active monitoring jobs
        # - Request rate
        # - Error rate
        # - Queue sizes
        return {
            'active_profiles': 0,  # Implement actual counting
            'pending_jobs': 0,     # Implement actual counting
            'error_rate': 0.0      # Implement actual calculation
        }
    
    def predict_capacity_needs(self, days_ahead=30):
        """Simple linear prediction of capacity needs"""
        if len(self.metrics) < 2:
            return None
        
        # Calculate growth trends
        recent_metrics = self.metrics[-7:]  # Last 7 data points
        
        cpu_trend = self.calculate_trend([m['cpu_percent'] for m in recent_metrics])
        memory_trend = self.calculate_trend([m['memory_percent'] for m in recent_metrics])
        disk_trend = self.calculate_trend([m['database_size_mb'] for m in recent_metrics])
        
        # Project forward
        current_cpu = recent_metrics[-1]['cpu_percent']
        current_memory = recent_metrics[-1]['memory_percent']
        current_disk = recent_metrics[-1]['database_size_mb']
        
        predicted_cpu = current_cpu + (cpu_trend * days_ahead)
        predicted_memory = current_memory + (memory_trend * days_ahead)
        predicted_disk = current_disk + (disk_trend * days_ahead)
        
        return {
            'days_ahead': days_ahead,
            'predicted_cpu_percent': predicted_cpu,
            'predicted_memory_percent': predicted_memory,
            'predicted_disk_mb': predicted_disk,
            'recommendations': self.generate_recommendations(predicted_cpu, predicted_memory, predicted_disk)
        }
    
    def calculate_trend(self, values):
        """Calculate simple linear trend"""
        if len(values) < 2:
            return 0
        
        n = len(values)
        x_sum = sum(range(n))
        y_sum = sum(values)
        xy_sum = sum(i * values[i] for i in range(n))
        x2_sum = sum(i * i for i in range(n))
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        return slope
    
    def generate_recommendations(self, cpu, memory, disk):
        """Generate capacity recommendations"""
        recommendations = []
        
        if cpu > 80:
            recommendations.append("Consider adding CPU cores or scaling horizontally")
        
        if memory > 80:
            recommendations.append("Consider increasing RAM or optimizing memory usage")
        
        if disk > 10000:  # 10GB
            recommendations.append("Consider implementing data archiving or increasing storage")
        
        return recommendations
```

## Incident Management

### Incident Response Procedures

#### Incident Classification
| Severity | Description | Response Time | Examples |
|----------|-------------|---------------|----------|
| P1 - Critical | Complete service outage | 15 minutes | Application down, database corruption |
| P2 - High | Major functionality impacted | 1 hour | Authentication failures, monitoring stopped |
| P3 - Medium | Minor functionality impacted | 4 hours | Slow performance, partial feature failure |
| P4 - Low | Cosmetic or minor issues | 24 hours | UI glitches, non-critical warnings |

#### Incident Response Playbook
```bash
#!/bin/bash
# incident-response.sh

INCIDENT_ID="$1"
SEVERITY="$2"
DESCRIPTION="$3"

if [ -z "$INCIDENT_ID" ] || [ -z "$SEVERITY" ] || [ -z "$DESCRIPTION" ]; then
    echo "Usage: $0 <incident_id> <severity> <description>"
    echo "Severity: P1|P2|P3|P4"
    exit 1
fi

INCIDENT_DIR="/var/log/incidents/$INCIDENT_ID"
mkdir -p "$INCIDENT_DIR"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$INCIDENT_DIR/incident.log"
}

# Initial response
log "INCIDENT $INCIDENT_ID STARTED - Severity: $SEVERITY"
log "Description: $DESCRIPTION"

# Collect initial diagnostics
log "Collecting system diagnostics"
{
    echo "=== System Status ==="
    systemctl status instagram-monitor nginx
    echo
    echo "=== Resource Usage ==="
    free -h
    df -h
    echo
    echo "=== Recent Logs ==="
    tail -50 /var/www/instagram-monitor/logs/application.log
    echo
    echo "=== Network Status ==="
    netstat -tulpn | grep :8000
} > "$INCIDENT_DIR/diagnostics.txt"

# Severity-specific actions
case $SEVERITY in
    "P1")
        log "P1 incident - implementing emergency procedures"
        # Stop traffic to prevent further damage
        systemctl stop nginx
        
        # Create emergency backup
        sqlite3 /var/www/instagram-monitor/data/instagram_monitor.db \
            ".backup $INCIDENT_DIR/emergency_backup.db"
        
        # Notify on-call team immediately
        echo "P1 INCIDENT: $DESCRIPTION" | \
            mail -s "CRITICAL: Instagram Monitor P1 Incident" <EMAIL>
        ;;
    "P2")
        log "P2 incident - implementing high priority procedures"
        # Restart services
        systemctl restart instagram-monitor
        
        # Notify team
        echo "P2 INCIDENT: $DESCRIPTION" | \
            mail -s "HIGH: Instagram Monitor P2 Incident" <EMAIL>
        ;;
    "P3"|"P4")
        log "$SEVERITY incident - standard procedures"
        # Log for investigation
        echo "$SEVERITY INCIDENT: $DESCRIPTION" | \
            mail -s "$SEVERITY: Instagram Monitor Incident" <EMAIL>
        ;;
esac

log "Initial incident response completed"
```

### Post-Incident Review

#### Post-Mortem Template
```markdown
# Post-Incident Review: [INCIDENT_ID]

## Incident Summary
- **Date**: [DATE]
- **Duration**: [START_TIME] - [END_TIME] ([DURATION])
- **Severity**: [P1/P2/P3/P4]
- **Impact**: [DESCRIPTION OF IMPACT]

## Timeline
| Time | Event | Action Taken |
|------|-------|--------------|
| [TIME] | [EVENT] | [ACTION] |

## Root Cause Analysis
### What Happened?
[Detailed description of what went wrong]

### Why Did It Happen?
[Root cause analysis - technical and process factors]

### Contributing Factors
- [Factor 1]
- [Factor 2]

## Resolution
### Immediate Actions Taken
- [Action 1]
- [Action 2]

### Long-term Fix
[Description of permanent solution implemented]

## Lessons Learned
### What Went Well?
- [Positive aspect 1]
- [Positive aspect 2]

### What Could Be Improved?
- [Improvement area 1]
- [Improvement area 2]

## Action Items
| Action | Owner | Due Date | Status |
|--------|-------|----------|--------|
| [ACTION] | [OWNER] | [DATE] | [STATUS] |

## Prevention Measures
- [Measure 1]
- [Measure 2]

## Monitoring Improvements
- [Improvement 1]
- [Improvement 2]
```

## Compliance and Auditing

### Audit Logging

#### Comprehensive Audit System
```python
# audit_system.py
import json
import logging
from datetime import datetime
from functools import wraps
from flask import request, g

class AuditLogger:
    def __init__(self):
        self.logger = logging.getLogger('audit')
        handler = logging.FileHandler('/var/log/instagram-monitor/audit.log')
        formatter = logging.Formatter('%(asctime)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_event(self, event_type, user_id, resource, action, details=None, result='success'):
        """Log an audit event"""
        audit_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'user_id': user_id,
            'ip_address': request.remote_addr if request else None,
            'user_agent': request.headers.get('User-Agent') if request else None,
            'resource': resource,
            'action': action,
            'result': result,
            'details': details or {}
        }
        
        self.logger.info(json.dumps(audit_record))
    
    def audit_decorator(self, resource, action):
        """Decorator for automatic audit logging"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                user_id = getattr(g, 'user_id', 'anonymous')
                
                try:
                    result = func(*args, **kwargs)
                    self.log_event('api_call', user_id, resource, action, 
                                 details={'args': str(args), 'kwargs': str(kwargs)})
                    return result
                except Exception as e:
                    self.log_event('api_call', user_id, resource, action,
                                 details={'error': str(e)}, result='failure')
                    raise
            return wrapper
        return decorator

# Usage example
audit = AuditLogger()

@app.route('/api/profiles', methods=['POST'])
@audit.audit_decorator('profile', 'create')
def create_profile():
    # Implementation
    pass
```

### Compliance Reporting

#### GDPR Compliance Report
```python
# gdpr_compliance.py
from datetime import datetime, timedelta

class GDPRComplianceReporter:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def generate_data_inventory_report(self):
        """Generate report of all personal data stored"""
        report = {
            'report_date': datetime.now().isoformat(),
            'data_categories': []
        }
        
        # Profile data
        profile_count = self.db.execute("SELECT COUNT(*) FROM profiles").fetchone()[0]
        report['data_categories'].append({
            'category': 'Instagram Profiles',
            'description': 'Instagram usernames and profile metadata',
            'record_count': profile_count,
            'retention_period': '1 year',
            'legal_basis': 'Legitimate interest'
        })
        
        # Follower data
        follower_count = self.db.execute("SELECT COUNT(*) FROM current_followers").fetchone()[0]
        report['data_categories'].append({
            'category': 'Follower Data',
            'description': 'Instagram usernames of followers',
            'record_count': follower_count,
            'retention_period': '1 year',
            'legal_basis': 'Legitimate interest'
        })
        
        return report
    
    def generate_retention_compliance_report(self):
        """Check compliance with data retention policies"""
        cutoff_date = datetime.now() - timedelta(days=365)
        
        old_records = self.db.execute("""
            SELECT COUNT(*) FROM follower_changes 
            WHERE timestamp < ?
        """, (cutoff_date,)).fetchone()[0]
        
        return {
            'report_date': datetime.now().isoformat(),
            'retention_policy': '1 year',
            'records_exceeding_retention': old_records,
            'compliance_status': 'compliant' if old_records == 0 else 'non_compliant',
            'action_required': old_records > 0
        }
    
    def handle_data_subject_request(self, request_type, subject_identifier):
        """Handle GDPR data subject requests"""
        if request_type == 'access':
            return self.export_subject_data(subject_identifier)
        elif request_type == 'deletion':
            return self.delete_subject_data(subject_identifier)
        elif request_type == 'portability':
            return self.export_portable_data(subject_identifier)
    
    def export_subject_data(self, username):
        """Export all data for a specific user"""
        data = {
            'username': username,
            'export_date': datetime.now().isoformat(),
            'data': {}
        }
        
        # Get follower changes
        changes = self.db.execute("""
            SELECT timestamp, change_type, profile_id 
            FROM follower_changes 
            WHERE username = ?
        """, (username,)).fetchall()
        
        data['data']['follower_changes'] = [
            {
                'timestamp': change[0],
                'change_type': change[1],
                'profile_id': change[2]
            }
            for change in changes
        ]
        
        return data
```

This comprehensive operations guide provides the foundation for running the Instagram Follower Monitor in production. Regular review and updates of these procedures ensure reliable, secure, and compliant operation of the system.

<function_calls>
<invoke name="taskStatus">
<parameter name="taskFilePath">.kiro/specs/instagram-follower-monitor/tasks.md