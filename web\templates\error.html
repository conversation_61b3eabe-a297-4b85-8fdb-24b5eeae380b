{% extends "base.html" %}

{% block title %}Error - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="text-center py-5">
            <i class="bi bi-exclamation-triangle display-1 text-danger"></i>
            <h2 class="mt-3">Oops! Something went wrong</h2>
            <p class="text-muted mb-4">{{ error or "An unexpected error occurred. Please try again." }}</p>
            
            <div class="d-flex justify-content-center gap-3">
                <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                    <i class="bi bi-house"></i> Go to Dashboard
                </a>
                <button onclick="history.back()" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Go Back
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}