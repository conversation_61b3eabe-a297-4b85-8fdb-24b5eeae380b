{% extends "base.html" %}

{% block title %}Create Backup - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus"></i> Create New Backup
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="backup_type" class="form-label">Backup Type</label>
                            <select class="form-select" id="backup_type" name="backup_type" onchange="toggleIncrementalOptions()">
                                <option value="full">Full Backup</option>
                                <option value="incremental">Incremental Backup</option>
                            </select>
                            <div class="form-text">
                                Full backup includes all data. Incremental backup includes only changes since a specific date.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="backup_name" class="form-label">Backup Name (Optional)</label>
                            <input type="text" class="form-control" id="backup_name" name="backup_name" 
                                   placeholder="Leave empty for auto-generated name">
                            <div class="form-text">
                                If not specified, a name will be generated based on the current timestamp.
                            </div>
                        </div>

                        <div class="mb-3" id="incremental_options" style="display: none;">
                            <label for="since_date" class="form-label">Since Date</label>
                            <input type="datetime-local" class="form-control" id="since_date" name="since_date">
                            <div class="form-text">
                                Include changes since this date and time.
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Backup Information</h6>
                            <ul class="mb-0">
                                <li><strong>Full Backup:</strong> Complete database, configuration, and data export</li>
                                <li><strong>Incremental Backup:</strong> Only changes since specified date</li>
                                <li><strong>Encryption:</strong> All backups are automatically encrypted</li>
                                <li><strong>Storage:</strong> Backups are stored in the <code>backups/</code> directory</li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('backup.backup_dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Backup
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Backup Process Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">What's Included in Backups</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-database"></i> Database</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Profile configurations</li>
                                <li><i class="fas fa-check text-success"></i> Current followers/following lists</li>
                                <li><i class="fas fa-check text-success"></i> Change history</li>
                                <li><i class="fas fa-check text-success"></i> System settings</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cog"></i> Configuration</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Application settings</li>
                                <li><i class="fas fa-check text-success"></i> Monitoring intervals</li>
                                <li><i class="fas fa-check text-success"></i> System preferences</li>
                                <li><i class="fas fa-check text-success"></i> User configurations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleIncrementalOptions() {
    const backupType = document.getElementById('backup_type').value;
    const incrementalOptions = document.getElementById('incremental_options');
    const sinceDateInput = document.getElementById('since_date');
    
    if (backupType === 'incremental') {
        incrementalOptions.style.display = 'block';
        sinceDateInput.required = true;
        
        // Set default to 7 days ago
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        sinceDateInput.value = weekAgo.toISOString().slice(0, 16);
    } else {
        incrementalOptions.style.display = 'none';
        sinceDateInput.required = false;
        sinceDateInput.value = '';
    }
}

// Set default backup name
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
    document.getElementById('backup_name').placeholder = `backup_${timestamp}`;
});
</script>
{% endblock %}