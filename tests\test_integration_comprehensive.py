"""
Comprehensive integration tests for database operations and API endpoints.

This module provides integration tests that test the interaction between
multiple components, database operations, and API endpoints.
"""

import pytest
import tempfile
import os
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from flask import Flask

from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, ChangeRepository, FollowerRepository, SettingsRepository
)
from models.data_models import (
    MonitoringConfig, FollowerChange, ChangeType, ProfileInfo
)
from web.api import api_v1
from web.app import create_app


class TestDatabaseIntegration:
    """Integration tests for database operations."""
    
    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        # Initialize database
        db_manager = DatabaseManager(temp_file.name)
        db_manager.initialize_database()
        
        yield temp_file.name
        
        # Cleanup
        os.unlink(temp_file.name)
    
    @pytest.fixture
    def repositories(self, temp_db):
        """Create repository instances with temporary database."""
        profile_repo = ProfileRepository(temp_db)
        change_repo = ChangeRepository(temp_db)
        follower_repo = FollowerRepository(temp_db)
        settings_repo = SettingsRepository(temp_db)
        
        return profile_repo, change_repo, follower_repo, settings_repo
    
    def test_profile_lifecycle(self, repositories):
        """Test complete profile lifecycle operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            is_private=False,
            enabled=True,
            interval_hours=2
        )
        
        profile_id = profile_repo.create_profile(config)
        assert profile_id is not None
        assert profile_id > 0
        
        # Retrieve profile
        retrieved_profile = profile_repo.get_profile_by_username("testuser")
        assert retrieved_profile is not None
        assert retrieved_profile.profile_username == "testuser"
        assert retrieved_profile.display_name == "Test User"
        assert retrieved_profile.profile_id == profile_id
        
        # Update profile
        retrieved_profile.display_name = "Updated User"
        retrieved_profile.enabled = False
        success = profile_repo.update_profile(retrieved_profile)
        assert success is True
        
        # Verify update
        updated_profile = profile_repo.get_profile_by_username("testuser")
        assert updated_profile.display_name == "Updated User"
        assert updated_profile.enabled is False
        
        # Get all profiles
        all_profiles = profile_repo.get_all_profiles()
        assert len(all_profiles) == 1
        assert all_profiles[0].profile_username == "testuser"
        
        # Get enabled profiles (should be empty since we disabled it)
        enabled_profiles = profile_repo.get_enabled_profiles()
        assert len(enabled_profiles) == 0
        
        # Re-enable and test enabled profiles
        updated_profile.enabled = True
        profile_repo.update_profile(updated_profile)
        enabled_profiles = profile_repo.get_enabled_profiles()
        assert len(enabled_profiles) == 1
        
        # Delete profile
        success = profile_repo.delete_profile("testuser")
        assert success is True
        
        # Verify deletion
        deleted_profile = profile_repo.get_profile_by_username("testuser")
        assert deleted_profile is None
    
    def test_follower_data_operations(self, repositories):
        """Test follower and following data operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create profile first
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Store initial followers and following
        initial_followers = {"user1", "user2", "user3"}
        initial_following = {"follow1", "follow2"}
        
        follower_repo.store_current_followers(profile_id, initial_followers)
        follower_repo.store_current_following(profile_id, initial_following)
        
        # Retrieve and verify
        retrieved_followers = follower_repo.get_current_followers(profile_id)
        retrieved_following = follower_repo.get_current_following(profile_id)
        
        assert retrieved_followers == initial_followers
        assert retrieved_following == initial_following
        
        # Update followers (simulate changes)
        updated_followers = {"user1", "user2", "user4"}  # user3 lost, user4 gained
        updated_following = {"follow1", "follow3"}  # follow2 lost, follow3 gained
        
        follower_repo.store_current_followers(profile_id, updated_followers)
        follower_repo.store_current_following(profile_id, updated_following)
        
        # Verify updates
        new_followers = follower_repo.get_current_followers(profile_id)
        new_following = follower_repo.get_current_following(profile_id)
        
        assert new_followers == updated_followers
        assert new_following == updated_following
        
        # Test with non-existent profile
        empty_followers = follower_repo.get_current_followers(99999)
        empty_following = follower_repo.get_current_following(99999)
        
        assert empty_followers == set()
        assert empty_following == set()
    
    def test_change_tracking_operations(self, repositories):
        """Test change tracking and retrieval operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Create test changes
        now = datetime.now()
        changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="user1",
                change_type=ChangeType.GAINED,
                timestamp=now - timedelta(hours=2),
                profile_id=profile_id
            ),
            FollowerChange(
                profile_username="testuser",
                affected_username="user2",
                change_type=ChangeType.LOST,
                timestamp=now - timedelta(hours=1),
                profile_id=profile_id
            ),
            FollowerChange(
                profile_username="testuser",
                affected_username="follow1",
                change_type=ChangeType.STARTED_FOLLOWING,
                timestamp=now - timedelta(minutes=30),
                profile_id=profile_id
            ),
        ]
        
        # Store changes
        change_repo.store_follower_changes(changes)
        
        # Retrieve all changes
        all_changes = change_repo.get_all_changes()
        assert len(all_changes) >= 3  # At least our 3 changes
        
        # Retrieve changes for specific profile
        profile_changes = change_repo.get_changes_for_profile(profile_id)
        assert len(profile_changes) == 3
        
        # Retrieve recent changes
        recent_changes = change_repo.get_recent_changes(profile_id=profile_id, limit=2)
        assert len(recent_changes) == 2
        # Should be ordered by timestamp descending (most recent first)
        assert recent_changes[0].timestamp > recent_changes[1].timestamp
        
        # Retrieve changes by date range
        start_date = now - timedelta(hours=3)
        end_date = now
        date_range_changes = change_repo.get_changes_by_date_range(
            start_date, end_date, profile_id
        )
        assert len(date_range_changes) == 3
        
        # Test change statistics
        stats = change_repo.get_change_statistics(profile_id=profile_id, days=1)
        assert stats['total_changes'] == 3
        assert stats['followers_gained'] == 1
        assert stats['followers_lost'] == 1
        assert stats['following_started'] == 1
        assert stats['following_stopped'] == 0
    
    def test_settings_operations(self, repositories):
        """Test settings storage and retrieval operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Test individual setting operations
        settings_repo.set_setting('test_key', 'test_value')
        retrieved_value = settings_repo.get_setting('test_key')
        assert retrieved_value == 'test_value'
        
        # Test non-existent setting
        non_existent = settings_repo.get_setting('non_existent_key')
        assert non_existent is None
        
        # Test with default value
        default_value = settings_repo.get_setting('non_existent_key', 'default')
        assert default_value == 'default'
        
        # Test monitoring settings
        monitoring_settings = {
            'monitoring_interval_hours': 2,
            'data_retention_days': 365,
            'min_request_delay': 1.0,
            'max_request_delay': 3.0
        }
        
        success = settings_repo.update_monitoring_settings(monitoring_settings)
        assert success is True
        
        # Retrieve monitoring settings
        retrieved_settings = settings_repo.get_monitoring_settings()
        assert retrieved_settings['monitoring_interval_hours'] == 2
        assert retrieved_settings['data_retention_days'] == 365
        assert retrieved_settings['min_request_delay'] == 1.0
        assert retrieved_settings['max_request_delay'] == 3.0
    
    def test_profile_statistics(self, repositories):
        """Test profile statistics calculation."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Add some follower data
        followers = {"user1", "user2", "user3", "user4", "user5"}
        following = {"follow1", "follow2", "follow3"}
        
        follower_repo.store_current_followers(profile_id, followers)
        follower_repo.store_current_following(profile_id, following)
        
        # Add some changes
        changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="user6",
                change_type=ChangeType.GAINED,
                timestamp=datetime.now() - timedelta(hours=1),
                profile_id=profile_id
            ),
            FollowerChange(
                profile_username="testuser",
                affected_username="user7",
                change_type=ChangeType.LOST,
                timestamp=datetime.now() - timedelta(hours=2),
                profile_id=profile_id
            ),
        ]
        change_repo.store_follower_changes(changes)
        
        # Get profile statistics
        stats = profile_repo.get_profile_stats(profile_id)
        
        assert stats['current_followers'] == 5
        assert stats['current_following'] == 3
        assert stats['total_changes'] >= 2
        assert 'last_change_date' in stats
    
    def test_data_retention_cleanup(self, repositories):
        """Test data retention and cleanup operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Create old changes (older than retention period)
        old_timestamp = datetime.now() - timedelta(days=400)  # Older than 1 year
        recent_timestamp = datetime.now() - timedelta(days=30)  # Recent
        
        old_changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="old_user1",
                change_type=ChangeType.GAINED,
                timestamp=old_timestamp,
                profile_id=profile_id
            ),
            FollowerChange(
                profile_username="testuser",
                affected_username="old_user2",
                change_type=ChangeType.LOST,
                timestamp=old_timestamp,
                profile_id=profile_id
            ),
        ]
        
        recent_changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="recent_user1",
                change_type=ChangeType.GAINED,
                timestamp=recent_timestamp,
                profile_id=profile_id
            ),
        ]
        
        # Store all changes
        change_repo.store_follower_changes(old_changes + recent_changes)
        
        # Verify all changes are stored
        all_changes = change_repo.get_changes_for_profile(profile_id)
        assert len(all_changes) == 3
        
        # Perform cleanup (simulate data retention manager)
        cutoff_date = datetime.now() - timedelta(days=365)
        deleted_count = change_repo.cleanup_old_changes(cutoff_date)
        
        # Verify old changes were removed
        remaining_changes = change_repo.get_changes_for_profile(profile_id)
        assert len(remaining_changes) == 1
        assert remaining_changes[0].affected_username == "recent_user1"
        assert deleted_count == 2
    
    def test_concurrent_operations(self, repositories):
        """Test concurrent database operations."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Create multiple profiles
        profiles = []
        for i in range(5):
            config = MonitoringConfig(
                profile_username=f"user{i}",
                display_name=f"User {i}",
                enabled=True
            )
            profile_id = profile_repo.create_profile(config)
            profiles.append((profile_id, config))
        
        # Add data for each profile concurrently
        for profile_id, config in profiles:
            # Add followers
            followers = {f"follower{j}" for j in range(10)}
            follower_repo.store_current_followers(profile_id, followers)
            
            # Add changes
            changes = [
                FollowerChange(
                    profile_username=config.profile_username,
                    affected_username=f"change_user{j}",
                    change_type=ChangeType.GAINED,
                    timestamp=datetime.now() - timedelta(minutes=j),
                    profile_id=profile_id
                )
                for j in range(5)
            ]
            change_repo.store_follower_changes(changes)
        
        # Verify all data was stored correctly
        all_profiles = profile_repo.get_all_profiles()
        assert len(all_profiles) == 5
        
        for profile_id, config in profiles:
            followers = follower_repo.get_current_followers(profile_id)
            assert len(followers) == 10
            
            changes = change_repo.get_changes_for_profile(profile_id)
            assert len(changes) == 5


class TestAPIIntegration:
    """Integration tests for API endpoints."""
    
    @pytest.fixture
    def app(self):
        """Create Flask app for testing."""
        app = Flask(__name__)
        app.config['TESTING'] = True
        app.config['SECRET_KEY'] = 'test-secret-key'
        
        # Register API blueprint
        app.register_blueprint(api_v1, url_prefix='/api/v1')
        
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def temp_db_for_api(self):
        """Create temporary database for API tests."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        # Initialize database
        db_manager = DatabaseManager(temp_file.name)
        db_manager.initialize_database()
        
        yield temp_file.name
        
        # Cleanup
        os.unlink(temp_file.name)
    
    @pytest.fixture
    def mock_repositories(self, temp_db_for_api):
        """Mock repositories for API tests."""
        with patch('web.api.profile_repo') as mock_profile_repo, \
             patch('web.api.change_repo') as mock_change_repo, \
             patch('web.api.follower_repo') as mock_follower_repo, \
             patch('web.api.settings_repo') as mock_settings_repo:
            
            # Create real repositories with temp database
            real_profile_repo = ProfileRepository(temp_db_for_api)
            real_change_repo = ChangeRepository(temp_db_for_api)
            real_follower_repo = FollowerRepository(temp_db_for_api)
            real_settings_repo = SettingsRepository(temp_db_for_api)
            
            # Configure mocks to use real repositories
            mock_profile_repo.get_all_profiles = real_profile_repo.get_all_profiles
            mock_profile_repo.get_enabled_profiles = real_profile_repo.get_enabled_profiles
            mock_profile_repo.get_profile_by_username = real_profile_repo.get_profile_by_username
            mock_profile_repo.create_profile = real_profile_repo.create_profile
            mock_profile_repo.update_profile = real_profile_repo.update_profile
            mock_profile_repo.delete_profile = real_profile_repo.delete_profile
            mock_profile_repo.get_profile_stats = real_profile_repo.get_profile_stats
            
            mock_change_repo.get_recent_changes = real_change_repo.get_recent_changes
            mock_change_repo.get_changes_by_date_range = real_change_repo.get_changes_by_date_range
            mock_change_repo.get_change_statistics = real_change_repo.get_change_statistics
            mock_change_repo.store_follower_changes = real_change_repo.store_follower_changes
            
            mock_follower_repo.get_current_followers = real_follower_repo.get_current_followers
            mock_follower_repo.get_current_following = real_follower_repo.get_current_following
            mock_follower_repo.store_current_followers = real_follower_repo.store_current_followers
            mock_follower_repo.store_current_following = real_follower_repo.store_current_following
            
            mock_settings_repo.get_monitoring_settings = real_settings_repo.get_monitoring_settings
            mock_settings_repo.update_monitoring_settings = real_settings_repo.update_monitoring_settings
            
            yield (real_profile_repo, real_change_repo, real_follower_repo, real_settings_repo)
    
    def test_get_profiles_endpoint(self, client, mock_repositories):
        """Test GET /api/v1/profiles endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Create test profiles
        for i in range(3):
            config = MonitoringConfig(
                profile_username=f"testuser{i}",
                display_name=f"Test User {i}",
                enabled=i < 2  # First 2 enabled, last one disabled
            )
            profile_repo.create_profile(config)
        
        # Test get all profiles
        response = client.get('/api/v1/profiles')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['profiles']) == 3
        assert 'pagination' in data
        
        # Test get enabled profiles only
        response = client.get('/api/v1/profiles?enabled_only=true')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['profiles']) == 2
        
        # Test pagination
        response = client.get('/api/v1/profiles?per_page=2&page=1')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['profiles']) == 2
        assert data['pagination']['page'] == 1
        assert data['pagination']['has_next'] is True
    
    def test_get_profile_endpoint(self, client, mock_repositories):
        """Test GET /api/v1/profiles/<username> endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Create test profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Add some test data
        followers = {"user1", "user2", "user3"}
        follower_repo.store_current_followers(profile_id, followers)
        
        changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="user4",
                change_type=ChangeType.GAINED,
                timestamp=datetime.now(),
                profile_id=profile_id
            )
        ]
        change_repo.store_follower_changes(changes)
        
        # Test successful retrieval
        response = client.get('/api/v1/profiles/testuser')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['username'] == 'testuser'
        assert data['display_name'] == 'Test User'
        assert data['monitoring_enabled'] is True
        assert 'stats' in data
        assert 'recent_changes' in data
        
        # Test non-existent profile
        response = client.get('/api/v1/profiles/nonexistent')
        assert response.status_code == 404
    
    def test_create_profile_endpoint(self, client, mock_repositories):
        """Test POST /api/v1/profiles endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Mock API key validation
        with patch('web.api.api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.validate_api_key.return_value = True
            
            # Test successful profile creation
            profile_data = {
                'username': 'newuser',
                'display_name': 'New User',
                'enabled': True,
                'interval_hours': 4
            }
            
            response = client.post(
                '/api/v1/profiles',
                data=json.dumps(profile_data),
                content_type='application/json',
                headers={'X-API-Key': 'test-key'}
            )
            
            assert response.status_code == 201
            
            data = json.loads(response.data)
            assert data['username'] == 'newuser'
            assert data['display_name'] == 'New User'
            assert 'id' in data
            
            # Verify profile was created
            created_profile = profile_repo.get_profile_by_username('newuser')
            assert created_profile is not None
            assert created_profile.display_name == 'New User'
    
    def test_update_profile_endpoint(self, client, mock_repositories):
        """Test PUT /api/v1/profiles/<username> endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Create test profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_repo.create_profile(config)
        
        # Mock API key validation
        with patch('web.api.api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.validate_api_key.return_value = True
            
            # Test successful update
            update_data = {
                'display_name': 'Updated User',
                'enabled': False
            }
            
            response = client.put(
                '/api/v1/profiles/testuser',
                data=json.dumps(update_data),
                content_type='application/json',
                headers={'X-API-Key': 'test-key'}
            )
            
            assert response.status_code == 200
            
            # Verify update
            updated_profile = profile_repo.get_profile_by_username('testuser')
            assert updated_profile.display_name == 'Updated User'
            assert updated_profile.enabled is False
    
    def test_delete_profile_endpoint(self, client, mock_repositories):
        """Test DELETE /api/v1/profiles/<username> endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Create test profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_repo.create_profile(config)
        
        # Mock API key validation
        with patch('web.api.api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.validate_api_key.return_value = True
            
            # Test successful deletion
            response = client.delete(
                '/api/v1/profiles/testuser',
                headers={'X-API-Key': 'test-key'}
            )
            
            assert response.status_code == 200
            
            # Verify deletion
            deleted_profile = profile_repo.get_profile_by_username('testuser')
            assert deleted_profile is None
    
    def test_get_changes_endpoint(self, client, mock_repositories):
        """Test GET /api/v1/changes endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Create test profile and changes
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Create test changes
        now = datetime.now()
        changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="user1",
                change_type=ChangeType.GAINED,
                timestamp=now - timedelta(hours=1),
                profile_id=profile_id
            ),
            FollowerChange(
                profile_username="testuser",
                affected_username="user2",
                change_type=ChangeType.LOST,
                timestamp=now - timedelta(hours=2),
                profile_id=profile_id
            ),
        ]
        change_repo.store_follower_changes(changes)
        
        # Test get all changes
        response = client.get('/api/v1/changes')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['changes']) >= 2
        assert 'pagination' in data
        assert 'filters' in data
        
        # Test filter by profile
        response = client.get('/api/v1/changes?profile=testuser')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert all(change['profile_username'] == 'testuser' for change in data['changes'])
        
        # Test filter by change type
        response = client.get('/api/v1/changes?type=gained')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        gained_changes = [c for c in data['changes'] if c['change_type'] == 'gained']
        assert len(gained_changes) >= 1
    
    def test_monitoring_status_endpoint(self, client, mock_repositories):
        """Test GET /api/v1/monitoring/status endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Create test profiles
        for i in range(3):
            config = MonitoringConfig(
                profile_username=f"testuser{i}",
                display_name=f"Test User {i}",
                enabled=i < 2,  # First 2 enabled
                last_scan=datetime.now() - timedelta(hours=i)
            )
            profile_repo.create_profile(config)
        
        response = client.get('/api/v1/monitoring/status')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'system_status' in data
        assert 'profile_status' in data
        
        system_status = data['system_status']
        assert system_status['total_profiles'] == 3
        assert system_status['enabled_profiles'] == 2
        
        profile_status = data['profile_status']
        assert len(profile_status) == 3
        assert 'testuser0' in profile_status
        assert 'testuser1' in profile_status
        assert 'testuser2' in profile_status
    
    def test_dashboard_summary_endpoint(self, client, mock_repositories):
        """Test GET /api/v1/dashboard/summary endpoint."""
        profile_repo, change_repo, follower_repo, settings_repo = mock_repositories
        
        # Create test data
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Add followers
        followers = {"user1", "user2", "user3"}
        following = {"follow1", "follow2"}
        follower_repo.store_current_followers(profile_id, followers)
        follower_repo.store_current_following(profile_id, following)
        
        # Add today's changes
        today_changes = [
            FollowerChange(
                profile_username="testuser",
                affected_username="user4",
                change_type=ChangeType.GAINED,
                timestamp=datetime.now(),
                profile_id=profile_id
            )
        ]
        change_repo.store_follower_changes(today_changes)
        
        response = client.get('/api/v1/dashboard/summary')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'summary' in data
        assert 'recent_changes' in data
        
        summary = data['summary']
        assert summary['total_profiles'] == 1
        assert summary['enabled_profiles'] == 1
        assert summary['changes_today'] >= 1
    
    def test_rate_limiting(self, client, mock_repositories):
        """Test API rate limiting."""
        # Make many requests quickly to trigger rate limiting
        responses = []
        for i in range(150):  # Exceed the typical rate limit
            response = client.get('/api/v1/profiles')
            responses.append(response.status_code)
        
        # Should eventually get rate limited (429 status)
        assert 429 in responses
    
    def test_api_error_handling(self, client, mock_repositories):
        """Test API error handling."""
        # Test invalid JSON
        response = client.post(
            '/api/v1/profiles',
            data='invalid json',
            content_type='application/json',
            headers={'X-API-Key': 'test-key'}
        )
        assert response.status_code in [400, 401]  # Bad request or unauthorized
        
        # Test missing required fields
        with patch('web.api.api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.validate_api_key.return_value = True
            
            response = client.post(
                '/api/v1/profiles',
                data=json.dumps({}),  # Empty data
                content_type='application/json',
                headers={'X-API-Key': 'test-key'}
            )
            assert response.status_code == 400
    
    def test_api_authentication(self, client, mock_repositories):
        """Test API key authentication."""
        # Test missing API key
        response = client.post('/api/v1/profiles')
        assert response.status_code == 401
        
        # Test invalid API key
        with patch('web.api.api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.validate_api_key.return_value = False
            
            response = client.post(
                '/api/v1/profiles',
                headers={'X-API-Key': 'invalid-key'}
            )
            assert response.status_code == 401
        
        # Test valid API key
        with patch('web.api.api_key_manager') as mock_api_key_manager:
            mock_api_key_manager.validate_api_key.return_value = True
            
            response = client.post(
                '/api/v1/profiles',
                data=json.dumps({'username': 'testuser'}),
                content_type='application/json',
                headers={'X-API-Key': 'valid-key'}
            )
            # Should not be 401 (may be 400 for other validation issues)
            assert response.status_code != 401