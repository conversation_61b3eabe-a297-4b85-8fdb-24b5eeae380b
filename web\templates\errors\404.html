{% extends "base.html" %}

{% block title %}Page Not Found - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        Page Not Found (404)
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-question-circle fa-5x text-warning"></i>
                    </div>
                    
                    <h5 class="card-title">Oops! The page you're looking for doesn't exist.</h5>
                    <p class="card-text text-muted">
                        The page you requested could not be found. It might have been moved, deleted, or you entered the wrong URL.
                    </p>
                    
                    <div class="mt-4">
                        <h6>Here are some helpful links:</h6>
                        <div class="d-grid gap-2 d-md-block">
                            <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>
                                Dashboard
                            </a>
                            <a href="{{ url_for('main.profiles') }}" class="btn btn-outline-primary">
                                <i class="fas fa-users me-2"></i>
                                Profiles
                            </a>
                            <a href="{{ url_for('main.changes') }}" class="btn btn-outline-primary">
                                <i class="fas fa-history me-2"></i>
                                Recent Changes
                            </a>
                            <a href="{{ url_for('main.settings') }}" class="btn btn-outline-primary">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button onclick="history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}