"""
Log Management and Rotation Service

This module provides log rotation, cleanup, and retention management
for the Instagram Follower Monitor application.
"""

import os
import gzip
import shutil
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import threading
import schedule

from services.logging_config import get_logger
from config import Config


logger = get_logger(__name__)


class LogManager:
    """Manages log rotation, compression, and cleanup."""
    
    def __init__(self, config: Config):
        """Initialize log manager."""
        self.config = config
        self.log_dir = Path(__file__).parent.parent / 'logs'
        self.log_dir.mkdir(exist_ok=True)
        
        # Configuration
        self.max_log_size = 10 * 1024 * 1024  # 10MB
        self.max_backup_count = 5
        self.retention_days = 30
        self.compression_enabled = True
        
        # Scheduler for automatic cleanup
        self._scheduler_thread: Optional[threading.Thread] = None
        self._scheduler_active = False
        
        logger.info("Log manager initialized", 
                   log_dir=str(self.log_dir),
                   max_size_mb=self.max_log_size // (1024 * 1024),
                   retention_days=self.retention_days)
    
    def start_scheduler(self):
        """Start the log management scheduler."""
        if self._scheduler_active:
            return
        
        # Schedule daily cleanup at 2 AM
        schedule.every().day.at("02:00").do(self.cleanup_old_logs)
        
        # Schedule log rotation check every hour
        schedule.every().hour.do(self.rotate_logs_if_needed)
        
        self._scheduler_active = True
        self._scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self._scheduler_thread.start()
        
        logger.info("Log management scheduler started")
    
    def stop_scheduler(self):
        """Stop the log management scheduler."""
        self._scheduler_active = False
        schedule.clear()
        
        if self._scheduler_thread:
            self._scheduler_thread.join(timeout=5.0)
        
        logger.info("Log management scheduler stopped")
    
    def rotate_logs_if_needed(self):
        """Check and rotate logs if they exceed size limits."""
        rotated_files = []
        
        for log_file in self.log_dir.glob('*.log'):
            if self._should_rotate_log(log_file):
                try:
                    self._rotate_log_file(log_file)
                    rotated_files.append(log_file.name)
                except Exception as e:
                    logger.error("Failed to rotate log file", 
                               file=str(log_file), error=str(e))
        
        if rotated_files:
            logger.info("Log files rotated", files=rotated_files)
    
    def cleanup_old_logs(self):
        """Clean up old log files based on retention policy."""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        cutoff_timestamp = cutoff_date.timestamp()
        
        cleaned_files = []
        total_size_freed = 0
        
        # Clean up old log files
        for log_file in self.log_dir.rglob('*'):
            if log_file.is_file() and log_file.stat().st_mtime < cutoff_timestamp:
                try:
                    file_size = log_file.stat().st_size
                    log_file.unlink()
                    cleaned_files.append(log_file.name)
                    total_size_freed += file_size
                except Exception as e:
                    logger.error("Failed to delete old log file", 
                               file=str(log_file), error=str(e))
        
        # Clean up empty directories
        self._cleanup_empty_directories()
        
        if cleaned_files:
            logger.info("Old log files cleaned up", 
                       files_count=len(cleaned_files),
                       size_freed_mb=round(total_size_freed / (1024 * 1024), 2),
                       retention_days=self.retention_days)
    
    def compress_old_logs(self):
        """Compress old log files to save space."""
        compressed_files = []
        total_size_saved = 0
        
        # Find log files older than 1 day that aren't compressed
        cutoff_date = datetime.now() - timedelta(days=1)
        cutoff_timestamp = cutoff_date.timestamp()
        
        for log_file in self.log_dir.rglob('*.log.*'):
            if (log_file.suffix != '.gz' and 
                log_file.is_file() and 
                log_file.stat().st_mtime < cutoff_timestamp):
                
                try:
                    original_size = log_file.stat().st_size
                    compressed_path = self._compress_file(log_file)
                    compressed_size = compressed_path.stat().st_size
                    
                    compressed_files.append(log_file.name)
                    total_size_saved += (original_size - compressed_size)
                    
                except Exception as e:
                    logger.error("Failed to compress log file", 
                               file=str(log_file), error=str(e))
        
        if compressed_files:
            logger.info("Log files compressed", 
                       files_count=len(compressed_files),
                       size_saved_mb=round(total_size_saved / (1024 * 1024), 2))
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """Get statistics about log files."""
        stats = {
            'total_files': 0,
            'total_size_mb': 0,
            'by_type': {},
            'oldest_file': None,
            'newest_file': None,
            'compressed_files': 0,
            'uncompressed_files': 0
        }
        
        oldest_time = float('inf')
        newest_time = 0
        
        for log_file in self.log_dir.rglob('*'):
            if log_file.is_file():
                stats['total_files'] += 1
                file_size = log_file.stat().st_size
                stats['total_size_mb'] += file_size
                
                # Track by file type
                suffix = log_file.suffix
                if suffix not in stats['by_type']:
                    stats['by_type'][suffix] = {'count': 0, 'size_mb': 0}
                stats['by_type'][suffix]['count'] += 1
                stats['by_type'][suffix]['size_mb'] += file_size
                
                # Track compression
                if suffix == '.gz':
                    stats['compressed_files'] += 1
                else:
                    stats['uncompressed_files'] += 1
                
                # Track oldest/newest
                mtime = log_file.stat().st_mtime
                if mtime < oldest_time:
                    oldest_time = mtime
                    stats['oldest_file'] = {
                        'name': log_file.name,
                        'date': datetime.fromtimestamp(mtime).isoformat()
                    }
                if mtime > newest_time:
                    newest_time = mtime
                    stats['newest_file'] = {
                        'name': log_file.name,
                        'date': datetime.fromtimestamp(mtime).isoformat()
                    }
        
        # Convert bytes to MB
        stats['total_size_mb'] = round(stats['total_size_mb'] / (1024 * 1024), 2)
        for file_type in stats['by_type'].values():
            file_type['size_mb'] = round(file_type['size_mb'] / (1024 * 1024), 2)
        
        return stats
    
    def export_logs(self, start_date: datetime, end_date: datetime, 
                   components: List[str] = None) -> Path:
        """Export logs for a specific date range and components."""
        export_dir = self.log_dir / 'exports'
        export_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        export_file = export_dir / f'logs_export_{timestamp}.txt'
        
        start_timestamp = start_date.timestamp()
        end_timestamp = end_date.timestamp()
        
        exported_lines = 0
        
        with open(export_file, 'w', encoding='utf-8') as outfile:
            outfile.write(f"Log Export: {start_date} to {end_date}\n")
            outfile.write(f"Components: {components or 'All'}\n")
            outfile.write("=" * 80 + "\n\n")
            
            # Process all log files
            for log_file in sorted(self.log_dir.glob('*.log*')):
                if log_file.is_file():
                    try:
                        lines = self._extract_log_lines(
                            log_file, start_timestamp, end_timestamp, components
                        )
                        if lines:
                            outfile.write(f"\n--- {log_file.name} ---\n")
                            outfile.writelines(lines)
                            exported_lines += len(lines)
                    except Exception as e:
                        logger.error("Error processing log file for export", 
                                   file=str(log_file), error=str(e))
        
        logger.info("Log export completed", 
                   export_file=str(export_file),
                   lines_exported=exported_lines,
                   date_range=f"{start_date} to {end_date}")
        
        return export_file
    
    def _should_rotate_log(self, log_file: Path) -> bool:
        """Check if a log file should be rotated."""
        try:
            return log_file.stat().st_size > self.max_log_size
        except OSError:
            return False
    
    def _rotate_log_file(self, log_file: Path):
        """Rotate a single log file."""
        base_name = log_file.stem
        
        # Shift existing backup files
        for i in range(self.max_backup_count - 1, 0, -1):
            old_backup = log_file.parent / f"{base_name}.{i}.log"
            new_backup = log_file.parent / f"{base_name}.{i + 1}.log"
            
            if old_backup.exists():
                if new_backup.exists():
                    new_backup.unlink()
                old_backup.rename(new_backup)
        
        # Move current log to .1
        backup_file = log_file.parent / f"{base_name}.1.log"
        if backup_file.exists():
            backup_file.unlink()
        
        shutil.move(str(log_file), str(backup_file))
        
        # Compress the backup if enabled
        if self.compression_enabled:
            try:
                self._compress_file(backup_file)
            except Exception as e:
                logger.warning("Failed to compress rotated log", 
                             file=str(backup_file), error=str(e))
        
        # Create new empty log file
        log_file.touch()
        
        logger.debug("Log file rotated", 
                    original=str(log_file),
                    backup=str(backup_file))
    
    def _compress_file(self, file_path: Path) -> Path:
        """Compress a file using gzip."""
        compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
        
        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # Remove original file
        file_path.unlink()
        
        return compressed_path
    
    def _cleanup_empty_directories(self):
        """Remove empty directories in the log directory."""
        for dirpath in self.log_dir.rglob('*'):
            if dirpath.is_dir() and not any(dirpath.iterdir()):
                try:
                    dirpath.rmdir()
                except OSError:
                    pass  # Directory not empty or permission error
    
    def _extract_log_lines(self, log_file: Path, start_timestamp: float, 
                          end_timestamp: float, components: List[str] = None) -> List[str]:
        """Extract log lines within date range and for specific components."""
        lines = []
        
        try:
            # Handle compressed files
            if log_file.suffix == '.gz':
                with gzip.open(log_file, 'rt', encoding='utf-8') as f:
                    content = f.readlines()
            else:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.readlines()
            
            for line in content:
                # Simple timestamp extraction (assumes ISO format at start of line)
                if self._line_in_date_range(line, start_timestamp, end_timestamp):
                    if components is None or self._line_matches_components(line, components):
                        lines.append(line)
        
        except Exception as e:
            logger.error("Error reading log file", file=str(log_file), error=str(e))
        
        return lines
    
    def _line_in_date_range(self, line: str, start_timestamp: float, end_timestamp: float) -> bool:
        """Check if log line is within the specified date range."""
        try:
            # Extract timestamp from log line (assumes format: YYYY-MM-DD HH:MM:SS)
            if len(line) >= 19:
                timestamp_str = line[:19]
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S').timestamp()
                return start_timestamp <= timestamp <= end_timestamp
        except (ValueError, IndexError):
            pass
        
        return False
    
    def _line_matches_components(self, line: str, components: List[str]) -> bool:
        """Check if log line matches any of the specified components."""
        return any(component in line for component in components)
    
    def _run_scheduler(self):
        """Run the scheduled tasks."""
        while self._scheduler_active:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.exception("Error in log manager scheduler")
                time.sleep(60)


# Global log manager instance
_log_manager: Optional[LogManager] = None


def get_log_manager(config: Config = None) -> LogManager:
    """Get or create global log manager instance."""
    global _log_manager
    
    if _log_manager is None:
        if config is None:
            config = Config()
        _log_manager = LogManager(config)
    
    return _log_manager