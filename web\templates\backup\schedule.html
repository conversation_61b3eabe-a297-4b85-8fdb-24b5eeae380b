{% extends "base.html" %}

{% block title %}Schedule Backups - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar"></i> Schedule Automated Backups
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="interval_hours" class="form-label">Backup Interval</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="interval_hours" name="interval_hours" 
                                       value="24" min="1" max="168" required>
                                <span class="input-group-text">hours</span>
                            </div>
                            <div class="form-text">
                                How often to create automated backups (1-168 hours).
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Automated Backup Information</h6>
                            <ul class="mb-0">
                                <li><strong>Type:</strong> Full backups are created automatically</li>
                                <li><strong>Retention:</strong> Old backups are cleaned up based on retention policy</li>
                                <li><strong>Encryption:</strong> All automated backups are encrypted</li>
                                <li><strong>Monitoring:</strong> Backup success/failure is logged</li>
                            </ul>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-clock"></i> Recommended Intervals</h6>
                                <div class="list-group">
                                    <button type="button" class="list-group-item list-group-item-action" 
                                            onclick="setInterval(6)">
                                        <strong>Every 6 hours</strong><br>
                                        <small class="text-muted">High frequency, for critical data</small>
                                    </button>
                                    <button type="button" class="list-group-item list-group-item-action" 
                                            onclick="setInterval(12)">
                                        <strong>Every 12 hours</strong><br>
                                        <small class="text-muted">Twice daily, good balance</small>
                                    </button>
                                    <button type="button" class="list-group-item list-group-item-action active" 
                                            onclick="setInterval(24)">
                                        <strong>Every 24 hours</strong><br>
                                        <small class="text-muted">Daily backups, recommended</small>
                                    </button>
                                    <button type="button" class="list-group-item list-group-item-action" 
                                            onclick="setInterval(72)">
                                        <strong>Every 3 days</strong><br>
                                        <small class="text-muted">Less frequent, minimal storage</small>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-chart-line"></i> Storage Considerations</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Interval</th>
                                                <th>Backups/Month</th>
                                                <th>Storage Impact</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>6 hours</td>
                                                <td>~120</td>
                                                <td class="text-danger">High</td>
                                            </tr>
                                            <tr>
                                                <td>12 hours</td>
                                                <td>~60</td>
                                                <td class="text-warning">Medium</td>
                                            </tr>
                                            <tr class="table-active">
                                                <td>24 hours</td>
                                                <td>~30</td>
                                                <td class="text-success">Low</td>
                                            </tr>
                                            <tr>
                                                <td>72 hours</td>
                                                <td>~10</td>
                                                <td class="text-success">Very Low</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="alert alert-light mt-3">
                                    <small>
                                        <i class="fas fa-lightbulb"></i>
                                        <strong>Tip:</strong> Use cleanup policies to manage storage automatically.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Important Notes</h6>
                            <ul class="mb-0">
                                <li>Automated backups run in the background and don't interrupt normal operation</li>
                                <li>Failed backups are logged and can be monitored</li>
                                <li>Manual backups can still be created at any time</li>
                                <li>Changing the interval will reschedule the automated backup job</li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('backup.backup_dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calendar-plus"></i> Schedule Backups
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Schedule Status -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Current Schedule Status</h5>
                </div>
                <div class="card-body">
                    <div id="schedule-status">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading schedule information...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function setInterval(hours) {
    document.getElementById('interval_hours').value = hours;
    
    // Update active state
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.closest('.list-group-item').classList.add('active');
}

// Load current schedule status
document.addEventListener('DOMContentLoaded', function() {
    loadScheduleStatus();
});

function loadScheduleStatus() {
    // This would typically fetch from an API endpoint
    // For now, we'll show a placeholder
    setTimeout(function() {
        document.getElementById('schedule-status').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-info-circle"></i> Current Settings</h6>
                    <ul class="list-unstyled">
                        <li><strong>Status:</strong> <span class="badge bg-success">Active</span></li>
                        <li><strong>Interval:</strong> 24 hours</li>
                        <li><strong>Next Backup:</strong> Tomorrow at 2:00 AM</li>
                        <li><strong>Last Backup:</strong> Today at 2:00 AM</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-chart-bar"></i> Statistics</h6>
                    <ul class="list-unstyled">
                        <li><strong>Total Automated Backups:</strong> 15</li>
                        <li><strong>Success Rate:</strong> 100%</li>
                        <li><strong>Last Failure:</strong> None</li>
                        <li><strong>Average Size:</strong> 2.5 MB</li>
                    </ul>
                </div>
            </div>
        `;
    }, 1000);
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const interval = parseInt(document.getElementById('interval_hours').value);
    
    if (interval < 1 || interval > 168) {
        alert('Backup interval must be between 1 and 168 hours.');
        e.preventDefault();
        return;
    }
    
    if (interval < 6) {
        if (!confirm('Very frequent backups (less than 6 hours) may impact performance and use significant storage. Continue?')) {
            e.preventDefault();
            return;
        }
    }
});
</script>
{% endblock %}