<!-- Chart Templates for Data Visualization -->

<!-- Follower Trend Chart -->
<div class="chart-container mb-4" id="followerTrendContainer" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-graph-up"></i> Follower/Following Trends
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary" onclick="updateTrendChart(7)">7D</button>
                <button type="button" class="btn btn-outline-secondary active" onclick="updateTrendChart(30)">30D</button>
                <button type="button" class="btn btn-outline-secondary" onclick="updateTrendChart(90)">90D</button>
                <button type="button" class="btn btn-outline-secondary" onclick="exportChart('followerTrend', 'follower_trend.png')">
                    <i class="bi bi-download"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="chart-wrapper" style="position: relative; height: 400px;">
                <canvas id="followerTrendChart" data-username="{{ username if username else '' }}"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Change Distribution Chart -->
<div class="chart-container mb-4" id="changeDistributionContainer" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-pie-chart"></i> Change Distribution
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary" onclick="updateChangeDistribution(7)">7D</button>
                <button type="button" class="btn btn-outline-secondary active" onclick="updateChangeDistribution(30)">30D</button>
                <button type="button" class="btn btn-outline-secondary" onclick="updateChangeDistribution(90)">90D</button>
                <button type="button" class="btn btn-outline-secondary" onclick="exportChart('changeDistribution', 'change_distribution.png')">
                    <i class="bi bi-download"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="chart-wrapper" style="position: relative; height: 400px;">
                <canvas id="changeDistributionChart" data-username="{{ username if username else '' }}"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Profile Comparison Chart -->
<div class="chart-container mb-4" id="profileComparisonContainer" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-bar-chart"></i> Profile Comparison
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary" onclick="toggleComparisonView('followers')">Followers</button>
                <button type="button" class="btn btn-outline-secondary" onclick="toggleComparisonView('following')">Following</button>
                <button type="button" class="btn btn-outline-secondary active" onclick="toggleComparisonView('both')">Both</button>
                <button type="button" class="btn btn-outline-secondary" onclick="exportChart('profileComparison', 'profile_comparison.png')">
                    <i class="bi bi-download"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="chart-wrapper" style="position: relative; height: 400px;">
                <canvas id="profileComparisonChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Mini Charts for Dashboard Cards -->
<template id="miniChartTemplate">
    <div class="mini-chart-container" style="height: 60px; margin-top: 10px;">
        <canvas class="mini-chart"></canvas>
    </div>
</template>

<!-- Chart Controls -->
<div class="chart-controls mb-3" id="chartControls" style="display: none;">
    <div class="row">
        <div class="col-md-6">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="showTrendChart" checked onchange="toggleChart('followerTrendContainer')">
                <label class="form-check-label" for="showTrendChart">
                    Show Trend Chart
                </label>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="showDistributionChart" checked onchange="toggleChart('changeDistributionContainer')">
                <label class="form-check-label" for="showDistributionChart">
                    Show Distribution Chart
                </label>
            </div>
        </div>
    </div>
    <div class="row mt-2">
        <div class="col-md-6">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="showComparisonChart" checked onchange="toggleChart('profileComparisonContainer')">
                <label class="form-check-label" for="showComparisonChart">
                    Show Comparison Chart
                </label>
            </div>
        </div>
        <div class="col-md-6">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportAllCharts()">
                <i class="bi bi-download"></i> Export All Charts
            </button>
        </div>
    </div>
</div>

<!-- Chart Loading Indicator -->
<div class="chart-loading text-center py-4" id="chartLoading" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading charts...</span>
    </div>
    <p class="mt-2 text-muted">Loading chart data...</p>
</div>

<style>
.chart-container {
    transition: opacity 0.3s ease;
}

.chart-container.loading {
    opacity: 0.6;
}

.chart-wrapper {
    position: relative;
}

.chart-wrapper canvas {
    max-height: 400px;
}

.mini-chart-container {
    position: relative;
}

.mini-chart {
    width: 100% !important;
    height: 60px !important;
}

.btn-group .btn.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

@media (max-width: 768px) {
    .chart-wrapper {
        height: 300px !important;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .card-header .btn-group {
        flex-wrap: wrap;
    }
}

@media (max-width: 576px) {
    .chart-wrapper {
        height: 250px !important;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .card-header .btn-group {
        margin-top: 0.5rem;
        width: 100%;
        justify-content: space-between;
    }
}
</style>

<script>
// Chart visibility and control functions
function toggleChart(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.style.display = container.style.display === 'none' ? 'block' : 'none';
        
        // Refresh chart if it's being shown
        if (container.style.display === 'block') {
            const canvas = container.querySelector('canvas');
            if (canvas && chartInstances[canvas.id.replace('Chart', '')]) {
                chartInstances[canvas.id.replace('Chart', '')].resize();
            }
        }
    }
}

function updateTrendChart(days) {
    // Update active button
    document.querySelectorAll('#followerTrendContainer .btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Reload chart data
    const canvas = document.getElementById('followerTrendChart');
    if (canvas && canvas.dataset.username) {
        loadFollowerTrendData(canvas.dataset.username, days);
    }
}

function updateChangeDistribution(days) {
    // Update active button
    document.querySelectorAll('#changeDistributionContainer .btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Reload chart data
    const canvas = document.getElementById('changeDistributionChart');
    if (canvas) {
        loadChangeDistributionData(canvas.dataset.username, days);
    }
}

function toggleComparisonView(view) {
    // Update active button
    document.querySelectorAll('#profileComparisonContainer .btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Update chart visibility
    const chart = chartInstances['profileComparison'];
    if (chart) {
        const datasets = chart.data.datasets;
        if (view === 'followers') {
            datasets[0].hidden = false;
            datasets[1].hidden = true;
        } else if (view === 'following') {
            datasets[0].hidden = true;
            datasets[1].hidden = false;
        } else {
            datasets[0].hidden = false;
            datasets[1].hidden = false;
        }
        chart.update();
    }
}

function exportAllCharts() {
    const charts = ['followerTrend', 'changeDistribution', 'profileComparison'];
    charts.forEach(chartName => {
        if (chartInstances[chartName]) {
            setTimeout(() => {
                exportChart(chartName, `${chartName}_chart.png`);
            }, 100);
        }
    });
}

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Show chart controls if any charts are present
    const chartContainers = document.querySelectorAll('.chart-container');
    if (chartContainers.length > 0) {
        const controls = document.getElementById('chartControls');
        if (controls) {
            controls.style.display = 'block';
        }
        
        // Show chart containers
        chartContainers.forEach(container => {
            container.style.display = 'block';
        });
    }
});
</script>