#!/usr/bin/env python3
"""
Initialize the database with schema and sample data.
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.connection import db_manager
from database.repositories import ProfileRepository, ChangeRepository
from models.data_models import MonitoringConfig, FollowerChange, ChangeType


def init_database():
    """Initialize database with schema and sample data."""
    print("Initializing database...")
    
    # Initialize schema
    db_manager.migrate_schema()
    print("Database schema initialized.")
    
    # Create sample data
    create_sample_data()
    print("Sample data created.")
    
    print("Database initialization complete!")


def create_sample_data():
    """Create sample profiles and changes for testing."""
    profile_repo = ProfileRepository()
    change_repo = ChangeRepository()
    
    # Create sample profiles
    sample_profiles = [
        MonitoringConfig(
            profile_username="sample_user1",
            display_name="Sample User One",
            enabled=True,
            is_private=False,
            last_scan=datetime.now() - timedelta(hours=1)
        ),
        MonitoringConfig(
            profile_username="sample_user2", 
            display_name="Sample User Two",
            enabled=True,
            is_private=True,
            last_scan=datetime.now() - timedelta(hours=3)
        ),
        MonitoringConfig(
            profile_username="inactive_user",
            display_name="Inactive User",
            enabled=False,
            is_private=False,
            last_scan=datetime.now() - timedelta(days=2)
        )
    ]
    
    profile_ids = []
    for config in sample_profiles:
        try:
            profile_id = profile_repo.create_profile(config)
            profile_ids.append(profile_id)
            print(f"Created profile: @{config.profile_username} (ID: {profile_id})")
        except Exception as e:
            print(f"Profile @{config.profile_username} might already exist: {e}")
            # Try to get existing profile
            existing = profile_repo.get_profile_by_username(config.profile_username)
            if existing:
                profile_ids.append(existing.profile_id)
    
    # Create sample changes
    if profile_ids:
        sample_changes = []
        now = datetime.now()
        
        for i, profile_id in enumerate(profile_ids[:2]):  # Only for first 2 profiles
            profile_username = sample_profiles[i].profile_username
            
            # Add some follower changes
            sample_changes.extend([
                FollowerChange(
                    profile_username=profile_username,
                    affected_username=f"new_follower_{i}_1",
                    change_type=ChangeType.GAINED,
                    timestamp=now - timedelta(hours=2),
                    profile_id=profile_id
                ),
                FollowerChange(
                    profile_username=profile_username,
                    affected_username=f"lost_follower_{i}_1",
                    change_type=ChangeType.LOST,
                    timestamp=now - timedelta(hours=4),
                    profile_id=profile_id
                ),
                FollowerChange(
                    profile_username=profile_username,
                    affected_username=f"new_following_{i}_1",
                    change_type=ChangeType.STARTED_FOLLOWING,
                    timestamp=now - timedelta(hours=1),
                    profile_id=profile_id
                ),
                FollowerChange(
                    profile_username=profile_username,
                    affected_username=f"stopped_following_{i}_1",
                    change_type=ChangeType.STOPPED_FOLLOWING,
                    timestamp=now - timedelta(hours=6),
                    profile_id=profile_id
                )
            ])
        
        # Store changes
        change_repo.store_follower_changes(sample_changes)
        print(f"Created {len(sample_changes)} sample changes")
        
        # Add some current followers/following data
        from database.repositories import FollowerRepository
        follower_repo = FollowerRepository()
        
        for i, profile_id in enumerate(profile_ids[:2]):
            # Add current followers
            current_followers = {f"follower_{i}_{j}" for j in range(1, 101)}  # 100 followers
            follower_repo.store_current_followers(profile_id, current_followers)
            
            # Add current following
            current_following = {f"following_{i}_{j}" for j in range(1, 51)}  # 50 following
            follower_repo.store_current_following(profile_id, current_following)
            
            print(f"Added current data for profile ID {profile_id}: {len(current_followers)} followers, {len(current_following)} following")


if __name__ == '__main__':
    init_database()