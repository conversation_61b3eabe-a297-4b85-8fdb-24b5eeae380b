<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Instagram Follower Monitor{% endblock %}</title>
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .card-stat {
            text-align: center;
            padding: 1rem;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #0d6efd;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .change-item {
            border-left: 4px solid #dee2e6;
            padding-left: 1rem;
            margin-bottom: 0.5rem;
        }
        .change-gained {
            border-left-color: #198754;
        }
        .change-lost {
            border-left-color: #dc3545;
        }
        .change-started-following {
            border-left-color: #0d6efd;
        }
        .change-stopped-following {
            border-left-color: #fd7e14;
        }
        .profile-card {
            transition: transform 0.2s;
        }
        .profile-card:hover {
            transform: translateY(-2px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        .status-active {
            background-color: #198754;
        }
        .status-inactive {
            background-color: #dc3545;
        }
        .status-pending {
            background-color: #ffc107;
        }
        .refresh-btn {
            transition: transform 0.3s;
        }
        .refresh-btn.spinning {
            transform: rotate(360deg);
        }
        .timeline {
            position: relative;
            padding-left: 2rem;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 1rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.75rem;
            top: 0.5rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #0d6efd;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">
                <i class="bi bi-instagram"></i>
                Follower Monitor
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}" 
                           href="{{ url_for('main.dashboard') }}">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.profiles' %}active{% endif %}" 
                           href="{{ url_for('main.profiles') }}">
                            <i class="bi bi-people"></i> Profiles
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.changes' %}active{% endif %}" 
                           href="{{ url_for('main.changes') }}">
                            <i class="bi bi-activity"></i> Changes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.search' %}active{% endif %}" 
                           href="{{ url_for('main.search') }}">
                            <i class="bi bi-search"></i> Search
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.charts' %}active{% endif %}" 
                           href="{{ url_for('main.charts') }}">
                            <i class="bi bi-bar-chart"></i> Charts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.settings' %}active{% endif %}" 
                           href="{{ url_for('main.settings') }}">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and request.endpoint.startswith('backup.') %}active{% endif %}" 
                           href="{{ url_for('backup.backup_dashboard') }}">
                            <i class="fas fa-shield-alt"></i> Backup
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="refreshData()">
                            <i class="bi bi-arrow-clockwise refresh-btn" id="refreshIcon"></i> Refresh
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text" id="lastUpdated">
                            <i class="bi bi-clock"></i> <span id="lastUpdatedTime">Loading...</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center text-muted">
            <p>&copy; 2024 Instagram Follower Monitor. Built with Flask and Bootstrap.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/config.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/forms.js') }}"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Global refresh function
        function refreshData() {
            const refreshIcon = document.getElementById('refreshIcon');
            refreshIcon.classList.add('spinning');
            
            // Call page-specific refresh function if it exists
            if (typeof pageRefresh === 'function') {
                pageRefresh().finally(() => {
                    refreshIcon.classList.remove('spinning');
                    updateLastUpdatedTime();
                });
            } else {
                // Default refresh - reload page
                setTimeout(() => {
                    location.reload();
                }, 500);
            }
        }
        
        // Update last updated time
        function updateLastUpdatedTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('lastUpdatedTime').textContent = `Updated at ${timeString}`;
        }
        
        // Format time ago
        function timeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) {
                return 'Just now';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} day${days !== 1 ? 's' : ''} ago`;
            }
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateLastUpdatedTime();
            
            // Update time every minute
            setInterval(updateLastUpdatedTime, 60000);
        });
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>