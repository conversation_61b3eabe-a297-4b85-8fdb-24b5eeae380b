# Environment variables for Docker deployment
# Copy this file to .env and update the values

# Flask configuration
SECRET_KEY=your-secret-key-here-change-this-in-production
FLASK_ENV=production

# Database configuration
DATABASE_URL=sqlite:///data/instagram_monitor.db

# Encryption key for Instagram credentials (generate with: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
ENCRYPTION_KEY=your-encryption-key-here

# Logging configuration
LOG_LEVEL=INFO

# Instagram configuration (optional - can be set via web interface)
INSTAGRAM_USERNAME=
INSTAGRAM_PASSWORD=

# Monitoring configuration
MONITORING_INTERVAL=2
MAX_PROFILES=10

# Security settings
SESSION_TIMEOUT=3600
CSRF_TIME_LIMIT=3600

# Rate limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Backup configuration
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=true