#!/usr/bin/env python3
"""
Command Line Interface for Backup and Recovery Operations

This script provides a command-line interface for managing backups and recovery
operations for the Instagram Follower Monitor application.
"""

import argparse
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from services.backup_manager import BackupManager, create_disaster_recovery_documentation
from config import Config
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_backup(args):
    """Create a new backup."""
    config = Config()
    backup_manager = BackupManager(config)
    
    if args.incremental:
        if not args.since_date:
            print("Error: --since-date is required for incremental backups")
            return 1
        
        since_date = datetime.fromisoformat(args.since_date)
        result = backup_manager.create_incremental_backup(since_date, args.name)
    else:
        result = backup_manager.create_full_backup(args.name)
    
    if result['success']:
        print(f"Backup created successfully: {result['backup_path']}")
        if 'backup_info' in result:
            print(f"Backup type: {result['backup_info']['type']}")
            print(f"Timestamp: {result['backup_info']['timestamp']}")
        return 0
    else:
        print(f"Backup failed: {result['error']}")
        return 1


def restore_backup(args):
    """Restore from a backup."""
    config = Config()
    backup_manager = BackupManager(config)
    
    restore_options = {
        'database': args.database,
        'configuration': args.configuration,
        'validate_integrity': not args.skip_validation
    }
    
    if not any(restore_options.values()):
        restore_options = {'database': True, 'configuration': True, 'validate_integrity': True}
    
    print(f"Restoring from backup: {args.backup_path}")
    print(f"Restore options: {restore_options}")
    
    if not args.force:
        confirm = input("This will overwrite existing data. Continue? (y/N): ")
        if confirm.lower() != 'y':
            print("Restore cancelled.")
            return 0
    
    result = backup_manager.restore_from_backup(args.backup_path, restore_options)
    
    if result['success']:
        print("Restore completed successfully!")
        print(f"Restored components: {result['restored_components']}")
        if result.get('errors'):
            print(f"Warnings: {result['errors']}")
        return 0
    else:
        print(f"Restore failed: {result['error']}")
        return 1


def list_backups(args):
    """List available backups."""
    config = Config()
    backup_manager = BackupManager(config)
    
    backups = backup_manager.list_available_backups()
    
    if not backups:
        print("No backups found.")
        return 0
    
    print(f"Found {len(backups)} backup(s):")
    print()
    
    for backup in backups:
        print(f"Name: {backup.get('backup_name', 'Unknown')}")
        print(f"Type: {backup.get('type', 'Unknown')}")
        print(f"Timestamp: {backup.get('timestamp', backup.get('file_modified', 'Unknown'))}")
        print(f"File: {backup['file_path']}")
        print(f"Size: {backup['file_size']:,} bytes")
        print(f"Status: {backup.get('status', 'OK')}")
        print("-" * 50)
    
    return 0


def validate_database(args):
    """Validate database integrity."""
    config = Config()
    backup_manager = BackupManager(config)
    
    print("Validating database integrity...")
    result = backup_manager.validate_database_integrity()
    
    print(f"Database valid: {result['valid']}")
    print(f"Checks performed: {', '.join(result['checks_performed'])}")
    
    if result.get('errors'):
        print("\nErrors found:")
        for error in result['errors']:
            print(f"  - {error}")
    
    if result.get('warnings'):
        print("\nWarnings:")
        for warning in result['warnings']:
            print(f"  - {warning}")
    
    if result.get('statistics'):
        print("\nDatabase Statistics:")
        for key, value in result['statistics'].items():
            print(f"  {key}: {value}")
    
    return 0 if result['valid'] else 1


def export_profile(args):
    """Export profile data."""
    config = Config()
    backup_manager = BackupManager(config)
    
    result = backup_manager.export_profile_data(args.username, args.output)
    
    if result['success']:
        print(f"Profile data exported successfully: {result['output_path']}")
        print(f"Records exported: {result['records_exported']}")
        return 0
    else:
        print(f"Export failed: {result['error']}")
        return 1


def import_profile(args):
    """Import profile data."""
    config = Config()
    backup_manager = BackupManager(config)
    
    result = backup_manager.import_profile_data(args.file_path, args.overwrite)
    
    if result['success']:
        print(f"Profile data imported successfully: {result['profile_username']}")
        print(f"Records imported: {result['records_imported']}")
        return 0
    else:
        print(f"Import failed: {result['error']}")
        return 1


def cleanup_backups(args):
    """Clean up old backups."""
    config = Config()
    backup_manager = BackupManager(config)
    
    if not args.force:
        confirm = input(f"This will delete backups older than {args.days} days. Continue? (y/N): ")
        if confirm.lower() != 'y':
            print("Cleanup cancelled.")
            return 0
    
    result = backup_manager.cleanup_old_backups(args.days)
    
    if result['success']:
        print(f"Cleanup completed successfully!")
        print(f"Deleted files: {len(result['deleted_files'])}")
        print(f"Space freed: {result['total_size_freed']:,} bytes")
        return 0
    else:
        print(f"Cleanup failed: {result['error']}")
        return 1


def schedule_backup(args):
    """Schedule automated backups."""
    config = Config()
    backup_manager = BackupManager(config)
    
    result = backup_manager.schedule_automated_backup(args.interval)
    
    if result['success']:
        print(f"Automated backup scheduled successfully!")
        print(f"Interval: {args.interval} hours")
        print(f"Job ID: {result['job_id']}")
        return 0
    else:
        print(f"Scheduling failed: {result['error']}")
        return 1


def create_docs(args):
    """Create disaster recovery documentation."""
    doc_path = create_disaster_recovery_documentation()
    print(f"Disaster recovery documentation created: {doc_path}")
    return 0


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Instagram Follower Monitor - Backup and Recovery CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Create backup command
    backup_parser = subparsers.add_parser('backup', help='Create a new backup')
    backup_parser.add_argument('--name', help='Custom backup name')
    backup_parser.add_argument('--incremental', action='store_true', help='Create incremental backup')
    backup_parser.add_argument('--since-date', help='Date for incremental backup (ISO format)')
    backup_parser.set_defaults(func=create_backup)
    
    # Restore backup command
    restore_parser = subparsers.add_parser('restore', help='Restore from backup')
    restore_parser.add_argument('backup_path', help='Path to backup file')
    restore_parser.add_argument('--database', action='store_true', help='Restore database only')
    restore_parser.add_argument('--configuration', action='store_true', help='Restore configuration only')
    restore_parser.add_argument('--skip-validation', action='store_true', help='Skip backup validation')
    restore_parser.add_argument('--force', action='store_true', help='Skip confirmation prompt')
    restore_parser.set_defaults(func=restore_backup)
    
    # List backups command
    list_parser = subparsers.add_parser('list', help='List available backups')
    list_parser.set_defaults(func=list_backups)
    
    # Validate database command
    validate_parser = subparsers.add_parser('validate', help='Validate database integrity')
    validate_parser.set_defaults(func=validate_database)
    
    # Export profile command
    export_parser = subparsers.add_parser('export', help='Export profile data')
    export_parser.add_argument('username', help='Profile username to export')
    export_parser.add_argument('--output', help='Output file path')
    export_parser.set_defaults(func=export_profile)
    
    # Import profile command
    import_parser = subparsers.add_parser('import', help='Import profile data')
    import_parser.add_argument('file_path', help='Path to profile export file')
    import_parser.add_argument('--overwrite', action='store_true', help='Overwrite existing profile')
    import_parser.set_defaults(func=import_profile)
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old backups')
    cleanup_parser.add_argument('--days', type=int, default=30, help='Delete backups older than N days')
    cleanup_parser.add_argument('--force', action='store_true', help='Skip confirmation prompt')
    cleanup_parser.set_defaults(func=cleanup_backups)
    
    # Schedule command
    schedule_parser = subparsers.add_parser('schedule', help='Schedule automated backups')
    schedule_parser.add_argument('--interval', type=int, default=24, help='Backup interval in hours')
    schedule_parser.set_defaults(func=schedule_backup)
    
    # Create docs command
    docs_parser = subparsers.add_parser('docs', help='Create disaster recovery documentation')
    docs_parser.set_defaults(func=create_docs)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        return args.func(args)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())