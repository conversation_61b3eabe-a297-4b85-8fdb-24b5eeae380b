#!/usr/bin/env python3
"""
Demonstration of Comprehensive Error Handling and Logging

This script demonstrates the error handling and logging functionality
implemented for task 14.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.logging_config import setup_logging, get_logger
from services.system_monitor import get_system_monitor, setup_default_alert_handlers
from services.log_manager import get_log_manager
from services.exceptions import (
    ValidationError, AuthenticationError, ExternalServiceError
)
from config import Config


def demonstrate_structured_logging():
    """Demonstrate structured logging capabilities."""
    print("=== Structured Logging Demo ===")
    
    logger = get_logger(__name__)
    
    # Basic logging with extra context
    logger.info("Application started", version="1.0.0", environment="demo")
    logger.debug("Debug information", component="demo", user_id=12345)
    logger.warning("This is a warning", threshold_exceeded=True, value=95)
    
    # Performance logging
    from services.logging_config import log_performance
    with log_performance("database_query"):
        # Simulate some work
        import time
        time.sleep(0.1)
    
    print("✓ Structured logging demonstrated")


def demonstrate_error_handling():
    """Demonstrate custom error classes and handling."""
    print("\n=== Error Handling Demo ===")
    
    logger = get_logger(__name__)
    
    try:
        # Simulate validation error
        raise ValidationError("Invalid username format", field="username", value="invalid@user")
    except ValidationError as e:
        logger.error("Validation failed", 
                    error_code=e.error_code,
                    field=e.details.get('field'),
                    status_code=e.status_code)
    
    try:
        # Simulate authentication error
        raise AuthenticationError("Instagram login failed")
    except AuthenticationError as e:
        logger.error("Authentication failed",
                    error_code=e.error_code,
                    service="Instagram",
                    status_code=e.status_code)
    
    try:
        # Simulate external service error
        raise ExternalServiceError("Instagram", "Connection timeout after 30 seconds")
    except ExternalServiceError as e:
        logger.error("External service error",
                    error_code=e.error_code,
                    service=e.details.get('service'),
                    status_code=e.status_code)
    
    print("✓ Error handling demonstrated")


def demonstrate_system_monitoring():
    """Demonstrate system monitoring capabilities."""
    print("\n=== System Monitoring Demo ===")
    
    # Set up system monitor
    config = Config()
    monitor = get_system_monitor(config)
    setup_default_alert_handlers()
    
    # Record some errors and metrics
    monitor.record_error("demo_component", ValueError("Test error"))
    monitor.record_performance_metric("response_time", 1.5, "seconds", component="web")
    monitor.record_performance_metric("memory_usage", 85.2, "percent", component="system")
    
    # Update component health
    monitor.update_component_health("database", "healthy", {"connections": 5})
    monitor.update_component_health("instagram_api", "warning", {"rate_limit": "approaching"})
    
    # Get system health summary
    health = monitor.get_system_health()
    print(f"System Status: {health['status']}")
    print(f"Total Alerts: {health['alerts']['total']}")
    print(f"Components Monitored: {len(health['components'])}")
    
    print("✓ System monitoring demonstrated")


def demonstrate_log_management():
    """Demonstrate log management capabilities."""
    print("\n=== Log Management Demo ===")
    
    config = Config()
    log_manager = get_log_manager(config)
    
    # Get log statistics
    stats = log_manager.get_log_statistics()
    print(f"Total log files: {stats['total_files']}")
    print(f"Total size: {stats['total_size_mb']} MB")
    
    # Check if logs need rotation
    log_manager.rotate_logs_if_needed()
    
    print("✓ Log management demonstrated")


def demonstrate_flask_integration():
    """Demonstrate Flask error handling integration."""
    print("\n=== Flask Integration Demo ===")
    
    from web.app import create_app
    
    config = Config()
    app = create_app(config)
    
    with app.test_client() as client:
        # Test error handling
        response = client.get('/nonexistent')
        print(f"404 Error Response: {response.status_code}")
        
        # Test API error handling
        response = client.get('/api/nonexistent', 
                            headers={'Accept': 'application/json'})
        print(f"API 404 Response: {response.status_code}")
        if response.is_json:
            error_data = response.get_json()
            print(f"Error Code: {error_data['error']['code']}")
        
        # Test health endpoint
        response = client.get('/health')
        if response.is_json:
            health_data = response.get_json()
            print(f"Health Status: {health_data['status']}")
    
    print("✓ Flask integration demonstrated")


def main():
    """Main demonstration function."""
    print("Instagram Follower Monitor - Error Handling & Logging Demo")
    print("=" * 60)
    
    # Set up logging
    config = Config()
    setup_logging(config)
    
    try:
        demonstrate_structured_logging()
        demonstrate_error_handling()
        demonstrate_system_monitoring()
        demonstrate_log_management()
        demonstrate_flask_integration()
        
        print("\n" + "=" * 60)
        print("✓ All demonstrations completed successfully!")
        print("\nFeatures implemented:")
        print("• Structured logging with different levels")
        print("• Custom error classes with proper HTTP status codes")
        print("• Flask error handling middleware")
        print("• User-friendly error pages")
        print("• System monitoring and alerting")
        print("• Log rotation and retention policies")
        print("• Performance logging and metrics")
        print("• Request/response logging")
        
    except Exception as e:
        logger = get_logger(__name__)
        logger.exception("Demo failed", error=str(e))
        print(f"\n❌ Demo failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()