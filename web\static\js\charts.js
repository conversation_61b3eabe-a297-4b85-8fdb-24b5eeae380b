/**
 * Chart.js functionality for data visualization
 * Handles follower/following trend graphs and statistics charts
 */

// Chart instances storage
const chartInstances = {};

/**
 * Initialize charts when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

/**
 * Initialize all charts on the page
 */
function initializeCharts() {
    // Initialize follower trend chart
    const followerTrendCanvas = document.getElementById('followerTrendChart');
    if (followerTrendCanvas) {
        initializeFollowerTrendChart(followerTrendCanvas);
    }
    
    // Initialize change distribution chart
    const changeDistributionCanvas = document.getElementById('changeDistributionChart');
    if (changeDistributionCanvas) {
        initializeChangeDistributionChart(changeDistributionCanvas);
    }
    
    // Initialize profile comparison chart
    const profileComparisonCanvas = document.getElementById('profileComparisonChart');
    if (profileComparisonCanvas) {
        initializeProfileComparisonChart(profileComparisonCanvas);
    }
}

/**
 * Initialize follower trend chart
 */
function initializeFollowerTrendChart(canvas) {
    const ctx = canvas.getContext('2d');
    
    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Followers',
                data: [],
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.4,
                fill: true,
                pointRadius: 3,
                pointHoverRadius: 6,
                borderWidth: 2
            }, {
                label: 'Following',
                data: [],
                borderColor: '#198754',
                backgroundColor: 'rgba(25, 135, 84, 0.1)',
                tension: 0.4,
                fill: true,
                pointRadius: 3,
                pointHoverRadius: 6,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Follower/Following Trends',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#0d6efd',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true,
                    callbacks: {
                        title: function(context) {
                            return 'Date: ' + context[0].label;
                        },
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'category',
                    title: {
                        display: true,
                        text: 'Date',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        maxTicksLimit: 10,
                        maxRotation: 45
                    }
                },
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: 'Count',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        }
    });
    
    chartInstances['followerTrend'] = chart;
    
    // Load data for the chart
    loadFollowerTrendData(canvas.dataset.username);
}

/**
 * Initialize change distribution chart
 */
function initializeChangeDistributionChart(canvas) {
    const ctx = canvas.getContext('2d');
    
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['New Followers', 'Lost Followers', 'Started Following', 'Stopped Following'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    '#198754',
                    '#dc3545',
                    '#0d6efd',
                    '#fd7e14'
                ],
                borderWidth: 3,
                borderColor: '#fff',
                hoverBorderWidth: 4,
                hoverBorderColor: '#fff',
                hoverBackgroundColor: [
                    '#20c997',
                    '#e55353',
                    '#3d8bfd',
                    '#ff922b'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                title: {
                    display: true,
                    text: 'Change Distribution (Last 30 Days)',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#0d6efd',
                    borderWidth: 1,
                    cornerRadius: 6,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        }
    });
    
    chartInstances['changeDistribution'] = chart;
    
    // Load data for the chart
    loadChangeDistributionData(canvas.dataset.username);
}

/**
 * Initialize profile comparison chart
 */
function initializeProfileComparisonChart(canvas) {
    const ctx = canvas.getContext('2d');
    
    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Followers',
                data: [],
                backgroundColor: 'rgba(13, 110, 253, 0.8)',
                borderColor: '#0d6efd',
                borderWidth: 2,
                hoverBackgroundColor: 'rgba(13, 110, 253, 0.9)',
                hoverBorderColor: '#0d6efd',
                borderRadius: 4,
                borderSkipped: false
            }, {
                label: 'Following',
                data: [],
                backgroundColor: 'rgba(25, 135, 84, 0.8)',
                borderColor: '#198754',
                borderWidth: 2,
                hoverBackgroundColor: 'rgba(25, 135, 84, 0.9)',
                hoverBorderColor: '#198754',
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Profile Comparison',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#0d6efd',
                    borderWidth: 1,
                    cornerRadius: 6,
                    callbacks: {
                        title: function(context) {
                            return 'Profile: @' + context[0].label;
                        },
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Profiles',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        callback: function(value, index) {
                            const label = this.getLabelForValue(value);
                            return '@' + label;
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Count',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        }
    });
    
    chartInstances['profileComparison'] = chart;
    
    // Load data for the chart
    loadProfileComparisonData();
}

/**
 * Load follower trend data
 */
async function loadFollowerTrendData(username, days = 30) {
    try {
        showChartLoading('followerTrendContainer');
        
        const url = username ? 
            `/api/profile/${username}/trend-data?days=${days}` : 
            `/api/dashboard/trend-data?days=${days}`;
            
        const response = await fetch(url);
        if (!response.ok) throw new Error('Failed to load trend data');
        
        const data = await response.json();
        updateFollowerTrendChart(data);
        
    } catch (error) {
        console.error('Error loading follower trend data:', error);
        showChartError('followerTrendContainer', 'Failed to load trend data');
    } finally {
        hideChartLoading('followerTrendContainer');
    }
}

/**
 * Load change distribution data
 */
async function loadChangeDistributionData(username, days = 30) {
    try {
        showChartLoading('changeDistributionContainer');
        
        const url = username ? 
            `/api/profile/${username}/change-distribution?days=${days}` : 
            `/api/dashboard/change-distribution?days=${days}`;
            
        const response = await fetch(url);
        if (!response.ok) throw new Error('Failed to load change distribution data');
        
        const data = await response.json();
        updateChangeDistributionChart(data);
        
    } catch (error) {
        console.error('Error loading change distribution data:', error);
        showChartError('changeDistributionContainer', 'Failed to load change distribution data');
    } finally {
        hideChartLoading('changeDistributionContainer');
    }
}

/**
 * Load profile comparison data
 */
async function loadProfileComparisonData() {
    try {
        showChartLoading('profileComparisonContainer');
        
        const response = await fetch('/api/dashboard/profile-comparison');
        if (!response.ok) throw new Error('Failed to load profile comparison data');
        
        const data = await response.json();
        updateProfileComparisonChart(data);
        
    } catch (error) {
        console.error('Error loading profile comparison data:', error);
        showChartError('profileComparisonContainer', 'Failed to load profile comparison data');
    } finally {
        hideChartLoading('profileComparisonContainer');
    }
}

/**
 * Update follower trend chart with new data
 */
function updateFollowerTrendChart(data) {
    const chart = chartInstances['followerTrend'];
    if (!chart) return;
    
    chart.data.labels = data.dates;
    chart.data.datasets[0].data = data.followers;
    chart.data.datasets[1].data = data.following;
    
    chart.update('active');
}

/**
 * Update change distribution chart with new data
 */
function updateChangeDistributionChart(data) {
    const chart = chartInstances['changeDistribution'];
    if (!chart) return;
    
    chart.data.datasets[0].data = [
        data.gained || 0,
        data.lost || 0,
        data.started_following || 0,
        data.stopped_following || 0
    ];
    
    chart.update('active');
}

/**
 * Update profile comparison chart with new data
 */
function updateProfileComparisonChart(data) {
    const chart = chartInstances['profileComparison'];
    if (!chart) return;
    
    chart.data.labels = data.profiles;
    chart.data.datasets[0].data = data.followers;
    chart.data.datasets[1].data = data.following;
    
    chart.update('active');
}

/**
 * Refresh all charts
 */
function refreshCharts() {
    Object.values(chartInstances).forEach(chart => {
        chart.update('active');
    });
}

/**
 * Destroy all chart instances
 */
function destroyCharts() {
    Object.values(chartInstances).forEach(chart => {
        chart.destroy();
    });
    
    // Clear the instances object
    Object.keys(chartInstances).forEach(key => {
        delete chartInstances[key];
    });
}

/**
 * Export chart as image
 */
function exportChart(chartName, filename) {
    const chart = chartInstances[chartName];
    if (!chart) return;
    
    const url = chart.toBase64Image();
    const link = document.createElement('a');
    link.download = filename || `${chartName}_chart.png`;
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * Create mini chart for dashboard cards
 */
function createMiniChart(canvas, data, type = 'line') {
    const ctx = canvas.getContext('2d');
    
    const chart = new Chart(ctx, {
        type: type,
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            },
            scales: {
                x: {
                    display: false
                },
                y: {
                    display: false
                }
            },
            elements: {
                point: {
                    radius: 0
                },
                line: {
                    borderWidth: 2
                }
            }
        }
    });
    
    return chart;
}

/**
 * Show loading indicator for chart
 */
function showChartLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.classList.add('loading');
        const loadingDiv = container.querySelector('.chart-loading') || createLoadingIndicator();
        if (!container.querySelector('.chart-loading')) {
            container.querySelector('.card-body').appendChild(loadingDiv);
        }
        loadingDiv.style.display = 'block';
    }
}

/**
 * Hide loading indicator for chart
 */
function hideChartLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.classList.remove('loading');
        const loadingDiv = container.querySelector('.chart-loading');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }
}

/**
 * Show error message for chart
 */
function showChartError(containerId, message) {
    const container = document.getElementById(containerId);
    if (container) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-warning chart-error';
        errorDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const cardBody = container.querySelector('.card-body');
        const existingError = cardBody.querySelector('.chart-error');
        if (existingError) {
            existingError.remove();
        }
        
        cardBody.insertBefore(errorDiv, cardBody.firstChild);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }
}

/**
 * Create loading indicator element
 */
function createLoadingIndicator() {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'chart-loading text-center py-4';
    loadingDiv.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading chart data...</p>
    `;
    return loadingDiv;
}

/**
 * Enhanced export chart function with options
 */
function exportChart(chartName, filename, options = {}) {
    const chart = chartInstances[chartName];
    if (!chart) {
        console.warn(`Chart ${chartName} not found`);
        return;
    }
    
    const defaultOptions = {
        backgroundColor: '#ffffff',
        pixelRatio: 2,
        format: 'png'
    };
    
    const exportOptions = { ...defaultOptions, ...options };
    
    try {
        // Set background color
        const originalBackgroundColor = chart.options.plugins?.backgroundColor;
        if (!chart.options.plugins) chart.options.plugins = {};
        chart.options.plugins.backgroundColor = exportOptions.backgroundColor;
        
        chart.update('none');
        
        const url = chart.toBase64Image('image/' + exportOptions.format, exportOptions.pixelRatio);
        
        // Restore original background
        if (originalBackgroundColor !== undefined) {
            chart.options.plugins.backgroundColor = originalBackgroundColor;
        } else {
            delete chart.options.plugins.backgroundColor;
        }
        chart.update('none');
        
        // Download the image
        const link = document.createElement('a');
        link.download = filename || `${chartName}_chart.${exportOptions.format}`;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Show success message
        showToast('Chart exported successfully', 'success');
        
    } catch (error) {
        console.error('Error exporting chart:', error);
        showToast('Failed to export chart', 'error');
    }
}

/**
 * Export chart data as CSV
 */
function exportChartData(chartName, filename) {
    const chart = chartInstances[chartName];
    if (!chart) {
        console.warn(`Chart ${chartName} not found`);
        return;
    }
    
    try {
        let csvContent = '';
        
        if (chartName === 'followerTrend') {
            csvContent = 'Date,Followers,Following\n';
            chart.data.labels.forEach((label, index) => {
                const followers = chart.data.datasets[0].data[index] || 0;
                const following = chart.data.datasets[1].data[index] || 0;
                csvContent += `${label},${followers},${following}\n`;
            });
        } else if (chartName === 'changeDistribution') {
            csvContent = 'Change Type,Count\n';
            chart.data.labels.forEach((label, index) => {
                const count = chart.data.datasets[0].data[index] || 0;
                csvContent += `${label},${count}\n`;
            });
        } else if (chartName === 'profileComparison') {
            csvContent = 'Profile,Followers,Following\n';
            chart.data.labels.forEach((label, index) => {
                const followers = chart.data.datasets[0].data[index] || 0;
                const following = chart.data.datasets[1].data[index] || 0;
                csvContent += `${label},${followers},${following}\n`;
            });
        }
        
        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.download = filename || `${chartName}_data.csv`;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        showToast('Chart data exported successfully', 'success');
        
    } catch (error) {
        console.error('Error exporting chart data:', error);
        showToast('Failed to export chart data', 'error');
    }
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-${type === 'success' ? 'check-circle text-success' : type === 'error' ? 'exclamation-circle text-danger' : 'info-circle text-info'}"></i>
                <strong class="me-auto ms-2">Chart Export</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

/**
 * Resize all charts (useful for responsive design)
 */
function resizeCharts() {
    Object.values(chartInstances).forEach(chart => {
        if (chart && typeof chart.resize === 'function') {
            chart.resize();
        }
    });
}

// Handle window resize for responsive charts
let resizeTimeout;
window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(resizeCharts, 250);
});

// Export functions for global use
window.chartUtils = {
    initializeCharts,
    refreshCharts,
    destroyCharts,
    exportChart,
    exportChartData,
    createMiniChart,
    loadFollowerTrendData,
    loadChangeDistributionData,
    loadProfileComparisonData,
    showChartLoading,
    hideChartLoading,
    showChartError,
    resizeCharts
};