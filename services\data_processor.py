"""
Data processor for storing changes and updating baselines.

This module handles the processing and storage of detected changes,
updating current follower/following baselines, and maintaining data integrity.
"""

import logging
from typing import List, Set, Dict, Any, Optional
from datetime import datetime

from models.data_models import (
    Follower<PERSON><PERSON>e, ProfileInfo, MonitoringConfig, 
    ChangeType, validate_username
)
from database.repositories import (
    FollowerRepository, ChangeRepository, ProfileRepository
)

logger = logging.getLogger(__name__)


class DataProcessor:
    """Processes and stores monitoring data and changes."""
    
    def __init__(self, follower_repo: FollowerRepository, 
                 change_repo: ChangeRepository, profile_repo: ProfileRepository):
        """Initialize data processor with repository dependencies.
        
        Args:
            follower_repo: Repository for follower/following data
            change_repo: Repository for change tracking
            profile_repo: Repository for profile management
        """
        self.follower_repo = follower_repo
        self.change_repo = change_repo
        self.profile_repo = profile_repo
        
        self._processing_stats = {
            'total_processing_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'changes_processed': 0,
            'profiles_updated': 0,
            'last_processing_time': None,
            'errors': []
        }
    
    def process_changes(self, profile: MonitoringConfig, profile_info: ProfileInfo,
                       current_followers: Set[str], current_following: Set[str],
                       changes: List[FollowerChange]) -> Dict[str, Any]:
        """Process detected changes and update stored data.
        
        Args:
            profile: Profile configuration
            profile_info: Current profile information
            current_followers: Current followers set
            current_following: Current following set
            changes: List of detected changes
            
        Returns:
            Dict[str, Any]: Processing results
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Processing {len(changes)} changes for profile {profile.profile_username}")
            
            # Validate inputs
            if not profile.profile_id:
                return {
                    'success': False,
                    'error': 'Profile ID is required for processing',
                    'changes_stored': 0
                }
            
            # Store changes in database
            changes_stored = 0
            if changes:
                try:
                    self.change_repo.store_follower_changes(changes)
                    changes_stored = len(changes)
                    logger.debug(f"Stored {changes_stored} changes for {profile.profile_username}")
                except Exception as e:
                    logger.error(f"Failed to store changes for {profile.profile_username}: {e}")
                    return {
                        'success': False,
                        'error': f'Failed to store changes: {e}',
                        'changes_stored': 0
                    }
            
            # Update current followers/following baselines
            try:
                self.follower_repo.store_current_followers(profile.profile_id, current_followers)
                self.follower_repo.store_current_following(profile.profile_id, current_following)
                logger.debug(f"Updated baselines for {profile.profile_username}: "
                           f"{len(current_followers)} followers, {len(current_following)} following")
            except Exception as e:
                logger.error(f"Failed to update baselines for {profile.profile_username}: {e}")
                return {
                    'success': False,
                    'error': f'Failed to update baselines: {e}',
                    'changes_stored': changes_stored
                }
            
            # Update profile information and last scan time
            try:
                profile.last_scan = start_time
                profile.display_name = profile_info.display_name
                profile.is_private = profile_info.is_private
                
                self.profile_repo.update_profile(profile)
                logger.debug(f"Updated profile metadata for {profile.profile_username}")
            except Exception as e:
                logger.error(f"Failed to update profile metadata for {profile.profile_username}: {e}")
                # This is not critical - continue processing
            
            # Update processing statistics
            self._update_processing_stats(True, len(changes))
            
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Successfully processed data for {profile.profile_username} in {duration:.2f}s: "
                       f"{changes_stored} changes stored")
            
            return {
                'success': True,
                'changes_stored': changes_stored,
                'followers_updated': len(current_followers),
                'following_updated': len(current_following),
                'profile_updated': True,
                'duration': duration,
                'processing_summary': self._create_processing_summary(changes)
            }
            
        except Exception as e:
            logger.error(f"Data processing failed for {profile.profile_username}: {e}")
            self._update_processing_stats(False, 0, str(e))
            
            return {
                'success': False,
                'error': str(e),
                'changes_stored': 0,
                'duration': (datetime.now() - start_time).total_seconds()
            }
    
    def _create_processing_summary(self, changes: List[FollowerChange]) -> Dict[str, Any]:
        """Create a summary of processed changes.
        
        Args:
            changes: List of changes that were processed
            
        Returns:
            Dict[str, Any]: Processing summary
        """
        try:
            if not changes:
                return {
                    'total_changes': 0,
                    'follower_changes': 0,
                    'following_changes': 0,
                    'change_breakdown': {}
                }
            
            # Count changes by type
            change_counts = {}
            follower_changes = 0
            following_changes = 0
            
            for change in changes:
                change_type = change.change_type.value
                change_counts[change_type] = change_counts.get(change_type, 0) + 1
                
                if change.is_follower_change:
                    follower_changes += 1
                elif change.is_following_change:
                    following_changes += 1
            
            return {
                'total_changes': len(changes),
                'follower_changes': follower_changes,
                'following_changes': following_changes,
                'change_breakdown': change_counts
            }
            
        except Exception as e:
            logger.error(f"Error creating processing summary: {e}")
            return {
                'total_changes': len(changes) if changes else 0,
                'error': str(e)
            }
    
    def store_initial_baseline(self, profile: MonitoringConfig, 
                              followers: Set[str], following: Set[str]) -> Dict[str, Any]:
        """Store initial baseline data for a new profile.
        
        Args:
            profile: Profile configuration
            followers: Initial followers set
            following: Initial following set
            
        Returns:
            Dict[str, Any]: Storage results
        """
        try:
            if not profile.profile_id:
                return {
                    'success': False,
                    'error': 'Profile ID is required for storing baseline'
                }
            
            logger.info(f"Storing initial baseline for {profile.profile_username}: "
                       f"{len(followers)} followers, {len(following)} following")
            
            # Store baseline data
            self.follower_repo.store_current_followers(profile.profile_id, followers)
            self.follower_repo.store_current_following(profile.profile_id, following)
            
            # Update profile last scan time
            profile.last_scan = datetime.now()
            self.profile_repo.update_profile(profile)
            
            logger.info(f"Successfully stored initial baseline for {profile.profile_username}")
            
            return {
                'success': True,
                'followers_stored': len(followers),
                'following_stored': len(following),
                'message': 'Initial baseline stored successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to store initial baseline for {profile.profile_username}: {e}")
            return {
                'success': False,
                'error': str(e),
                'followers_stored': 0,
                'following_stored': 0
            }
    
    def update_baseline_only(self, profile_id: int, followers: Set[str], 
                            following: Set[str]) -> Dict[str, Any]:
        """Update only the baseline data without processing changes.
        
        Args:
            profile_id: Profile database ID
            followers: Current followers set
            following: Current following set
            
        Returns:
            Dict[str, Any]: Update results
        """
        try:
            logger.debug(f"Updating baseline for profile ID {profile_id}")
            
            # Update baseline data
            self.follower_repo.store_current_followers(profile_id, followers)
            self.follower_repo.store_current_following(profile_id, following)
            
            return {
                'success': True,
                'followers_updated': len(followers),
                'following_updated': len(following)
            }
            
        except Exception as e:
            logger.error(f"Failed to update baseline for profile ID {profile_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'followers_updated': 0,
                'following_updated': 0
            }
    
    def validate_data_integrity(self, profile_id: int) -> Dict[str, Any]:
        """Validate data integrity for a profile.
        
        Args:
            profile_id: Profile database ID
            
        Returns:
            Dict[str, Any]: Validation results
        """
        try:
            logger.debug(f"Validating data integrity for profile ID {profile_id}")
            
            # Get current stored data
            current_followers = self.follower_repo.get_current_followers(profile_id)
            current_following = self.follower_repo.get_current_following(profile_id)
            
            # Get recent changes
            recent_changes = self.change_repo.get_recent_changes(profile_id, limit=100)
            
            # Basic validation checks
            validation_results = {
                'profile_id': profile_id,
                'current_followers_count': len(current_followers),
                'current_following_count': len(current_following),
                'recent_changes_count': len(recent_changes),
                'validation_passed': True,
                'issues': []
            }
            
            # Check for duplicate usernames in current lists
            if len(current_followers) != len(set(current_followers)):
                validation_results['issues'].append('Duplicate followers detected')
                validation_results['validation_passed'] = False
            
            if len(current_following) != len(set(current_following)):
                validation_results['issues'].append('Duplicate following detected')
                validation_results['validation_passed'] = False
            
            # Check for invalid usernames
            invalid_followers = []
            for username in current_followers:
                try:
                    validate_username(username)
                except ValueError:
                    invalid_followers.append(username)
            
            if invalid_followers:
                validation_results['issues'].append(f'Invalid follower usernames: {invalid_followers[:5]}')
                validation_results['validation_passed'] = False
            
            invalid_following = []
            for username in current_following:
                try:
                    validate_username(username)
                except ValueError:
                    invalid_following.append(username)
            
            if invalid_following:
                validation_results['issues'].append(f'Invalid following usernames: {invalid_following[:5]}')
                validation_results['validation_passed'] = False
            
            # Check for inconsistent change data
            change_usernames = set(change.affected_username for change in recent_changes)
            invalid_change_usernames = []
            for username in change_usernames:
                try:
                    validate_username(username)
                except ValueError:
                    invalid_change_usernames.append(username)
            
            if invalid_change_usernames:
                validation_results['issues'].append(f'Invalid change usernames: {invalid_change_usernames[:5]}')
                validation_results['validation_passed'] = False
            
            if validation_results['validation_passed']:
                logger.debug(f"Data integrity validation passed for profile ID {profile_id}")
            else:
                logger.warning(f"Data integrity issues found for profile ID {profile_id}: {validation_results['issues']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Data integrity validation failed for profile ID {profile_id}: {e}")
            return {
                'profile_id': profile_id,
                'validation_passed': False,
                'error': str(e),
                'issues': [f'Validation error: {e}']
            }
    
    def cleanup_invalid_data(self, profile_id: int) -> Dict[str, Any]:
        """Clean up invalid data for a profile.
        
        Args:
            profile_id: Profile database ID
            
        Returns:
            Dict[str, Any]: Cleanup results
        """
        try:
            logger.info(f"Cleaning up invalid data for profile ID {profile_id}")
            
            # Get current data
            current_followers = self.follower_repo.get_current_followers(profile_id)
            current_following = self.follower_repo.get_current_following(profile_id)
            
            # Clean followers
            clean_followers = set()
            invalid_followers = 0
            for username in current_followers:
                try:
                    clean_username = validate_username(username)
                    clean_followers.add(clean_username)
                except ValueError:
                    invalid_followers += 1
            
            # Clean following
            clean_following = set()
            invalid_following = 0
            for username in current_following:
                try:
                    clean_username = validate_username(username)
                    clean_following.add(clean_username)
                except ValueError:
                    invalid_following += 1
            
            # Update with cleaned data if changes were made
            cleanup_results = {
                'profile_id': profile_id,
                'followers_cleaned': invalid_followers,
                'following_cleaned': invalid_following,
                'followers_remaining': len(clean_followers),
                'following_remaining': len(clean_following)
            }
            
            if invalid_followers > 0 or invalid_following > 0:
                self.follower_repo.store_current_followers(profile_id, clean_followers)
                self.follower_repo.store_current_following(profile_id, clean_following)
                
                logger.info(f"Cleaned up data for profile ID {profile_id}: "
                           f"removed {invalid_followers} invalid followers, "
                           f"{invalid_following} invalid following")
            else:
                logger.debug(f"No cleanup needed for profile ID {profile_id}")
            
            return cleanup_results
            
        except Exception as e:
            logger.error(f"Data cleanup failed for profile ID {profile_id}: {e}")
            return {
                'profile_id': profile_id,
                'error': str(e),
                'followers_cleaned': 0,
                'following_cleaned': 0
            }
    
    def _update_processing_stats(self, success: bool, changes_count: int, 
                                error: Optional[str] = None) -> None:
        """Update processing statistics.
        
        Args:
            success: Whether processing was successful
            changes_count: Number of changes processed
            error: Error message if processing failed
        """
        try:
            self._processing_stats['total_processing_runs'] += 1
            self._processing_stats['last_processing_time'] = datetime.now()
            
            if success:
                self._processing_stats['successful_runs'] += 1
                self._processing_stats['changes_processed'] += changes_count
                self._processing_stats['profiles_updated'] += 1
            else:
                self._processing_stats['failed_runs'] += 1
                if error:
                    # Keep only last 10 errors
                    self._processing_stats['errors'].append({
                        'timestamp': datetime.now().isoformat(),
                        'error': error
                    })
                    if len(self._processing_stats['errors']) > 10:
                        self._processing_stats['errors'] = self._processing_stats['errors'][-10:]
            
        except Exception as e:
            logger.error(f"Error updating processing stats: {e}")
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get current processing statistics.
        
        Returns:
            Dict[str, Any]: Processing statistics
        """
        stats = self._processing_stats.copy()
        
        # Format last processing time
        if stats['last_processing_time']:
            stats['last_processing_time'] = stats['last_processing_time'].isoformat()
        
        # Calculate success rate
        if stats['total_processing_runs'] > 0:
            stats['success_rate'] = stats['successful_runs'] / stats['total_processing_runs']
        else:
            stats['success_rate'] = 0.0
        
        # Calculate average changes per run
        if stats['successful_runs'] > 0:
            stats['average_changes_per_run'] = stats['changes_processed'] / stats['successful_runs']
        else:
            stats['average_changes_per_run'] = 0.0
        
        return stats
    
    def reset_statistics(self) -> None:
        """Reset processing statistics."""
        self._processing_stats = {
            'total_processing_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'changes_processed': 0,
            'profiles_updated': 0,
            'last_processing_time': None,
            'errors': []
        }
        logger.info("Data processor statistics reset")