# Instagram Follower Monitor

A Python application that monitors Instagram profiles for follower and following changes, providing a web-based dashboard for visualization and configuration.

## Features

- Monitor multiple Instagram profiles for follower/following changes
- Secure credential storage with encryption
- Web-based dashboard for data visualization
- Configurable monitoring intervals (default: 2 hours)
- 1-year historical data retention
- Rate limiting and anti-bot measures
- REST API for programmatic access

## Project Structure

```
instagram-follower-monitor/
├── models/          # Data models and structures
├── services/        # Business logic and core services
├── web/            # Flask web application
├── database/       # Database management and repositories
├── app.py          # Main application entry point
├── config.py       # Configuration management
└── requirements.txt # Python dependencies
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Copy `.env.example` to `.env` and configure your settings
4. Run the application:
   ```bash
   python app.py
   ```

## Configuration

The application uses environment variables for configuration. See `.env.example` for available options.

## Development

This project follows a modular architecture with clear separation of concerns:

- **Models**: Data structures and database models
- **Services**: Core business logic and Instagram integration
- **Web**: Flask application and dashboard interface
- **Database**: Data persistence and repository patterns

## Requirements

- Python 3.8+
- Instagram account for monitoring private profiles
- SQLite for data storage

## License

This project is for educational purposes only. Please respect Instagram's Terms of Service and rate limits.