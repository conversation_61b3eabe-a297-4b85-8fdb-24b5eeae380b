#!/usr/bin/env python3
"""
Demo script to showcase enhanced rate limiting and anti-bot measures.

This script demonstrates the new rate limiting features including:
- Request pattern monitoring
- Adaptive delays
- User agent rotation
- Session rotation
- Detection avoidance strategies
"""

import time
import json
from config import Config
from services.instagram_client import InstagramClient, RateLimiter


def demo_rate_limiter():
    """Demonstrate RateLimiter functionality."""
    print("=== Rate Limiter Demo ===")
    
    config = Config()
    rate_limiter = RateLimiter(config)
    
    print(f"Initial state:")
    print(f"  Request count: {rate_limiter.request_count}")
    print(f"  Consecutive errors: {rate_limiter.consecutive_errors}")
    print(f"  User agents available: {len(rate_limiter.user_agents)}")
    
    # Demonstrate user agent rotation
    print(f"\nUser agent rotation:")
    for i in range(3):
        agent = rate_limiter.get_next_user_agent()
        print(f"  Agent {i+1}: {agent[:50]}...")
    
    # Demonstrate request pattern monitoring
    print(f"\nSimulating requests with pattern monitoring:")
    for i in range(5):
        print(f"  Request {i+1}: ", end="")
        start_time = time.time()
        rate_limiter.wait_if_needed(f'demo_request_{i % 2}')  # Alternate request types
        end_time = time.time()
        print(f"waited {end_time - start_time:.2f}s")
    
    # Show request statistics
    stats = rate_limiter.get_request_stats()
    print(f"\nRequest statistics:")
    print(json.dumps(stats, indent=2))
    
    # Demonstrate error handling
    print(f"\nSimulating rate limit errors:")
    initial_errors = rate_limiter.consecutive_errors
    rate_limiter.handle_rate_limit_error()
    print(f"  Errors before: {initial_errors}, after: {rate_limiter.consecutive_errors}")
    print(f"  Backoff until: {rate_limiter.backoff_until}")
    
    # Demonstrate session rotation check
    print(f"\nSession rotation check:")
    should_rotate = rate_limiter.should_rotate_session()
    print(f"  Should rotate session: {should_rotate}")
    
    print("Rate limiter demo completed!\n")


def demo_instagram_client():
    """Demonstrate InstagramClient with enhanced rate limiting."""
    print("=== Instagram Client Demo ===")
    
    config = Config()
    client = InstagramClient(config)
    
    print(f"Initial session info:")
    session_info = client.get_session_info()
    print(json.dumps(session_info, indent=2))
    
    # Demonstrate session rotation check
    print(f"\nChecking if session rotation is needed:")
    rotation_needed = client.rotate_session_if_needed()
    print(f"  Session rotated: {rotation_needed}")
    
    # Show updated session info
    print(f"\nUpdated session info:")
    session_info = client.get_session_info()
    print(json.dumps(session_info, indent=2))
    
    print("Instagram client demo completed!\n")


def demo_adaptive_delays():
    """Demonstrate adaptive delay calculation."""
    print("=== Adaptive Delays Demo ===")
    
    config = Config()
    rate_limiter = RateLimiter(config)
    
    # Simulate many requests to trigger adaptive delays
    print("Simulating high-frequency requests:")
    current_time = time.time()
    
    # Add many recent requests to history
    for i in range(25):
        rate_limiter.request_history.append(current_time - (300 - i * 10))  # Spread over 5 minutes
    
    # Add repetitive request types
    rate_limiter.request_types['profile_info'] = [current_time - (300 - i * 20) for i in range(15)]
    
    # Calculate adaptive delay
    delay = rate_limiter._calculate_adaptive_delay('profile_info')
    print(f"  Adaptive delay for high-frequency profile_info requests: {delay:.2f}s")
    
    # Test different request type
    delay = rate_limiter._calculate_adaptive_delay('followers')
    print(f"  Adaptive delay for new request type 'followers': {delay:.2f}s")
    
    # Show request statistics
    stats = rate_limiter.get_request_stats()
    print(f"\nRequest statistics after simulation:")
    print(f"  Total requests: {stats['total_requests']}")
    print(f"  Recent requests (10min): {stats['recent_requests_10min']}")
    print(f"  Request types: {stats['request_types']}")
    
    print("Adaptive delays demo completed!\n")


def main():
    """Run all demonstrations."""
    print("Instagram Follower Monitor - Rate Limiting Demo")
    print("=" * 50)
    
    try:
        demo_rate_limiter()
        demo_instagram_client()
        demo_adaptive_delays()
        
        print("All demos completed successfully!")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()