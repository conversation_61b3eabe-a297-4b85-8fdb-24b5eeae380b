"""
Unit tests for MonitoringService.

Tests the core monitoring service orchestration and integration
with other components.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from models.data_models import MonitoringConfig, ProfileInfo, FollowerChange, ChangeType
from services.monitoring_service import MonitoringService
from config import Config


class TestMonitoringService:
    """Test cases for MonitoringService class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = Config()
        self.service = MonitoringService(self.config)
        
        # Mock dependencies
        self.service.instagram_client = Mock()
        self.service.profile_repo = Mock()
        self.service.follower_repo = Mock()
        self.service.change_repo = Mock()
        self.service.profile_scanner = Mock()
        self.service.change_detector = Mock()
        self.service.data_processor = Mock()
    
    def test_start_monitoring_cycle_no_profiles(self):
        """Test monitoring cycle when no profiles are due for scanning."""
        # Mock no profiles due for scan
        self.service.profile_repo.get_enabled_profiles.return_value = []
        
        result = self.service.start_monitoring_cycle()
        
        assert result['success'] is True
        assert result['profiles_processed'] == 0
        assert result['changes_detected'] == 0
        assert 'No profiles due for scanning' in result['message']
    
    def test_start_monitoring_cycle_authentication_failure(self):
        """Test monitoring cycle when authentication fails."""
        # Mock profile due for scan
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True,
            last_scan=datetime.now() - timedelta(hours=3)
        )
        self.service.profile_repo.get_enabled_profiles.return_value = [profile]
        
        # Mock authentication failure
        self.service.instagram_client.is_authenticated.return_value = False
        self.service.instagram_client.authenticate.return_value = False
        
        result = self.service.start_monitoring_cycle()
        
        assert result['success'] is False
        assert 'Authentication failed' in result['message']
        assert result['profiles_processed'] == 0
    
    def test_start_monitoring_cycle_successful(self):
        """Test successful monitoring cycle."""
        # Mock profile due for scan
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True,
            last_scan=datetime.now() - timedelta(hours=3)
        )
        self.service.profile_repo.get_enabled_profiles.return_value = [profile]
        
        # Mock authentication success
        self.service.instagram_client.is_authenticated.return_value = True
        
        # Mock profile scanning
        scan_result = {
            'success': True,
            'data': {
                'profile_info': ProfileInfo(username="testuser", follower_count=100, following_count=50),
                'followers': {"user1", "user2"},
                'following': {"follow1"}
            }
        }
        self.service.profile_scanner.scan_profile.return_value = scan_result
        
        # Mock previous data
        self.service.follower_repo.get_current_followers.return_value = {"user1"}
        self.service.follower_repo.get_current_following.return_value = {"follow1"}
        
        # Mock change detection
        changes = [
            FollowerChange("testuser", "user2", ChangeType.GAINED, profile_id=1)
        ]
        self.service.change_detector.detect_changes.return_value = changes
        
        # Mock data processing
        processing_result = {'success': True}
        self.service.data_processor.process_changes.return_value = processing_result
        
        result = self.service.start_monitoring_cycle()
        
        assert result['success'] is True
        assert result['profiles_processed'] == 1
        assert result['successful_profiles'] == 1
        assert result['changes_detected'] == 1
        
        # Verify method calls
        self.service.profile_scanner.scan_profile.assert_called_once_with("testuser")
        self.service.change_detector.detect_changes.assert_called_once()
        self.service.data_processor.process_changes.assert_called_once()
    
    def test_start_monitoring_cycle_scan_failure(self):
        """Test monitoring cycle when profile scanning fails."""
        # Mock profile due for scan
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True,
            last_scan=datetime.now() - timedelta(hours=3)
        )
        self.service.profile_repo.get_enabled_profiles.return_value = [profile]
        
        # Mock authentication success
        self.service.instagram_client.is_authenticated.return_value = True
        
        # Mock profile scanning failure
        scan_result = {
            'success': False,
            'error': 'Profile not found'
        }
        self.service.profile_scanner.scan_profile.return_value = scan_result
        
        result = self.service.start_monitoring_cycle()
        
        assert result['success'] is True  # Overall cycle succeeds even if individual profiles fail
        assert result['profiles_processed'] == 1
        assert result['successful_profiles'] == 0
        assert result['changes_detected'] == 0
        
        # Should have one failed profile result
        assert len(result['profile_results']) == 1
        assert result['profile_results'][0]['success'] is False
    
    def test_start_monitoring_cycle_processing_failure(self):
        """Test monitoring cycle when data processing fails."""
        # Mock profile due for scan
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True,
            last_scan=datetime.now() - timedelta(hours=3)
        )
        self.service.profile_repo.get_enabled_profiles.return_value = [profile]
        
        # Mock authentication success
        self.service.instagram_client.is_authenticated.return_value = True
        
        # Mock successful scanning
        scan_result = {
            'success': True,
            'data': {
                'profile_info': ProfileInfo(username="testuser", follower_count=100, following_count=50),
                'followers': {"user1", "user2"},
                'following': {"follow1"}
            }
        }
        self.service.profile_scanner.scan_profile.return_value = scan_result
        
        # Mock previous data
        self.service.follower_repo.get_current_followers.return_value = {"user1"}
        self.service.follower_repo.get_current_following.return_value = {"follow1"}
        
        # Mock change detection
        changes = [
            FollowerChange("testuser", "user2", ChangeType.GAINED, profile_id=1)
        ]
        self.service.change_detector.detect_changes.return_value = changes
        
        # Mock data processing failure
        processing_result = {
            'success': False,
            'error': 'Database error'
        }
        self.service.data_processor.process_changes.return_value = processing_result
        
        result = self.service.start_monitoring_cycle()
        
        assert result['success'] is True  # Overall cycle succeeds
        assert result['profiles_processed'] == 1
        assert result['successful_profiles'] == 0  # But profile processing failed
        assert result['changes_detected'] == 0
    
    def test_monitor_single_profile_success(self):
        """Test monitoring a single profile successfully."""
        # Mock profile exists
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        self.service.profile_repo.get_profile_by_username.return_value = profile
        
        # Mock authentication success
        self.service.instagram_client.is_authenticated.return_value = True
        
        # Mock successful processing
        with patch.object(self.service, '_process_single_profile') as mock_process:
            mock_process.return_value = {
                'profile': 'testuser',
                'success': True,
                'changes_detected': 2
            }
            
            result = self.service.monitor_single_profile("testuser")
            
            assert result['success'] is True
            assert result['changes_detected'] == 2
            mock_process.assert_called_once_with(profile)
    
    def test_monitor_single_profile_not_found(self):
        """Test monitoring a profile that doesn't exist."""
        # Mock profile not found
        self.service.profile_repo.get_profile_by_username.return_value = None
        
        result = self.service.monitor_single_profile("nonexistent")
        
        assert result['success'] is False
        assert 'not found in monitoring list' in result['message']
        assert result['changes_detected'] == 0
    
    def test_add_profile_to_monitoring_success(self):
        """Test successfully adding a profile to monitoring."""
        # Mock profile doesn't exist
        self.service.profile_repo.get_profile_by_username.return_value = None
        
        # Mock authentication success
        self.service.instagram_client.is_authenticated.return_value = True
        
        # Mock profile info retrieval
        profile_info = ProfileInfo(
            username="newuser",
            display_name="New User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        self.service.instagram_client.get_profile_info.return_value = profile_info
        
        # Mock profile creation
        self.service.profile_repo.create_profile.return_value = 123
        
        result = self.service.add_profile_to_monitoring("newuser")
        
        assert result['success'] is True
        assert result['profile_id'] == 123
        assert 'added to monitoring' in result['message']
        
        # Verify profile creation was called
        self.service.profile_repo.create_profile.assert_called_once()
    
    def test_add_profile_to_monitoring_already_exists(self):
        """Test adding a profile that already exists."""
        # Mock profile already exists
        existing_profile = MonitoringConfig(
            profile_username="existinguser",
            profile_id=1
        )
        self.service.profile_repo.get_profile_by_username.return_value = existing_profile
        
        result = self.service.add_profile_to_monitoring("existinguser")
        
        assert result['success'] is False
        assert 'already exists' in result['message']
    
    def test_add_profile_to_monitoring_profile_not_found(self):
        """Test adding a profile that doesn't exist on Instagram."""
        # Mock profile doesn't exist in database
        self.service.profile_repo.get_profile_by_username.return_value = None
        
        # Mock authentication success
        self.service.instagram_client.is_authenticated.return_value = True
        
        # Mock profile info retrieval failure
        self.service.instagram_client.get_profile_info.return_value = None
        
        result = self.service.add_profile_to_monitoring("nonexistent")
        
        assert result['success'] is False
        assert 'Could not fetch profile information' in result['message']
    
    def test_remove_profile_from_monitoring_success(self):
        """Test successfully removing a profile from monitoring."""
        # Mock profile exists
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1
        )
        self.service.profile_repo.get_profile_by_username.return_value = profile
        
        # Mock successful deletion
        self.service.profile_repo.delete_profile.return_value = True
        
        result = self.service.remove_profile_from_monitoring("testuser")
        
        assert result['success'] is True
        assert 'removed from monitoring' in result['message']
        
        # Verify deletion was called
        self.service.profile_repo.delete_profile.assert_called_once_with("testuser")
    
    def test_remove_profile_from_monitoring_not_found(self):
        """Test removing a profile that doesn't exist."""
        # Mock profile not found
        self.service.profile_repo.get_profile_by_username.return_value = None
        
        result = self.service.remove_profile_from_monitoring("nonexistent")
        
        assert result['success'] is False
        assert 'not found in monitoring list' in result['message']
    
    def test_update_profile_monitoring_success(self):
        """Test successfully updating profile monitoring status."""
        # Mock profile exists
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        self.service.profile_repo.get_profile_by_username.return_value = profile
        
        # Mock successful update
        self.service.profile_repo.update_profile.return_value = True
        
        result = self.service.update_profile_monitoring("testuser", False)
        
        assert result['success'] is True
        assert 'disabled' in result['message']
        
        # Verify profile was updated
        self.service.profile_repo.update_profile.assert_called_once()
        assert profile.enabled is False
    
    def test_get_monitoring_status(self):
        """Test getting monitoring service status."""
        # Mock session info
        self.service.instagram_client.is_authenticated.return_value = True
        self.service.instagram_client.get_current_username.return_value = "testuser"
        self.service.instagram_client.get_session_info.return_value = {
            'request_count': 10,
            'consecutive_errors': 0
        }
        
        status = self.service.get_monitoring_status()
        
        assert status['monitoring_active'] is False
        assert status['authenticated'] is True
        assert status['current_username'] == "testuser"
        assert 'statistics' in status
        assert 'session_info' in status
    
    def test_get_monitored_profiles(self):
        """Test getting list of monitored profiles."""
        # Mock profiles
        profiles = [
            MonitoringConfig(
                profile_username="user1",
                profile_id=1,
                enabled=True,
                display_name="User One",
                last_scan=datetime.now()
            ),
            MonitoringConfig(
                profile_username="user2",
                profile_id=2,
                enabled=False,
                display_name="User Two"
            )
        ]
        self.service.profile_repo.get_all_profiles.return_value = profiles
        
        result = self.service.get_monitored_profiles()
        
        assert len(result) == 2
        assert result[0]['username'] == "user1"
        assert result[0]['enabled'] is True
        assert result[1]['username'] == "user2"
        assert result[1]['enabled'] is False
    
    def test_cleanup_old_data(self):
        """Test data cleanup functionality."""
        with patch('database.repositories.DataRetentionManager') as mock_retention:
            mock_instance = Mock()
            mock_retention.return_value = mock_instance
            mock_instance.cleanup_old_data.return_value = {
                'follower_changes_deleted': 100,
                'following_changes_deleted': 50
            }
            
            result = self.service.cleanup_old_data()
            
            assert result['success'] is True
            assert 'cleanup_stats' in result
            assert result['cleanup_stats']['follower_changes_deleted'] == 100
    
    def test_get_profiles_due_for_scan(self):
        """Test getting profiles due for scanning."""
        # Create profiles with different scan times
        now = datetime.now()
        profiles = [
            MonitoringConfig(
                profile_username="due1",
                profile_id=1,
                enabled=True,
                last_scan=now - timedelta(hours=3),  # Due (> 2 hours)
                interval_hours=2
            ),
            MonitoringConfig(
                profile_username="notdue",
                profile_id=2,
                enabled=True,
                last_scan=now - timedelta(minutes=30),  # Not due (< 2 hours)
                interval_hours=2
            ),
            MonitoringConfig(
                profile_username="never_scanned",
                profile_id=3,
                enabled=True,
                last_scan=None  # Never scanned, should be due
            ),
            MonitoringConfig(
                profile_username="disabled",
                profile_id=4,
                enabled=False,
                last_scan=now - timedelta(hours=5)  # Disabled, not due
            )
        ]
        
        self.service.profile_repo.get_enabled_profiles.return_value = profiles[:3]  # Only enabled ones
        
        due_profiles = self.service._get_profiles_due_for_scan()
        
        # Should return due1 and never_scanned
        assert len(due_profiles) == 2
        due_usernames = {p.profile_username for p in due_profiles}
        assert due_usernames == {"due1", "never_scanned"}
    
    def test_monitoring_cycle_already_active(self):
        """Test starting monitoring cycle when already active."""
        # Set monitoring as active
        self.service._monitoring_active = True
        
        result = self.service.start_monitoring_cycle()
        
        assert result['success'] is False
        assert 'already in progress' in result['message']
        assert result['profiles_processed'] == 0


class TestMonitoringServiceIntegration:
    """Integration tests for MonitoringService with real components."""
    
    def setup_method(self):
        """Set up test fixtures with real components."""
        self.config = Config()
        
        # Create service with mocked external dependencies only
        with patch('services.monitoring_service.InstagramClient'):
            self.service = MonitoringService(self.config)
        
        # Mock only the Instagram client and repositories
        self.service.instagram_client = Mock()
        self.service.profile_repo = Mock()
        self.service.follower_repo = Mock()
        self.service.change_repo = Mock()
    
    def test_full_monitoring_workflow(self):
        """Test complete monitoring workflow with real change detection."""
        # Mock profile due for scan
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True,
            last_scan=datetime.now() - timedelta(hours=3)
        )
        self.service.profile_repo.get_enabled_profiles.return_value = [profile]
        
        # Mock authentication
        self.service.instagram_client.is_authenticated.return_value = True
        
        # Mock Instagram data
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=102,
            following_count=51,
            is_private=False
        )
        
        current_followers = {"user1", "user2", "user3"}
        current_following = {"follow1", "follow2"}
        previous_followers = {"user1", "user2"}  # user3 is new
        previous_following = {"follow1", "follow2", "follow3"}  # follow3 was unfollowed
        
        # Mock profile scanner
        self.service.profile_scanner = Mock()
        self.service.profile_scanner.scan_profile.return_value = {
            'success': True,
            'data': {
                'profile_info': profile_info,
                'followers': current_followers,
                'following': current_following
            }
        }
        
        # Mock repository data
        self.service.follower_repo.get_current_followers.return_value = previous_followers
        self.service.follower_repo.get_current_following.return_value = previous_following
        
        # Mock change detector
        self.service.change_detector = Mock()
        self.service.change_detector.detect_changes.return_value = [
            FollowerChange("testuser", "user3", ChangeType.GAINED, profile_id=1),
            FollowerChange("testuser", "follow3", ChangeType.STOPPED_FOLLOWING, profile_id=1)
        ]
        
        # Mock data processor
        self.service.data_processor = Mock()
        self.service.data_processor.process_changes.return_value = {'success': True}
        
        # Run monitoring cycle
        result = self.service.start_monitoring_cycle()
        
        # Verify results
        assert result['success'] is True
        assert result['profiles_processed'] == 1
        assert result['successful_profiles'] == 1
        assert result['changes_detected'] == 2  # 1 new follower + 1 unfollowed
        
        # Verify change detection was called with correct parameters
        self.service.change_detector.detect_changes.assert_called_once()
        call_args = self.service.change_detector.detect_changes.call_args
        
        assert call_args[1]['profile_username'] == "testuser"
        assert call_args[1]['profile_id'] == 1
        assert call_args[1]['current_followers'] == current_followers
        assert call_args[1]['previous_followers'] == previous_followers
        assert call_args[1]['current_following'] == current_following
        assert call_args[1]['previous_following'] == previous_following