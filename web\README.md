# Web Application Structure

This directory contains the Flask web application for the Instagram Follower Monitor dashboard.

## Directory Structure

```
web/
├── app.py                 # Flask application factory
├── routes.py             # Route handlers and API endpoints
├── __init__.py           # Package initialization
├── static/               # Static assets
│   ├── css/
│   │   └── dashboard.css # Custom styles
│   ├── js/
│   │   ├── config.js     # Application configuration
│   │   ├── dashboard.js  # Dashboard functionality
│   │   ├── forms.js      # Form handling utilities
│   │   ├── search.js     # Search functionality
│   │   └── charts.js     # Chart.js integration
│   ├── images/           # Images and icons
│   └── assets/           # Other static assets
└── templates/            # Jinja2 templates
    ├── base.html         # Base template
    ├── dashboard.html    # Main dashboard
    ├── profiles.html     # Profile management
    ├── changes.html      # Changes view
    ├── search.html       # Search interface
    ├── settings.html     # Settings page
    ├── profile_detail.html # Profile details
    ├── add_profile.html  # Add profile form
    ├── edit_profile.html # Edit profile form
    └── error.html        # Error page
```

## Features

### Security
- **CSRF Protection**: All forms are protected with CSRF tokens
- **Security Headers**: CSP, XSS protection, frame options, etc.
- **Input Validation**: Client-side and server-side validation
- **Session Security**: Secure session cookies and proper lifetime

### User Interface
- **Responsive Design**: Bootstrap 5 with custom CSS
- **Interactive Dashboard**: Real-time data updates
- **Search & Filtering**: Advanced search with live results
- **Data Visualization**: Chart.js integration for trends
- **Form Validation**: Real-time validation with user feedback

### JavaScript Architecture
- **Modular Design**: Separate files for different functionality
- **Configuration Management**: Centralized config in config.js
- **Error Handling**: Comprehensive error handling and user feedback
- **AJAX Support**: Asynchronous form submission and data loading

## Key Components

### Flask Application (app.py)
- Application factory pattern
- CSRF protection setup
- Security headers configuration
- Blueprint registration

### Routes (routes.py)
- Main dashboard routes
- Profile management endpoints
- API endpoints for AJAX requests
- Settings and configuration routes

### Templates
- **base.html**: Common layout with navigation and scripts
- **dashboard.html**: Main overview with statistics and recent changes
- **profiles.html**: Profile management interface
- **changes.html**: Detailed changes view with filtering
- **search.html**: Search interface with advanced options
- **settings.html**: Configuration and system settings

### JavaScript Modules

#### config.js
- Application configuration constants
- API endpoint definitions
- UI settings and feature flags
- Utility functions

#### dashboard.js
- Real-time data updates
- Interactive dashboard features
- Profile toggle functionality
- Auto-refresh capabilities

#### forms.js
- Form validation and submission
- CSRF token management
- Error handling and display
- Real-time field validation

#### search.js
- Live search functionality
- Advanced filtering options
- Result pagination
- Export capabilities

#### charts.js
- Chart.js integration
- Data visualization components
- Interactive charts and graphs
- Export functionality

### CSS Styling (dashboard.css)
- Custom Bootstrap theme
- Responsive design utilities
- Animation and transition effects
- Component-specific styles

## Usage

### Development
```bash
# Install dependencies
pip install -r requirements.txt

# Run development server
python run_dashboard.py
```

### Production
- Use WSGI server (Gunicorn)
- Configure reverse proxy (Nginx)
- Set proper environment variables
- Enable HTTPS for security headers

## Security Considerations

1. **CSRF Protection**: Enabled on all forms
2. **Content Security Policy**: Restricts resource loading
3. **XSS Prevention**: Output escaping and CSP headers
4. **Session Security**: Secure cookies and proper lifetime
5. **Input Validation**: Both client and server-side
6. **Error Handling**: No sensitive information in error messages

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

### Python
- Flask 2.3+
- Flask-WTF 1.1+ (CSRF protection)
- Jinja2 3.1+

### Frontend
- Bootstrap 5.3
- Chart.js (latest)
- Bootstrap Icons
- Font Awesome 6.0

## API Endpoints

### Dashboard
- `GET /api/dashboard/stats` - Dashboard statistics
- `GET /api/dashboard/recent-changes` - Recent changes
- `GET /api/dashboard/change-distribution` - Change distribution data

### Profiles
- `GET /api/profile/{username}/stats` - Profile statistics
- `POST /profiles/{username}/toggle` - Toggle profile monitoring

### Monitoring
- `GET /api/monitoring/status` - System monitoring status

### Search
- `GET /api/search` - Search changes and users
- `GET /api/search/export` - Export search results

## Contributing

1. Follow the existing code structure
2. Add proper error handling
3. Include CSRF tokens in forms
4. Test responsive design
5. Validate all user inputs
6. Document new features