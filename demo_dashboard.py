#!/usr/bin/env python3
"""
Demo script to showcase the dashboard functionality.
"""

import sys
from pathlib import Path
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.app import create_app
from config import Config


def demo_dashboard_api():
    """Demonstrate dashboard API endpoints."""
    config = Config()
    app = create_app(config)
    
    with app.test_client() as client:
        print("=== Instagram Follower Monitor Dashboard Demo ===\n")
        
        # Test dashboard stats
        print("1. Dashboard Statistics:")
        response = client.get('/api/dashboard/stats')
        if response.status_code == 200:
            stats = response.get_json()
            print(f"   Total Profiles: {stats['total_profiles']}")
            print(f"   Active Monitoring: {stats['enabled_profiles']}")
            print(f"   Total Followers: {stats['total_followers']:,}")
            print(f"   Changes Today: {stats['changes_today']}")
        else:
            print(f"   Error: {response.status_code}")
        
        print()
        
        # Test recent changes
        print("2. Recent Changes:")
        response = client.get('/api/dashboard/recent-changes?limit=5')
        if response.status_code == 200:
            changes = response.get_json()
            if changes:
                for i, change in enumerate(changes, 1):
                    print(f"   {i}. {change['display_text']}")
                    print(f"      Time: {change['timestamp']}")
                    print(f"      Type: {change['change_type']}")
                    print()
            else:
                print("   No recent changes found")
        else:
            print(f"   Error: {response.status_code}")
        
        # Test monitoring status
        print("3. Monitoring Status:")
        response = client.get('/api/monitoring/status')
        if response.status_code == 200:
            status = response.get_json()
            print(f"   Total Profiles: {status['total_profiles']}")
            print(f"   Enabled Profiles: {status['enabled_profiles']}")
            print(f"   Profiles Due for Scan: {status['profiles_due_for_scan']}")
            print("\n   Profile Last Scan Times:")
            for username, info in status['last_scan_times'].items():
                scan_time = info['last_scan'] or 'Never'
                due_status = 'Due' if info['is_due'] else 'Not due'
                print(f"     @{username}: {scan_time} ({due_status})")
        else:
            print(f"   Error: {response.status_code}")
        
        print("\n=== Demo Complete ===")
        print("\nTo view the full dashboard interface:")
        print("1. Run: python run_dashboard.py")
        print("2. Open your browser to: http://127.0.0.1:5000")
        print("\nFeatures available in the web interface:")
        print("- Real-time dashboard with profile statistics")
        print("- Profile summary cards with monitoring status")
        print("- Recent changes timeline with timestamps")
        print("- Detailed profile views with charts")
        print("- Changes page with filtering options")
        print("- Auto-refresh capabilities")
        print("- Responsive design for mobile devices")


if __name__ == '__main__':
    demo_dashboard_api()