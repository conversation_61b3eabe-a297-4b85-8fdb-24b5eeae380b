"""Flask routes for the Instagram Follower Monitor dashboard."""

from flask import Blueprint, render_template, jsonify, request, redirect, url_for, flash
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging
import os

from database.repositories import ProfileRepository, ChangeRepository, SettingsRepository
from database.connection import db_manager
from models.data_models import FollowerChange, MonitoringConfig, validate_username
from web.security import (
    validate_form_input, InputValidator, sanitize_output, 
    secure_session_management, SecurityHeaders
)

logger = logging.getLogger(__name__)

# Create blueprints
main_bp = Blueprint('main', __name__)
api_bp = Blueprint('api', __name__)

# Initialize repositories
profile_repo = ProfileRepository()
change_repo = ChangeRepository()
settings_repo = SettingsRepository()


@main_bp.route('/')
def dashboard():
    """Main dashboard overview page."""
    try:
        # Get all profiles for overview
        profiles = profile_repo.get_all_profiles()
        
        # Get recent changes across all profiles
        recent_changes = change_repo.get_recent_changes(limit=20)
        
        # Calculate overall statistics
        total_profiles = len(profiles)
        enabled_profiles = len([p for p in profiles if p.enabled])
        
        # Get profile statistics
        profile_stats = []
        for profile in profiles:
            if profile.profile_id:
                stats = profile_repo.get_profile_stats(profile.profile_id)
                profile_stats.append({
                    'profile': profile,
                    'stats': stats
                })
        
        return render_template('dashboard.html',
                             profiles=profiles,
                             profile_stats=profile_stats,
                             recent_changes=recent_changes,
                             total_profiles=total_profiles,
                             enabled_profiles=enabled_profiles)
    
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        return render_template('error.html', error="Failed to load dashboard"), 500


@main_bp.route('/profile/<username>')
def profile_detail(username: str):
    """Detailed view for a specific profile."""
    try:
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            return render_template('error.html', error="Profile not found"), 404
        
        # Get profile statistics
        stats = profile_repo.get_profile_stats(profile.profile_id) if profile.profile_id else {}
        
        # Get recent changes for this profile
        recent_changes = change_repo.get_recent_changes(profile_id=profile.profile_id, limit=50)
        
        return render_template('profile_detail.html',
                             profile=profile,
                             stats=stats,
                             recent_changes=recent_changes)
    
    except Exception as e:
        logger.error(f"Error loading profile {username}: {e}")
        return render_template('error.html', error="Failed to load profile"), 500


@main_bp.route('/changes')
def changes():
    """Recent changes page with advanced search and filtering options."""
    try:
        # Get filter parameters
        profile_filter = request.args.get('profile')
        days_filter = int(request.args.get('days', 7))
        change_type_filter = request.args.get('type')
        search_query = request.args.get('q', '').strip()
        page = int(request.args.get('page', 1))
        per_page = 20
        sort_by = request.args.get('sort_by', 'timestamp')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Get profile ID if profile specified
        profile_id = None
        if profile_filter:
            profile = profile_repo.get_profile_by_username(profile_filter)
            profile_id = profile.profile_id if profile else None
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_filter)
        
        # Calculate offset for pagination
        offset = (page - 1) * per_page
        
        # Search changes with advanced filtering
        if search_query or any([profile_filter, change_type_filter]):
            result = change_repo.search_changes(
                search_term=search_query,
                profile_id=profile_id,
                change_type=change_type_filter,
                start_date=start_date,
                end_date=end_date,
                limit=per_page,
                offset=offset,
                sort_by=sort_by,
                sort_order=sort_order
            )
            changes = result['changes']
            pagination = {
                'page': result['page'],
                'total_pages': result['total_pages'],
                'total_count': result['total_count'],
                'has_more': result['has_more'],
                'per_page': per_page
            }
        else:
            # Use simple date range query for better performance when no search/filters
            all_changes = change_repo.get_changes_by_date_range(start_date, end_date, profile_id)
            total_count = len(all_changes)
            changes = all_changes[offset:offset + per_page]
            pagination = {
                'page': page,
                'total_pages': (total_count + per_page - 1) // per_page,
                'total_count': total_count,
                'has_more': total_count > offset + len(changes),
                'per_page': per_page
            }
        
        # Get all profiles for filter dropdown
        profiles = profile_repo.get_all_profiles()
        
        return render_template('changes.html',
                             changes=changes,
                             profiles=profiles,
                             pagination=pagination,
                             current_filters={
                                 'profile': profile_filter,
                                 'days': days_filter,
                                 'type': change_type_filter,
                                 'q': search_query,
                                 'sort_by': sort_by,
                                 'sort_order': sort_order
                             })
    
    except Exception as e:
        logger.error(f"Error loading changes: {e}")
        return render_template('error.html', error="Failed to load changes"), 500


@main_bp.route('/profiles')
def profiles():
    """Profile management page with list of all profiles."""
    try:
        profiles = profile_repo.get_all_profiles()
        
        # Get statistics for each profile
        profile_stats = []
        for profile in profiles:
            if profile.profile_id:
                stats = profile_repo.get_profile_stats(profile.profile_id)
                profile_stats.append({
                    'profile': profile,
                    'stats': stats
                })
            else:
                profile_stats.append({
                    'profile': profile,
                    'stats': {}
                })
        
        return render_template('profiles.html', profile_stats=profile_stats)
    
    except Exception as e:
        logger.error(f"Error loading profiles: {e}")
        return render_template('error.html', error="Failed to load profiles"), 500


@main_bp.route('/profiles/add', methods=['GET', 'POST'])
@validate_form_input({
    'username': {'type': 'username', 'required': True},
    'display_name': {'type': 'display_name', 'required': False, 'max_length': 100},
    'is_private': {'type': 'boolean', 'required': False},
    'enabled': {'type': 'boolean', 'required': False},
    'interval_hours': {'type': 'integer', 'required': False, 'min': 1, 'max': 168}
})
@secure_session_management()
def add_profile():
    """Add new profile for monitoring."""
    if request.method == 'POST':
        try:
            # Use validated data from security decorator
            validated = request.validated_data
            clean_username = validated['username']
            display_name = validated['display_name']
            is_private = validated.get('is_private', False)
            enabled = validated.get('enabled', True)
            interval_hours = validated.get('interval_hours', 2)
            
            # Check if profile already exists
            existing_profile = profile_repo.get_profile_by_username(clean_username)
            if existing_profile:
                flash(f'Profile @{clean_username} is already being monitored', 'error')
                return render_template('add_profile.html')
            
            # Create monitoring configuration
            config = MonitoringConfig(
                profile_username=clean_username,
                display_name=display_name if display_name else None,
                is_private=is_private,
                enabled=enabled,
                interval_hours=interval_hours
            )
            
            # Save to database
            profile_id = profile_repo.create_profile(config)
            
            flash(f'Profile @{clean_username} added successfully', 'success')
            return redirect(url_for('main.profiles'))
        
        except ValueError as e:
            flash(f'Invalid input: {e}', 'error')
            return render_template('add_profile.html')
        except Exception as e:
            logger.error(f"Error adding profile: {e}")
            flash('Failed to add profile. Please try again.', 'error')
            return render_template('add_profile.html')
    
    return render_template('add_profile.html')


@main_bp.route('/profiles/<username>/edit', methods=['GET', 'POST'])
@validate_form_input({
    'display_name': {'type': 'display_name', 'required': False, 'max_length': 100},
    'is_private': {'type': 'boolean', 'required': False},
    'enabled': {'type': 'boolean', 'required': False},
    'interval_hours': {'type': 'integer', 'required': False, 'min': 1, 'max': 168}
})
@secure_session_management()
def edit_profile(username: str):
    """Edit profile monitoring settings."""
    try:
        # Validate username parameter
        username = InputValidator.validate_username(username)
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            flash('Profile not found', 'error')
            return redirect(url_for('main.profiles'))
        
        if request.method == 'POST':
            try:
                # Use validated data from security decorator
                validated = request.validated_data
                
                # Update profile configuration
                profile.display_name = validated['display_name']
                profile.is_private = validated.get('is_private', False)
                profile.enabled = validated.get('enabled', True)
                profile.interval_hours = validated.get('interval_hours', 2)
                
                # Save changes
                success = profile_repo.update_profile(profile)
                
                if success:
                    flash(f'Profile @{username} updated successfully', 'success')
                    return redirect(url_for('main.profiles'))
                else:
                    flash('Failed to update profile', 'error')
            
            except ValueError as e:
                flash(f'Invalid input: {e}', 'error')
            except Exception as e:
                logger.error(f"Error updating profile {username}: {e}")
                flash('Failed to update profile. Please try again.', 'error')
        
        return render_template('edit_profile.html', profile=profile)
    
    except Exception as e:
        logger.error(f"Error loading profile {username} for editing: {e}")
        flash('Failed to load profile', 'error')
        return redirect(url_for('main.profiles'))


@main_bp.route('/profiles/<username>/delete', methods=['POST'])
def delete_profile(username: str):
    """Delete profile and all associated data."""
    try:
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            flash('Profile not found', 'error')
            return redirect(url_for('main.profiles'))
        
        # Delete profile and all associated data
        success = profile_repo.delete_profile(username)
        
        if success:
            flash(f'Profile @{username} deleted successfully', 'success')
        else:
            flash('Failed to delete profile', 'error')
    
    except Exception as e:
        logger.error(f"Error deleting profile {username}: {e}")
        flash('Failed to delete profile. Please try again.', 'error')
    
    return redirect(url_for('main.profiles'))


@main_bp.route('/profiles/<username>/force-scan', methods=['POST'])
@secure_session_management()
def force_scan_profile(username: str):
    """Force an immediate scan of a specific profile."""
    try:
        # Get profile first to check if it exists
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            if request.is_json:
                return jsonify({'success': False, 'error': f'Profile @{username} not found'}), 404
            flash(f'Profile @{username} not found', 'error')
            return redirect(url_for('main.profiles'))

        # Check if profile is enabled
        if not profile.enabled:
            if request.is_json:
                return jsonify({'success': False, 'error': 'Profile monitoring is disabled'}), 400
            flash(f'Profile @{username} monitoring is disabled', 'error')
            return redirect(url_for('main.profiles'))

        # Check if a scan is already in progress (basic rate limiting)
        # This is a simple check - in production you might want a more sophisticated approach
        from datetime import datetime, timedelta
        if profile.last_scan:
            time_since_last_scan = datetime.now() - profile.last_scan
            if time_since_last_scan < timedelta(minutes=1):
                error_msg = 'Please wait at least 1 minute between scans to avoid rate limiting'
                if request.is_json:
                    return jsonify({'success': False, 'error': error_msg}), 429
                flash(error_msg, 'warning')
                return redirect(url_for('main.profiles'))

        # Import monitoring service
        from services.monitoring_service import MonitoringService
        from config import Config

        # Initialize monitoring service
        config = Config()
        monitoring_service = MonitoringService(config)

        # Log the manual scan action for audit purposes
        logger.info(f"Manual scan triggered for profile @{username} by user")

        # Trigger immediate scan
        result = monitoring_service.monitor_single_profile(username)

        if result['success']:
            message = f"Scan completed for @{username}. Changes detected: {result['changes_detected']}"
            logger.info(f"Manual scan completed for @{username}: {result['changes_detected']} changes detected")

            if request.is_json:
                return jsonify({
                    'success': True,
                    'message': message,
                    'changes_detected': result['changes_detected'],
                    'profile': username
                })
            flash(message, 'success')
        else:
            error_msg = result.get('message', 'Unknown error occurred during scan')
            logger.warning(f"Manual scan failed for @{username}: {error_msg}")

            if request.is_json:
                return jsonify({'success': False, 'error': error_msg}), 500
            flash(f'Scan failed for @{username}: {error_msg}', 'error')

        return redirect(url_for('main.profiles'))

    except Exception as e:
        logger.error(f"Error during force scan for {username}: {e}")
        error_msg = 'Failed to scan profile. Please try again.'

        if request.is_json:
            return jsonify({'success': False, 'error': error_msg}), 500
        flash(error_msg, 'error')
        return redirect(url_for('main.profiles'))


@main_bp.route('/profiles/<username>/toggle', methods=['POST'])
def toggle_profile(username: str):
    """Toggle profile monitoring on/off."""
    try:
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            return jsonify({'error': 'Profile not found'}), 404
        
        # Toggle enabled status
        profile.enabled = not profile.enabled
        
        # Save changes
        success = profile_repo.update_profile(profile)
        
        if success:
            status = 'enabled' if profile.enabled else 'disabled'
            return jsonify({
                'success': True,
                'enabled': profile.enabled,
                'message': f'Profile @{username} {status}'
            })
        else:
            return jsonify({'error': 'Failed to update profile'}), 500
    
    except Exception as e:
        logger.error(f"Error toggling profile {username}: {e}")
        return jsonify({'error': 'Failed to toggle profile'}), 500


@main_bp.route('/search')
def search():
    """Search page for finding specific users and changes."""
    try:
        # Get all profiles for filter dropdown
        profiles = profile_repo.get_all_profiles()
        
        return render_template('search.html', profiles=profiles)
    
    except Exception as e:
        logger.error(f"Error loading search page: {e}")
        return render_template('error.html', error="Failed to load search page"), 500


@main_bp.route('/charts')
def charts():
    """Data visualization and charts page."""
    try:
        # Get all profiles for filter dropdown
        profiles = profile_repo.get_all_profiles()
        
        return render_template('charts_page.html', profiles=profiles)
    
    except Exception as e:
        logger.error(f"Error loading charts page: {e}")
        return render_template('error.html', error="Failed to load charts page"), 500


@main_bp.route('/settings')
def settings():
    """Settings and configuration management page."""
    try:
        # Get current monitoring settings
        monitoring_settings = settings_repo.get_monitoring_settings()
        
        # Get authentication status
        from services.authentication import AuthenticationManager
        from config import Config
        config = Config()
        auth_manager = AuthenticationManager(config)
        
        auth_status = {
            'has_credentials': auth_manager.has_stored_credentials(),
            'credentials': auth_manager.retrieve_credentials() if auth_manager.has_stored_credentials() else None
        }
        
        # Get API key status
        from web.api_auth import get_api_key_status
        api_key_status = get_api_key_status()
        
        # Get system status
        profiles = profile_repo.get_all_profiles()
        system_status = {
            'total_profiles': len(profiles),
            'enabled_profiles': len([p for p in profiles if p.enabled]),
            'profiles_with_recent_scans': len([p for p in profiles if p.last_scan and 
                                            (datetime.now() - p.last_scan).total_seconds() < 86400]),  # 24 hours
            'database_size': get_database_size(),
            'last_scan_times': {p.profile_username: p.last_scan for p in profiles if p.last_scan}
        }
        
        return render_template('settings.html',
                             monitoring_settings=monitoring_settings,
                             auth_status=auth_status,
                             api_key_status=api_key_status,
                             system_status=system_status)
    
    except Exception as e:
        logger.error(f"Error loading settings page: {e}")
        return render_template('error.html', error="Failed to load settings"), 500


@main_bp.route('/settings/monitoring', methods=['POST'])
@validate_form_input({
    'monitoring_interval_hours': {'type': 'integer', 'required': True, 'min': 1, 'max': 168},
    'data_retention_days': {'type': 'integer', 'required': True, 'min': 1, 'max': 3650},
    'min_request_delay': {'type': 'float', 'required': True, 'min': 0, 'max': 60},
    'max_request_delay': {'type': 'float', 'required': True, 'min': 0, 'max': 60},
    'max_retries': {'type': 'integer', 'required': True, 'min': 0, 'max': 10},
    'profile_processing_delay': {'type': 'float', 'required': True, 'min': 0, 'max': 300}
})
@secure_session_management()
def update_monitoring_settings():
    """Update monitoring configuration settings."""
    try:
        # Use validated data from security decorator
        validated = request.validated_data
        
        # Additional validation for delay settings
        if validated['max_request_delay'] < validated['min_request_delay']:
            flash('Max delay must be greater than or equal to min delay', 'error')
            return redirect(url_for('main.settings'))
        
        # Update settings
        new_settings = {
            'monitoring_interval_hours': validated['monitoring_interval_hours'],
            'data_retention_days': validated['data_retention_days'],
            'min_request_delay': validated['min_request_delay'],
            'max_request_delay': validated['max_request_delay'],
            'max_retries': validated['max_retries'],
            'profile_processing_delay': validated['profile_processing_delay']
        }
        
        if settings_repo.update_monitoring_settings(new_settings):
            flash('Monitoring settings updated successfully', 'success')
        else:
            flash('Failed to update monitoring settings', 'error')
    
    except ValueError as e:
        flash(f'Invalid input: {e}', 'error')
    except Exception as e:
        logger.error(f"Error updating monitoring settings: {e}")
        flash('Failed to update settings. Please try again.', 'error')
    
    return redirect(url_for('main.settings'))


@main_bp.route('/settings/credentials', methods=['POST'])
@validate_form_input({
    'instagram_username': {'type': 'username', 'required': True},
    'instagram_password': {'type': 'string', 'required': True, 'max_length': 200}
})
@secure_session_management()
def update_credentials():
    """Update Instagram credentials."""
    try:
        # Use validated data from security decorator
        validated = request.validated_data
        username = validated['instagram_username']
        password = validated['instagram_password']
        
        # Initialize authentication manager
        from services.authentication import AuthenticationManager
        from config import Config
        config = Config()
        auth_manager = AuthenticationManager(config)
        
        # Validate credentials format
        if not auth_manager.validate_credentials(username, password):
            flash('Invalid username or password format', 'error')
            return redirect(url_for('main.settings'))
        
        # Store credentials
        if auth_manager.store_credentials(username, password):
            flash('Instagram credentials updated successfully', 'success')
        else:
            flash('Failed to store credentials', 'error')
    
    except Exception as e:
        logger.error(f"Error updating credentials: {e}")
        flash('Failed to update credentials. Please try again.', 'error')
    
    return redirect(url_for('main.settings'))


@main_bp.route('/settings/credentials/clear', methods=['POST'])
def clear_credentials():
    """Clear stored Instagram credentials."""
    try:
        from services.authentication import AuthenticationManager
        from config import Config
        config = Config()
        auth_manager = AuthenticationManager(config)
        
        if auth_manager.clear_credentials():
            flash('Instagram credentials cleared successfully', 'success')
        else:
            flash('Failed to clear credentials', 'error')
    
    except Exception as e:
        logger.error(f"Error clearing credentials: {e}")
        flash('Failed to clear credentials. Please try again.', 'error')
    
    return redirect(url_for('main.settings'))


@main_bp.route('/settings/backup/export')
def export_settings():
    """Export settings and configuration as JSON."""
    try:
        # Export settings
        settings_data = settings_repo.export_settings()
        
        # Add profile configurations
        profiles = profile_repo.get_all_profiles()
        settings_data['profiles'] = []
        for profile in profiles:
            settings_data['profiles'].append({
                'username': profile.profile_username,
                'display_name': profile.display_name,
                'is_private': profile.is_private,
                'enabled': profile.enabled,
                'created_at': profile.created_at.isoformat() if profile.created_at else None
            })
        
        # Create response
        from flask import Response
        import json
        
        response = Response(
            json.dumps(settings_data, indent=2),
            mimetype='application/json',
            headers={
                'Content-Disposition': f'attachment; filename=instagram_monitor_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            }
        )
        
        return response
    
    except Exception as e:
        logger.error(f"Error exporting settings: {e}")
        flash('Failed to export settings', 'error')
        return redirect(url_for('main.settings'))


@main_bp.route('/settings/backup/import', methods=['POST'])
def import_settings():
    """Import settings and configuration from JSON file."""
    try:
        if 'backup_file' not in request.files:
            flash('No backup file selected', 'error')
            return redirect(url_for('main.settings'))
        
        file = request.files['backup_file']
        if file.filename == '':
            flash('No backup file selected', 'error')
            return redirect(url_for('main.settings'))
        
        if not file.filename.endswith('.json'):
            flash('Backup file must be a JSON file', 'error')
            return redirect(url_for('main.settings'))
        
        # Read and parse backup file
        import json
        try:
            backup_data = json.loads(file.read().decode('utf-8'))
        except json.JSONDecodeError:
            flash('Invalid backup file format', 'error')
            return redirect(url_for('main.settings'))
        
        # Import settings
        if 'settings' in backup_data:
            if settings_repo.import_settings(backup_data):
                flash('Settings imported successfully', 'success')
            else:
                flash('Failed to import some settings', 'warning')
        
        # Import profiles if requested
        import_profiles = request.form.get('import_profiles') == 'on'
        if import_profiles and 'profiles' in backup_data:
            imported_count = 0
            for profile_data in backup_data['profiles']:
                try:
                    # Check if profile already exists
                    existing = profile_repo.get_profile_by_username(profile_data['username'])
                    if existing:
                        continue  # Skip existing profiles
                    
                    # Create new profile
                    from models.data_models import MonitoringConfig
                    config = MonitoringConfig(
                        profile_username=profile_data['username'],
                        display_name=profile_data.get('display_name'),
                        is_private=profile_data.get('is_private', False),
                        enabled=profile_data.get('enabled', True)
                    )
                    
                    if profile_repo.create_profile(config):
                        imported_count += 1
                
                except Exception as e:
                    logger.warning(f"Failed to import profile {profile_data.get('username', 'unknown')}: {e}")
                    continue
            
            if imported_count > 0:
                flash(f'Imported {imported_count} profiles', 'success')
    
    except Exception as e:
        logger.error(f"Error importing settings: {e}")
        flash('Failed to import backup file', 'error')
    
    return redirect(url_for('main.settings'))


@main_bp.route('/settings/api/generate', methods=['POST'])
def generate_api_key():
    """Generate a new API key."""
    try:
        from web.api_auth import api_key_manager
        
        new_key = api_key_manager.rotate_api_key()
        if new_key:
            flash(f'New API key generated: {new_key}', 'success')
            flash('Please save this key securely. It will not be shown again.', 'warning')
        else:
            flash('Failed to generate API key', 'error')
    
    except Exception as e:
        logger.error(f"Error generating API key: {e}")
        flash('Failed to generate API key', 'error')
    
    return redirect(url_for('main.settings'))


@main_bp.route('/settings/api/revoke', methods=['POST'])
def revoke_api_key():
    """Revoke the current API key."""
    try:
        from web.api_auth import api_key_manager
        
        if api_key_manager.revoke_api_key():
            flash('API key revoked successfully', 'success')
        else:
            flash('Failed to revoke API key', 'error')
    
    except Exception as e:
        logger.error(f"Error revoking API key: {e}")
        flash('Failed to revoke API key', 'error')
    
    return redirect(url_for('main.settings'))


# API Routes for real-time data refresh

@api_bp.route('/dashboard/stats')
def api_dashboard_stats():
    """API endpoint for dashboard statistics."""
    try:
        profiles = profile_repo.get_all_profiles()
        
        # Calculate overall statistics
        stats = {
            'total_profiles': len(profiles),
            'enabled_profiles': len([p for p in profiles if p.enabled]),
            'total_followers': 0,
            'total_following': 0,
            'changes_today': 0
        }
        
        # Get detailed stats for each profile
        for profile in profiles:
            if profile.profile_id:
                profile_stats = profile_repo.get_profile_stats(profile.profile_id)
                stats['total_followers'] += profile_stats.get('current_followers', 0)
                stats['total_following'] += profile_stats.get('current_following', 0)
        
        # Get today's changes
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = datetime.now()
        today_changes = change_repo.get_changes_by_date_range(today_start, today_end)
        stats['changes_today'] = len(today_changes)
        
        return jsonify(stats)
    
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        return jsonify({'error': 'Failed to get statistics'}), 500


@api_bp.route('/dashboard/recent-changes')
def api_recent_changes():
    """API endpoint for recent changes."""
    try:
        limit = int(request.args.get('limit', 10))
        changes = change_repo.get_recent_changes(limit=limit)
        
        # Convert changes to JSON-serializable format
        changes_data = []
        for change in changes:
            changes_data.append({
                'profile_username': change.profile_username,
                'affected_username': change.affected_username,
                'change_type': change.change_type.value,
                'timestamp': change.timestamp.isoformat(),
                'display_text': format_change_display(change)
            })
        
        return jsonify(changes_data)
    
    except Exception as e:
        logger.error(f"Error getting recent changes: {e}")
        return jsonify({'error': 'Failed to get recent changes'}), 500


@api_bp.route('/profile/<username>/stats')
def api_profile_stats(username: str):
    """API endpoint for profile statistics."""
    try:
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            return jsonify({'error': 'Profile not found'}), 404
        
        stats = profile_repo.get_profile_stats(profile.profile_id) if profile.profile_id else {}
        
        # Add profile info
        stats.update({
            'username': profile.profile_username,
            'display_name': profile.display_name,
            'is_private': profile.is_private,
            'monitoring_enabled': profile.enabled,
            'last_scan': profile.last_scan.isoformat() if profile.last_scan else None
        })
        
        return jsonify(stats)
    
    except Exception as e:
        logger.error(f"Error getting profile stats for {username}: {e}")
        return jsonify({'error': 'Failed to get profile statistics'}), 500


@api_bp.route('/monitoring/status')
def api_monitoring_status():
    """API endpoint for monitoring system status."""
    try:
        profiles = profile_repo.get_all_profiles()

        status = {
            'total_profiles': len(profiles),
            'enabled_profiles': len([p for p in profiles if p.enabled]),
            'profiles_due_for_scan': 0,
            'last_scan_times': {}
        }

        for profile in profiles:
            if profile.enabled and profile.is_due_for_scan:
                status['profiles_due_for_scan'] += 1

            status['last_scan_times'][profile.profile_username] = {
                'last_scan': profile.last_scan.isoformat() if profile.last_scan else None,
                'is_due': profile.is_due_for_scan if profile.enabled else False
            }

        return jsonify(status)

    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        return jsonify({'error': 'Failed to get monitoring status'}), 500


# Chart Data Endpoints

@api_bp.route('/dashboard/trend-data')
def api_dashboard_trend_data():
    """API endpoint for dashboard trend data."""
    try:
        # Get days parameter (default 30 days)
        days = int(request.args.get('days', 30))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Get trend data for all profiles combined
        trend_data = change_repo.get_combined_trend_data(start_date, end_date)

        return jsonify({
            'dates': trend_data.get('dates', []),
            'followers': trend_data.get('followers', []),
            'following': trend_data.get('following', []),
            'period_days': days
        })

    except Exception as e:
        logger.error(f"Error getting dashboard trend data: {e}")
        return jsonify({'error': 'Failed to get trend data'}), 500


@api_bp.route('/dashboard/change-distribution')
def api_dashboard_change_distribution():
    """API endpoint for dashboard change distribution."""
    try:
        # Get days parameter (default 30 days)
        days = int(request.args.get('days', 30))

        # Get change statistics for all profiles
        stats = change_repo.get_change_statistics(profile_id=None, days=days)

        return jsonify({
            'gained': stats.get('followers_gained', 0),
            'lost': stats.get('followers_lost', 0),
            'started_following': stats.get('following_started', 0),
            'stopped_following': stats.get('following_stopped', 0),
            'period_days': days
        })

    except Exception as e:
        logger.error(f"Error getting dashboard change distribution: {e}")
        return jsonify({'error': 'Failed to get change distribution'}), 500


@api_bp.route('/dashboard/profile-comparison')
def api_dashboard_profile_comparison():
    """API endpoint for dashboard profile comparison."""
    try:
        profiles = profile_repo.get_enabled_profiles()

        profile_names = []
        follower_counts = []
        following_counts = []

        for profile in profiles:
            if profile.profile_id:
                stats = profile_repo.get_profile_stats(profile.profile_id)
                profile_names.append(profile.profile_username)
                follower_counts.append(stats.get('current_followers', 0))
                following_counts.append(stats.get('current_following', 0))

        return jsonify({
            'profiles': profile_names,
            'followers': follower_counts,
            'following': following_counts
        })

    except Exception as e:
        logger.error(f"Error getting profile comparison: {e}")
        return jsonify({'error': 'Failed to get profile comparison'}), 500


@api_bp.route('/profile/<username>/trend-data')
def api_profile_trend_data(username: str):
    """API endpoint for profile trend data."""
    try:
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            return jsonify({'error': 'Profile not found'}), 404

        if not profile.profile_id:
            return jsonify({'error': 'Profile not initialized'}), 400

        # Get days parameter (default 30 days)
        days = int(request.args.get('days', 30))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Get historical data points
        trend_data = change_repo.get_trend_data(profile.profile_id, start_date, end_date)

        return jsonify({
            'profile': username,
            'dates': trend_data.get('dates', []),
            'followers': trend_data.get('followers', []),
            'following': trend_data.get('following', []),
            'period_days': days
        })

    except Exception as e:
        logger.error(f"Error getting profile trend data for {username}: {e}")
        return jsonify({'error': 'Failed to get trend data'}), 500


@api_bp.route('/profile/<username>/change-distribution')
def api_profile_change_distribution(username: str):
    """API endpoint for profile change distribution."""
    try:
        profile = profile_repo.get_profile_by_username(username)
        if not profile:
            return jsonify({'error': 'Profile not found'}), 404

        if not profile.profile_id:
            return jsonify({'error': 'Profile not initialized'}), 400

        # Get days parameter (default 30 days)
        days = int(request.args.get('days', 30))

        # Get change statistics
        stats = change_repo.get_change_statistics(profile_id=profile.profile_id, days=days)

        return jsonify({
            'profile': username,
            'gained': stats.get('followers_gained', 0),
            'lost': stats.get('followers_lost', 0),
            'started_following': stats.get('following_started', 0),
            'stopped_following': stats.get('following_stopped', 0),
            'period_days': days
        })

    except Exception as e:
        logger.error(f"Error getting profile change distribution for {username}: {e}")
        return jsonify({'error': 'Failed to get change distribution'}), 500


@api_bp.route('/settings/status')
def api_settings_status():
    """API endpoint for settings and system status."""
    try:
        # Get monitoring settings
        monitoring_settings = settings_repo.get_monitoring_settings()
        
        # Get authentication status
        from services.authentication import AuthenticationManager
        from config import Config
        config = Config()
        auth_manager = AuthenticationManager(config)
        
        auth_status = {
            'has_credentials': auth_manager.has_stored_credentials(),
            'username': None
        }
        
        if auth_status['has_credentials']:
            credentials = auth_manager.retrieve_credentials()
            if credentials:
                auth_status['username'] = credentials['username']
        
        # Get system status
        profiles = profile_repo.get_all_profiles()
        system_status = {
            'total_profiles': len(profiles),
            'enabled_profiles': len([p for p in profiles if p.enabled]),
            'profiles_with_recent_scans': len([p for p in profiles if p.last_scan and 
                                            (datetime.now() - p.last_scan).total_seconds() < 86400]),
            'database_size': get_database_size(),
            'last_scan_times': {p.profile_username: p.last_scan.isoformat() if p.last_scan else None 
                              for p in profiles if p.last_scan}
        }
        
        return jsonify({
            'monitoring_settings': monitoring_settings,
            'auth_status': auth_status,
            'system_status': system_status
        })
    
    except Exception as e:
        logger.error(f"Error getting settings status: {e}")
        return jsonify({'error': 'Failed to get settings status'}), 500


@api_bp.route('/search/changes')
def api_search_changes():
    """API endpoint for searching changes with advanced filtering."""
    try:
        # Get search parameters
        search_term = request.args.get('q', '').strip()
        profile_username = request.args.get('profile')
        change_type = request.args.get('type')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)  # Max 100 per page
        sort_by = request.args.get('sort_by', 'timestamp')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Parse dates
        start_date = None
        end_date = None
        
        if start_date_str:
            try:
                start_date = datetime.fromisoformat(start_date_str)
            except ValueError:
                return jsonify({'error': 'Invalid start_date format'}), 400
        
        if end_date_str:
            try:
                end_date = datetime.fromisoformat(end_date_str)
            except ValueError:
                return jsonify({'error': 'Invalid end_date format'}), 400
        
        # Get profile ID if profile specified
        profile_id = None
        if profile_username:
            profile = profile_repo.get_profile_by_username(profile_username)
            if not profile:
                return jsonify({'error': 'Profile not found'}), 404
            profile_id = profile.profile_id
        
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Search changes
        result = change_repo.search_changes(
            search_term=search_term,
            profile_id=profile_id,
            change_type=change_type,
            start_date=start_date,
            end_date=end_date,
            limit=per_page,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Convert changes to JSON-serializable format
        changes_data = []
        for change in result['changes']:
            changes_data.append({
                'profile_username': change.profile_username,
                'affected_username': change.affected_username,
                'change_type': change.change_type.value,
                'timestamp': change.timestamp.isoformat(),
                'display_text': format_change_display(change)
            })
        
        return jsonify({
            'changes': changes_data,
            'pagination': {
                'page': result['page'],
                'per_page': per_page,
                'total_pages': result['total_pages'],
                'total_count': result['total_count'],
                'has_more': result['has_more']
            }
        })
    
    except Exception as e:
        logger.error(f"Error searching changes: {e}")
        return jsonify({'error': 'Failed to search changes'}), 500


@api_bp.route('/search/users')
def api_search_users():
    """API endpoint for searching current followers/following users."""
    try:
        # Get search parameters
        search_term = request.args.get('q', '').strip()
        profile_username = request.args.get('profile')
        user_type = request.args.get('type', 'both')  # both, followers, following
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        sort_by = request.args.get('sort_by', 'username')
        sort_order = request.args.get('sort_order', 'asc')
        
        if not search_term:
            return jsonify({'error': 'Search term is required'}), 400
        
        # Get profile ID if profile specified
        profile_id = None
        if profile_username:
            profile = profile_repo.get_profile_by_username(profile_username)
            if not profile:
                return jsonify({'error': 'Profile not found'}), 404
            profile_id = profile.profile_id
        
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Search users
        result = change_repo.search_current_users(
            search_term=search_term,
            profile_id=profile_id,
            user_type=user_type,
            limit=per_page,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Convert users to JSON-serializable format
        users_data = []
        for user in result['users']:
            users_data.append({
                'profile_username': user['profile_username'],
                'username': user['username'],
                'user_type': user['user_type'],
                'added_at': user['added_at'].isoformat() if user['added_at'] else None
            })
        
        return jsonify({
            'users': users_data,
            'pagination': {
                'page': result['page'],
                'per_page': per_page,
                'total_pages': result['total_pages'],
                'total_count': result['total_count'],
                'has_more': result['has_more']
            }
        })
    
    except Exception as e:
        logger.error(f"Error searching users: {e}")
        return jsonify({'error': 'Failed to search users'}), 500


@api_bp.route('/changes/filters')
def api_changes_filters():
    """API endpoint to get available filter options."""
    try:
        # Get all profiles for profile filter
        profiles = profile_repo.get_all_profiles()
        
        # Get date range of available data
        date_range_query = """
            SELECT 
                MIN(timestamp) as earliest,
                MAX(timestamp) as latest
            FROM (
                SELECT timestamp FROM follower_changes
                UNION ALL
                SELECT timestamp FROM following_changes
            )
        """
        
        date_result = db_manager.execute_query(date_range_query)
        earliest_date = None
        latest_date = None
        
        if date_result and date_result[0]['earliest']:
            earliest_date = date_result[0]['earliest']
            latest_date = date_result[0]['latest']
        
        return jsonify({
            'profiles': [
                {
                    'username': profile.profile_username,
                    'display_name': profile.display_name or profile.profile_username
                }
                for profile in profiles
            ],
            'change_types': [
                {'value': 'gained', 'label': 'Followers Gained'},
                {'value': 'lost', 'label': 'Followers Lost'},
                {'value': 'started_following', 'label': 'Started Following'},
                {'value': 'stopped_following', 'label': 'Stopped Following'}
            ],
            'date_range': {
                'earliest': earliest_date,
                'latest': latest_date
            }
        })
    
    except Exception as e:
        logger.error(f"Error getting filter options: {e}")
        return jsonify({'error': 'Failed to get filter options'}), 500


def format_change_display(change: FollowerChange) -> str:
    """Format a change for display in the UI."""
    if change.change_type.value == 'gained':
        return f"@{change.affected_username} started following @{change.profile_username}"
    elif change.change_type.value == 'lost':
        return f"@{change.affected_username} unfollowed @{change.profile_username}"
    elif change.change_type.value == 'started_following':
        return f"@{change.profile_username} started following @{change.affected_username}"
    elif change.change_type.value == 'stopped_following':
        return f"@{change.profile_username} stopped following @{change.affected_username}"
    else:
        return f"Unknown change type: {change.change_type.value}"


def get_database_size() -> str:
    """Get database file size in human-readable format."""
    try:
        from config import Config
        config = Config()
        db_path = config.get_database_path()
        
        if db_path.exists():
            size_bytes = os.path.getsize(db_path)
            
            # Convert to human-readable format
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size_bytes < 1024.0:
                    return f"{size_bytes:.1f} {unit}"
                size_bytes /= 1024.0
            return f"{size_bytes:.1f} TB"
        else:
            return "0 B"
    except Exception:
        return "Unknown"


def format_time_ago(timestamp: datetime) -> str:
    """Format timestamp as 'time ago' string."""
    now = datetime.now()
    diff = now - timestamp
    
    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    else:
        return "Just now"


# Template filters
@main_bp.app_template_filter('timeago')
def timeago_filter(timestamp):
    """Template filter for formatting timestamps as 'time ago'."""
    if isinstance(timestamp, str):
        timestamp = datetime.fromisoformat(timestamp)
    return format_time_ago(timestamp)


@main_bp.app_template_filter('format_change')
def format_change_filter(change):
    """Template filter for formatting change objects."""
    return format_change_display(change)