/**
 * Form handling utilities
 * Handles form validation, submission, and CSRF protection
 */

// Initialize form handling when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeForms();
    setupFormValidation();
});

/**
 * Initialize all forms on the page
 */
function initializeForms() {
    // Add CSRF tokens to all forms
    addCSRFTokensToForms();
    
    // Setup form submission handlers
    setupFormSubmissionHandlers();
    
    // Setup real-time validation
    setupRealTimeValidation();
}

/**
 * Add CSRF tokens to all forms that don't have them
 */
function addCSRFTokensToForms() {
    const csrfToken = getCSRFToken();
    if (!csrfToken) return;
    
    document.querySelectorAll('form').forEach(form => {
        // Skip if form already has CSRF token
        if (form.querySelector('input[name="csrf_token"]')) return;
        
        // Skip GET forms
        if (form.method.toLowerCase() === 'get') return;
        
        // Add CSRF token input
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
    });
}

/**
 * Setup form submission handlers
 */
function setupFormSubmissionHandlers() {
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', handleFormSubmit);
    });
}

/**
 * Setup real-time form validation
 */
function setupRealTimeValidation() {
    // Username validation
    document.querySelectorAll('input[name="username"]').forEach(input => {
        input.addEventListener('input', validateUsername);
        input.addEventListener('blur', validateUsername);
    });
    
    // Password validation
    document.querySelectorAll('input[type="password"]').forEach(input => {
        input.addEventListener('input', validatePassword);
        input.addEventListener('blur', validatePassword);
    });
    
    // Email validation
    document.querySelectorAll('input[type="email"]').forEach(input => {
        input.addEventListener('input', validateEmail);
        input.addEventListener('blur', validateEmail);
    });
    
    // Number validation
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', validateNumber);
        input.addEventListener('blur', validateNumber);
    });
}

/**
 * Setup form validation rules
 */
function setupFormValidation() {
    // Custom validation messages
    const validationMessages = {
        valueMissing: 'This field is required.',
        typeMismatch: 'Please enter a valid value.',
        tooShort: 'This field is too short.',
        tooLong: 'This field is too long.',
        rangeUnderflow: 'Value is too low.',
        rangeOverflow: 'Value is too high.',
        stepMismatch: 'Please enter a valid value.',
        patternMismatch: 'Please match the requested format.'
    };
    
    // Apply custom validation messages
    document.querySelectorAll('input, textarea, select').forEach(field => {
        field.addEventListener('invalid', function(event) {
            const validity = event.target.validity;
            let message = '';
            
            for (const key in validationMessages) {
                if (validity[key]) {
                    message = validationMessages[key];
                    break;
                }
            }
            
            event.target.setCustomValidity(message);
        });
        
        field.addEventListener('input', function(event) {
            event.target.setCustomValidity('');
        });
    });
}

/**
 * Handle form submission
 */
async function handleFormSubmit(event) {
    const form = event.target;
    
    // Skip if form has data-no-ajax attribute
    if (form.hasAttribute('data-no-ajax')) return;
    
    // Skip if form is already being submitted
    if (form.classList.contains('submitting')) {
        event.preventDefault();
        return;
    }
    
    // Validate form before submission
    if (!form.checkValidity()) {
        event.preventDefault();
        showFormErrors(form);
        return;
    }
    
    // Handle AJAX forms
    if (form.hasAttribute('data-ajax')) {
        event.preventDefault();
        await handleAjaxFormSubmit(form);
    }
}

/**
 * Handle AJAX form submission
 */
async function handleAjaxFormSubmit(form) {
    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
    const originalText = submitButton ? submitButton.textContent : '';
    
    try {
        // Show loading state
        form.classList.add('submitting');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Submitting...';
        }
        
        // Prepare form data
        const formData = new FormData(form);
        
        // Submit form
        const response = await fetch(form.action || window.location.pathname, {
            method: form.method || 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (response.ok) {
            const result = await response.json();
            handleFormSuccess(form, result);
        } else {
            const error = await response.json();
            handleFormError(form, error);
        }
        
    } catch (error) {
        console.error('Form submission error:', error);
        handleFormError(form, { message: 'An error occurred. Please try again.' });
    } finally {
        // Reset loading state
        form.classList.remove('submitting');
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    }
}

/**
 * Handle successful form submission
 */
function handleFormSuccess(form, result) {
    // Show success message
    if (result.message) {
        showNotification(result.message, 'success');
    }
    
    // Redirect if specified
    if (result.redirect) {
        setTimeout(() => {
            window.location.href = result.redirect;
        }, 1000);
        return;
    }
    
    // Reset form if specified
    if (result.reset) {
        form.reset();
        clearFormErrors(form);
    }
    
    // Refresh data if specified
    if (result.refresh && typeof refreshData === 'function') {
        refreshData();
    }
}

/**
 * Handle form submission error
 */
function handleFormError(form, error) {
    // Show error message
    if (error.message) {
        showNotification(error.message, 'error');
    }
    
    // Show field errors
    if (error.errors) {
        showFieldErrors(form, error.errors);
    }
}

/**
 * Show form validation errors
 */
function showFormErrors(form) {
    const invalidFields = form.querySelectorAll(':invalid');
    
    invalidFields.forEach(field => {
        showFieldError(field, field.validationMessage);
    });
    
    // Focus first invalid field
    if (invalidFields.length > 0) {
        invalidFields[0].focus();
    }
}

/**
 * Show field-specific errors
 */
function showFieldErrors(form, errors) {
    Object.entries(errors).forEach(([fieldName, message]) => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (field) {
            showFieldError(field, message);
        }
    });
}

/**
 * Show error for a specific field
 */
function showFieldError(field, message) {
    // Remove existing error
    clearFieldError(field);
    
    // Add error class
    field.classList.add('is-invalid');
    
    // Create error message element
    const errorElement = document.createElement('div');
    errorElement.className = 'invalid-feedback';
    errorElement.textContent = message;
    
    // Insert error message after field
    field.parentNode.insertBefore(errorElement, field.nextSibling);
}

/**
 * Clear error for a specific field
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    
    const errorElement = field.parentNode.querySelector('.invalid-feedback');
    if (errorElement) {
        errorElement.remove();
    }
}

/**
 * Clear all form errors
 */
function clearFormErrors(form) {
    form.querySelectorAll('.is-invalid').forEach(field => {
        clearFieldError(field);
    });
}

/**
 * Validate username field
 */
function validateUsername(event) {
    const field = event.target;
    const value = field.value.trim();
    
    // Clear previous validation
    clearFieldError(field);
    
    if (!value) return;
    
    // Instagram username validation
    const usernameRegex = /^[a-zA-Z0-9._]{1,30}$/;
    
    if (!usernameRegex.test(value)) {
        showFieldError(field, 'Username can only contain letters, numbers, periods, and underscores (max 30 characters)');
        return false;
    }
    
    if (value.startsWith('.') || value.endsWith('.')) {
        showFieldError(field, 'Username cannot start or end with a period');
        return false;
    }
    
    if (value.includes('..')) {
        showFieldError(field, 'Username cannot contain consecutive periods');
        return false;
    }
    
    return true;
}

/**
 * Validate password field
 */
function validatePassword(event) {
    const field = event.target;
    const value = field.value;
    
    // Clear previous validation
    clearFieldError(field);
    
    if (!value) return;
    
    if (value.length < 6) {
        showFieldError(field, 'Password must be at least 6 characters long');
        return false;
    }
    
    return true;
}

/**
 * Validate email field
 */
function validateEmail(event) {
    const field = event.target;
    const value = field.value.trim();
    
    // Clear previous validation
    clearFieldError(field);
    
    if (!value) return;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailRegex.test(value)) {
        showFieldError(field, 'Please enter a valid email address');
        return false;
    }
    
    return true;
}

/**
 * Validate number field
 */
function validateNumber(event) {
    const field = event.target;
    const value = parseFloat(field.value);
    
    // Clear previous validation
    clearFieldError(field);
    
    if (isNaN(value)) return;
    
    const min = parseFloat(field.min);
    const max = parseFloat(field.max);
    
    if (!isNaN(min) && value < min) {
        showFieldError(field, `Value must be at least ${min}`);
        return false;
    }
    
    if (!isNaN(max) && value > max) {
        showFieldError(field, `Value must be at most ${max}`);
        return false;
    }
    
    return true;
}

/**
 * Get CSRF token from meta tag
 */
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
}

/**
 * Show notification to user
 */
function showNotification(message, type = 'info') {
    // Use dashboard utility if available
    if (window.dashboardUtils && window.dashboardUtils.showNotification) {
        window.dashboardUtils.showNotification(message, type);
        return;
    }
    
    // Fallback notification
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Export functions for global use
window.formUtils = {
    addCSRFTokensToForms,
    validateUsername,
    validatePassword,
    validateEmail,
    validateNumber,
    showFieldError,
    clearFieldError,
    clearFormErrors,
    getCSRFToken
};