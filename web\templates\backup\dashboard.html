{% extends "base.html" %}

{% block title %}Backup Management - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-shield-alt"></i> Backup Management
            </h1>
        </div>
    </div>

    <!-- Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Backups</h5>
                            <h2 class="mb-0">{{ backup_stats.get('total_backups', 0) }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-archive fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Size</h5>
                            <h2 class="mb-0">{{ (backup_stats.get('total_size', 0) / 1024 / 1024) | round(1) }} MB</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hdd fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card {% if integrity_status.valid %}bg-success{% else %}bg-danger{% endif %} text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Database Status</h5>
                            <h2 class="mb-0">{% if integrity_status.valid %}OK{% else %}ERROR{% endif %}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas {% if integrity_status.valid %}fa-check-circle{% else %}fa-exclamation-triangle{% endif %} fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Latest Backup</h5>
                            <p class="mb-0">
                                {% if backup_stats.get('latest_backup') %}
                                    {{ backup_stats.latest_backup.get('timestamp', 'Unknown')[:10] }}
                                {% else %}
                                    None
                                {% endif %}
                            </p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('backup.create_backup') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Backup
                        </a>
                        <a href="{{ url_for('backup.restore_backup') }}" class="btn btn-warning">
                            <i class="fas fa-undo"></i> Restore Backup
                        </a>
                        <a href="{{ url_for('backup.validate_database') }}" class="btn btn-info">
                            <i class="fas fa-check"></i> Validate Database
                        </a>
                        <a href="{{ url_for('backup.schedule_backup') }}" class="btn btn-secondary">
                            <i class="fas fa-calendar"></i> Schedule Backups
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Integrity Status -->
    {% if not integrity_status.valid %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Database Integrity Issues Detected</h5>
                {% if integrity_status.get('errors') %}
                <ul class="mb-0">
                    {% for error in integrity_status.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
                <hr>
                <a href="{{ url_for('backup.validate_database') }}" class="btn btn-outline-danger">
                    View Full Report
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Available Backups -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Available Backups</h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cleanupModal">
                            <i class="fas fa-trash"></i> Cleanup Old Backups
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if backups %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Size</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups %}
                                <tr>
                                    <td>
                                        <strong>{{ backup.get('backup_name', 'Unknown') }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge {% if backup.get('type') == 'full' %}bg-primary{% else %}bg-secondary{% endif %}">
                                            {{ backup.get('type', 'Unknown').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ backup.get('timestamp', backup.get('file_modified', 'Unknown'))[:19] }}
                                    </td>
                                    <td>
                                        {{ (backup.get('file_size', 0) / 1024 / 1024) | round(2) }} MB
                                    </td>
                                    <td>
                                        {% if backup.get('status') == 'completed' or not backup.get('status') %}
                                        <span class="badge bg-success">OK</span>
                                        {% else %}
                                        <span class="badge bg-warning">{{ backup.get('status', 'Unknown') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('backup.download_backup', backup_name=backup.get('backup_name', '')) }}" 
                                               class="btn btn-outline-primary" title="Download">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="restoreBackup('{{ backup.file_path }}')" title="Restore">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No backups found</h5>
                        <p class="text-muted">Create your first backup to get started.</p>
                        <a href="{{ url_for('backup.create_backup') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Backup
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ url_for('backup.cleanup_backups') }}">
                <div class="modal-header">
                    <h5 class="modal-title">Cleanup Old Backups</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="retention_days" class="form-label">Delete backups older than:</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="retention_days" name="retention_days" 
                                   value="30" min="1" required>
                            <span class="input-group-text">days</span>
                        </div>
                        <div class="form-text">This action cannot be undone.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Old Backups</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function restoreBackup(backupPath) {
    if (confirm('This will overwrite existing data. Are you sure you want to restore from this backup?')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("backup.restore_backup") }}';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'backup_path';
        input.value = backupPath;
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-refresh backup status every 30 seconds
setInterval(function() {
    fetch('{{ url_for("backup.api_backup_status") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update status indicators if needed
                console.log('Backup status updated:', data);
            }
        })
        .catch(error => console.error('Error fetching backup status:', error));
}, 30000);
</script>
{% endblock %}