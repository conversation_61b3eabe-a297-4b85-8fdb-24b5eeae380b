{% extends "base.html" %}

{% block title %}Dashboard - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="h2 mb-0">
            <i class="bi bi-speedometer2"></i> Dashboard Overview
        </h1>
        <p class="text-muted">Monitor your Instagram profiles and track follower changes</p>
    </div>
</div>

<!-- Overall Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number" id="totalProfiles">{{ total_profiles }}</div>
                <div class="stat-label">Total Profiles</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number text-success" id="enabledProfiles">{{ enabled_profiles }}</div>
                <div class="stat-label">Active Monitoring</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number text-info" id="totalFollowers">0</div>
                <div class="stat-label">Total Followers</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number text-warning" id="changesToday">0</div>
                <div class="stat-label">Changes Today</div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Summary Cards -->
<div class="row mb-4">
    <div class="col">
        <h3 class="h4 mb-3">
            <i class="bi bi-person-circle"></i> Profile Summary
        </h3>
        
        {% if profile_stats %}
        <div class="row">
            {% for item in profile_stats %}
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card profile-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span class="status-indicator {% if item.profile.enabled %}status-active{% else %}status-inactive{% endif %}"></span>
                            <strong>@{{ item.profile.profile_username }}</strong>
                        </div>
                        <div class="text-muted small">
                            {% if item.profile.last_scan %}
                                {{ item.profile.last_scan|timeago }}
                            {% else %}
                                Never scanned
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        {% if item.profile.display_name %}
                        <h6 class="card-title">{{ item.profile.display_name }}</h6>
                        {% endif %}
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="fw-bold text-primary">{{ item.stats.get('current_followers', 0) }}</div>
                                <small class="text-muted">Followers</small>
                            </div>
                            <div class="col-6">
                                <div class="fw-bold text-info">{{ item.stats.get('current_following', 0) }}</div>
                                <small class="text-muted">Following</small>
                            </div>
                        </div>
                        
                        <hr class="my-2">
                        
                        <div class="row text-center small">
                            <div class="col-6">
                                <div class="text-success">+{{ item.stats.get('followers_gained_30d', 0) }}</div>
                                <div class="text-danger">-{{ item.stats.get('followers_lost_30d', 0) }}</div>
                            </div>
                            <div class="col-6">
                                <div class="text-primary">+{{ item.stats.get('following_started_30d', 0) }}</div>
                                <div class="text-warning">-{{ item.stats.get('following_stopped_30d', 0) }}</div>
                            </div>
                        </div>
                        <div class="text-center small text-muted mt-1">Last 30 days</div>
                        
                        <div class="mt-3">
                            <a href="{{ url_for('main.profile_detail', username=item.profile.profile_username) }}" 
                               class="btn btn-outline-primary btn-sm w-100">
                                <i class="bi bi-eye"></i> View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            No profiles are currently being monitored. Add a profile to get started!
        </div>
        {% endif %}
    </div>
</div>

<!-- Data Visualization Charts -->
<div class="row mb-4">
    <div class="col">
        {% include 'charts.html' %}
    </div>
</div>

<!-- Recent Changes Timeline -->
<div class="row">
    <div class="col">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="h4 mb-0">
                <i class="bi bi-activity"></i> Recent Changes
            </h3>
            <a href="{{ url_for('main.changes') }}" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-list"></i> View All Changes
            </a>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div id="recentChanges">
                    {% if recent_changes %}
                    <div class="timeline">
                        {% for change in recent_changes[:10] %}
                        <div class="timeline-item change-item change-{{ change.change_type.value.replace('_', '-') }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="fw-medium">
                                        {{ change|format_change }}
                                    </div>
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> {{ change.timestamp|timeago }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">{{ change.change_type.value.replace('_', ' ').title() }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-clock-history fs-1"></i>
                        <p class="mt-2">No recent changes to display</p>
                        <small>Changes will appear here once monitoring begins</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Page-specific refresh function
    async function pageRefresh() {
        try {
            // Refresh dashboard statistics
            const statsResponse = await fetch('/api/dashboard/stats');
            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                document.getElementById('totalProfiles').textContent = stats.total_profiles;
                document.getElementById('enabledProfiles').textContent = stats.enabled_profiles;
                document.getElementById('totalFollowers').textContent = stats.total_followers.toLocaleString();
                document.getElementById('changesToday').textContent = stats.changes_today;
            }
            
            // Refresh recent changes
            const changesResponse = await fetch('/api/dashboard/recent-changes?limit=10');
            if (changesResponse.ok) {
                const changes = await changesResponse.json();
                updateRecentChanges(changes);
            }
            
        } catch (error) {
            console.error('Error refreshing dashboard:', error);
        }
    }
    
    function updateRecentChanges(changes) {
        const container = document.getElementById('recentChanges');
        
        if (changes.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-clock-history fs-1"></i>
                    <p class="mt-2">No recent changes to display</p>
                    <small>Changes will appear here once monitoring begins</small>
                </div>
            `;
            return;
        }
        
        let timelineHtml = '<div class="timeline">';
        changes.forEach(change => {
            const changeClass = change.change_type.replace(/_/g, '-');
            const badgeText = change.change_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            
            timelineHtml += `
                <div class="timeline-item change-item change-${changeClass}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="fw-medium">
                                ${change.display_text}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-clock"></i> ${timeAgo(change.timestamp)}
                            </small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-secondary">${badgeText}</span>
                        </div>
                    </div>
                </div>
            `;
        });
        timelineHtml += '</div>';
        
        container.innerHTML = timelineHtml;
    }
    
    // Auto-refresh every 5 minutes
    setInterval(pageRefresh, 5 * 60 * 1000);
    
    // Initial load of dynamic data
    document.addEventListener('DOMContentLoaded', function() {
        pageRefresh();
    });
</script>
{% endblock %}