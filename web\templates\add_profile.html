{% extends "base.html" %}

{% block title %}Add Profile - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-plus-circle"></i> Add New Profile
                </h4>
            </div>
            
            <div class="card-body">
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST" id="addProfileForm">
                    {{ csrf_token() }}
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            Instagram Username <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="text" class="form-control" id="username" name="username" 
                                   placeholder="username" required maxlength="30"
                                   pattern="[a-zA-Z0-9._]+" 
                                   title="Username can only contain letters, numbers, periods, and underscores">
                        </div>
                        <div class="form-text">
                            Enter the Instagram username without the @ symbol
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="display_name" class="form-label">Display Name</label>
                        <input type="text" class="form-control" id="display_name" name="display_name" 
                               placeholder="Optional display name" maxlength="100">
                        <div class="form-text">
                            Optional friendly name for this profile
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="interval_hours" class="form-label">Monitoring Interval</label>
                        <select class="form-select" id="interval_hours" name="interval_hours">
                            <option value="1">Every hour</option>
                            <option value="2" selected>Every 2 hours (recommended)</option>
                            <option value="4">Every 4 hours</option>
                            <option value="6">Every 6 hours</option>
                            <option value="12">Every 12 hours</option>
                            <option value="24">Daily</option>
                        </select>
                        <div class="form-text">
                            How often to check for follower changes
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_private" name="is_private">
                                <label class="form-check-label" for="is_private">
                                    Private Profile
                                </label>
                                <div class="form-text">
                                    Check if this is a private Instagram account
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enabled" name="enabled" checked>
                                <label class="form-check-label" for="enabled">
                                    Enable Monitoring
                                </label>
                                <div class="form-text">
                                    Start monitoring immediately after adding
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Note:</strong> For private profiles, you'll need to ensure your Instagram account 
                        follows the target profile and has proper authentication configured.
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.profiles') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Profiles
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.getElementById('addProfileForm').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    
    // Basic client-side validation
    if (!username) {
        e.preventDefault();
        showAlert('Username is required', 'danger');
        return;
    }
    
    // Check for valid Instagram username format
    const usernameRegex = /^[a-zA-Z0-9._]+$/;
    if (!usernameRegex.test(username)) {
        e.preventDefault();
        showAlert('Username can only contain letters, numbers, periods, and underscores', 'danger');
        return;
    }
    
    if (username.length > 30) {
        e.preventDefault();
        showAlert('Username cannot exceed 30 characters', 'danger');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Adding Profile...';
    submitBtn.disabled = true;
    
    // Re-enable button after a delay in case of server error
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const form = document.getElementById('addProfileForm');
    form.insertBefore(alertDiv, form.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Real-time username validation
document.getElementById('username').addEventListener('input', function(e) {
    const username = e.target.value;
    const usernameRegex = /^[a-zA-Z0-9._]*$/;
    
    if (username && !usernameRegex.test(username)) {
        e.target.setCustomValidity('Username can only contain letters, numbers, periods, and underscores');
    } else {
        e.target.setCustomValidity('');
    }
});
</script>
{% endblock %}