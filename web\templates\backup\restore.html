{% extends "base.html" %}

{% block title %}Restore Backup - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-undo"></i> Restore from Backup
                    </h4>
                </div>
                <div class="card-body">
                    {% if backups %}
                    <form method="POST" onsubmit="return confirmRestore()">
                        <div class="mb-3">
                            <label for="backup_path" class="form-label">Select Backup</label>
                            <select class="form-select" id="backup_path" name="backup_path" required onchange="updateBackupInfo()">
                                <option value="">Choose a backup to restore...</option>
                                {% for backup in backups %}
                                <option value="{{ backup.file_path }}" 
                                        data-type="{{ backup.get('type', 'Unknown') }}"
                                        data-timestamp="{{ backup.get('timestamp', 'Unknown') }}"
                                        data-size="{{ backup.get('file_size', 0) }}">
                                    {{ backup.get('backup_name', 'Unknown') }} - {{ backup.get('timestamp', 'Unknown')[:19] }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div id="backup_info" class="alert alert-info" style="display: none;">
                            <h6><i class="fas fa-info-circle"></i> Backup Information</h6>
                            <div id="backup_details"></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Restore Options</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="restore_database" name="restore_database" checked>
                                <label class="form-check-label" for="restore_database">
                                    <strong>Database</strong> - Profiles, followers, changes, and settings
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="restore_configuration" name="restore_configuration" checked>
                                <label class="form-check-label" for="restore_configuration">
                                    <strong>Configuration</strong> - Application settings and preferences
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skip_validation" name="skip_validation">
                                <label class="form-check-label" for="skip_validation">
                                    Skip backup validation (faster but less safe)
                                </label>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Important Warning</h6>
                            <p class="mb-2">
                                Restoring from a backup will <strong>overwrite your current data</strong>. 
                                This action cannot be undone.
                            </p>
                            <p class="mb-0">
                                A backup of your current state will be created automatically before restoration.
                            </p>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('backup.backup_dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-undo"></i> Restore Backup
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Backups Available</h5>
                        <p class="text-muted">You need to create a backup before you can restore from one.</p>
                        <a href="{{ url_for('backup.create_backup') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Backup
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Restore Process Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Restore Process</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-list-ol"></i> Steps</h6>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item">Validate backup integrity</li>
                                <li class="list-group-item">Create pre-restore backup</li>
                                <li class="list-group-item">Stop monitoring services</li>
                                <li class="list-group-item">Restore selected components</li>
                                <li class="list-group-item">Verify restoration</li>
                                <li class="list-group-item">Restart services</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-clock"></i> Estimated Time</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-database"></i> Database only: 1-2 minutes</li>
                                <li><i class="fas fa-cog"></i> Configuration only: &lt;1 minute</li>
                                <li><i class="fas fa-archive"></i> Full restore: 2-5 minutes</li>
                            </ul>
                            
                            <h6 class="mt-3"><i class="fas fa-shield-alt"></i> Safety</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Automatic pre-restore backup</li>
                                <li><i class="fas fa-check text-success"></i> Integrity validation</li>
                                <li><i class="fas fa-check text-success"></i> Rollback capability</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateBackupInfo() {
    const select = document.getElementById('backup_path');
    const infoDiv = document.getElementById('backup_info');
    const detailsDiv = document.getElementById('backup_details');
    
    if (select.value) {
        const option = select.selectedOptions[0];
        const type = option.dataset.type;
        const timestamp = option.dataset.timestamp;
        const size = parseInt(option.dataset.size);
        
        detailsDiv.innerHTML = `
            <ul class="mb-0">
                <li><strong>Type:</strong> ${type.charAt(0).toUpperCase() + type.slice(1)} Backup</li>
                <li><strong>Created:</strong> ${timestamp}</li>
                <li><strong>Size:</strong> ${(size / 1024 / 1024).toFixed(2)} MB</li>
            </ul>
        `;
        infoDiv.style.display = 'block';
    } else {
        infoDiv.style.display = 'none';
    }
}

function confirmRestore() {
    const backupSelect = document.getElementById('backup_path');
    const restoreDatabase = document.getElementById('restore_database').checked;
    const restoreConfiguration = document.getElementById('restore_configuration').checked;
    
    if (!backupSelect.value) {
        alert('Please select a backup to restore.');
        return false;
    }
    
    if (!restoreDatabase && !restoreConfiguration) {
        alert('Please select at least one component to restore.');
        return false;
    }
    
    const components = [];
    if (restoreDatabase) components.push('Database');
    if (restoreConfiguration) components.push('Configuration');
    
    const message = `This will restore the following components from the selected backup:\n\n${components.join(', ')}\n\nYour current data will be overwritten. A backup of your current state will be created automatically.\n\nDo you want to continue?`;
    
    return confirm(message);
}

// Auto-select all options by default
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('restore_database').checked = true;
    document.getElementById('restore_configuration').checked = true;
});
</script>
{% endblock %}