"""
Flask Application Factory

This module contains the Flask application factory function that creates
and configures the Flask application instance.
"""

from flask import Flask
from flask_wtf.csrf import CSRFProtect
from config import Config
from services.logging_config import setup_logging, get_logger, setup_default_notifications
from services.system_monitor import get_system_monitor, setup_default_alert_handlers
from services.log_manager import get_log_manager
from web.error_handlers import register_error_handlers, setup_request_logging
from web.security import SecurityHeaders, register_template_filters
from web.session_security import setup_session_security


def create_app(config: Config = None) -> Flask:
    """
    Create and configure Flask application.
    
    Args:
        config: Configuration object. If None, creates a new Config instance.
        
    Returns:
        Configured Flask application instance.
    """
    if config is None:
        config = Config()
    
    # Set up logging first
    setup_logging(config)
    logger = get_logger(__name__)
    logger.info("Starting Flask application", app_name="Instagram Follower Monitor")
    
    # Initialize monitoring systems
    setup_default_notifications()
    setup_default_alert_handlers()
    
    # Start system monitor
    system_monitor = get_system_monitor(config)
    system_monitor.start_monitoring()
    
    # Start log manager
    log_manager = get_log_manager(config)
    log_manager.start_scheduler()
    
    logger.info("Monitoring systems initialized")
    
    # Create Flask application
    app = Flask(__name__)
    
    # Configure Flask security settings
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['DEBUG'] = config.DEBUG
    app.config['WTF_CSRF_TIME_LIMIT'] = 3600  # 1 hour CSRF token validity
    app.config['WTF_CSRF_SSL_STRICT'] = False  # Allow CSRF over HTTP for development
    app.config['SESSION_COOKIE_SECURE'] = not config.DEBUG  # HTTPS only in production
    app.config['SESSION_COOKIE_HTTPONLY'] = True  # Prevent XSS access to session cookies
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # CSRF protection
    app.config['PERMANENT_SESSION_LIFETIME'] = 86400  # 24 hours session lifetime
    
    # Initialize CSRF protection
    csrf = CSRFProtect(app)
    
    # Configure security headers
    @app.after_request
    def set_security_headers(response):
        """Set security headers on all responses."""
        return SecurityHeaders.apply_security_headers(response)
    
    # Register blueprints
    from web.routes import main_bp, api_bp
    from web.api import api_v1
    from web.monitoring_routes import monitoring_bp
    from web.backup_routes import backup_bp
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(api_v1, url_prefix='/api/v1')
    app.register_blueprint(monitoring_bp)
    app.register_blueprint(backup_bp)
    
    # Set up error handlers
    register_error_handlers(app)
    setup_request_logging(app)
    
    # Register security template filters
    register_template_filters(app)
    
    # Set up session security (without CSRF - using Flask-WTF instead)
    from web.session_security import setup_session_security_without_csrf
    setup_session_security_without_csrf(app)
    
    # Health check route
    @app.route('/health')
    def health():
        return {'status': 'ok', 'message': 'Application is running'}
    
    logger.info("Flask application created successfully")
    return app