"""
Unit tests for authentication management functionality.
"""

import os
import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from cryptography.fernet import Fernet

from config import Config
from services.authentication import AuthenticationManager


class TestAuthenticationManager:
    """Test cases for AuthenticationManager class."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for test files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        config.ENCRYPTION_KEY = Fernet.generate_key().decode()
        return config
    
    @pytest.fixture
    def auth_manager(self, mock_config, temp_dir, monkeypatch):
        """Create an AuthenticationManager instance for testing."""
        # Change to temp directory for test files
        monkeypatch.chdir(temp_dir)
        return AuthenticationManager(mock_config)
    
    def test_store_credentials_success(self, auth_manager):
        """Test successful credential storage."""
        username = "testuser"
        password = "testpassword123"
        
        result = auth_manager.store_credentials(username, password)
        
        assert result is True
        assert auth_manager.has_stored_credentials() is True
        assert auth_manager._credentials_file.exists()
    
    def test_store_credentials_file_permissions(self, auth_manager):
        """Test that stored credentials file has correct permissions."""
        username = "testuser"
        password = "testpassword123"
        
        auth_manager.store_credentials(username, password)
        
        # Check file permissions (Windows and Unix handle permissions differently)
        import platform
        if platform.system() != 'Windows':
            file_mode = oct(auth_manager._credentials_file.stat().st_mode)[-3:]
            assert file_mode == '600'
        else:
            # On Windows, just check that the file exists and is readable
            assert auth_manager._credentials_file.exists()
            assert auth_manager._credentials_file.is_file()
    
    def test_retrieve_credentials_success(self, auth_manager):
        """Test successful credential retrieval."""
        username = "testuser"
        password = "testpassword123"
        
        # Store credentials first
        auth_manager.store_credentials(username, password)
        
        # Retrieve credentials
        retrieved = auth_manager.retrieve_credentials()
        
        assert retrieved is not None
        assert retrieved['username'] == username
        assert retrieved['password'] == password
    
    def test_retrieve_credentials_no_file(self, auth_manager):
        """Test credential retrieval when no file exists."""
        result = auth_manager.retrieve_credentials()
        assert result is None
    
    def test_retrieve_credentials_corrupted_file(self, auth_manager):
        """Test credential retrieval with corrupted file."""
        # Create a corrupted credentials file
        with open(auth_manager._credentials_file, 'wb') as f:
            f.write(b'corrupted_data')
        
        result = auth_manager.retrieve_credentials()
        assert result is None
    
    def test_has_stored_credentials(self, auth_manager):
        """Test checking for stored credentials."""
        # Clear any existing credentials first
        auth_manager.clear_credentials()
        
        # Initially no credentials
        assert auth_manager.has_stored_credentials() is False
        
        # Store credentials
        auth_manager.store_credentials("testuser", "testpass")
        
        # Now should have credentials
        assert auth_manager.has_stored_credentials() is True
    
    def test_clear_credentials(self, auth_manager):
        """Test clearing stored credentials."""
        # Store credentials and session data
        auth_manager.store_credentials("testuser", "testpass")
        auth_manager.store_session_data({"session_id": "test123"})
        
        # Clear credentials
        result = auth_manager.clear_credentials()
        
        assert result is True
        assert auth_manager.has_stored_credentials() is False
        assert not auth_manager._credentials_file.exists()
        assert not auth_manager._session_file.exists()
    
    def test_store_session_data_success(self, auth_manager):
        """Test successful session data storage."""
        session_data = {
            "session_id": "test123",
            "expires_at": "2024-01-01T00:00:00Z",
            "user_agent": "test_agent"
        }
        
        result = auth_manager.store_session_data(session_data)
        
        assert result is True
        assert auth_manager._session_file.exists()
    
    def test_retrieve_session_data_success(self, auth_manager):
        """Test successful session data retrieval."""
        session_data = {
            "session_id": "test123",
            "expires_at": "2024-01-01T00:00:00Z"
        }
        
        # Store session data first
        auth_manager.store_session_data(session_data)
        
        # Retrieve session data
        retrieved = auth_manager.retrieve_session_data()
        
        assert retrieved is not None
        assert retrieved['session_id'] == session_data['session_id']
        assert retrieved['expires_at'] == session_data['expires_at']
    
    def test_retrieve_session_data_no_file(self, auth_manager):
        """Test session data retrieval when no file exists."""
        result = auth_manager.retrieve_session_data()
        assert result is None
    
    def test_validate_credentials_valid(self, auth_manager):
        """Test credential validation with valid credentials."""
        assert auth_manager.validate_credentials("testuser", "password123") is True
        assert auth_manager.validate_credentials("user.name", "mypassword") is True
        assert auth_manager.validate_credentials("user_123", "securepass") is True
    
    def test_validate_credentials_invalid_username(self, auth_manager):
        """Test credential validation with invalid usernames."""
        # Empty username
        assert auth_manager.validate_credentials("", "password123") is False
        
        # Username too long
        long_username = "a" * 31
        assert auth_manager.validate_credentials(long_username, "password123") is False
        
        # Invalid characters
        assert auth_manager.validate_credentials("user@domain", "password123") is False
        assert auth_manager.validate_credentials("user space", "password123") is False
    
    def test_validate_credentials_invalid_password(self, auth_manager):
        """Test credential validation with invalid passwords."""
        # Empty password
        assert auth_manager.validate_credentials("testuser", "") is False
        
        # Password too short
        assert auth_manager.validate_credentials("testuser", "12345") is False
    
    @patch('services.authentication.logger')
    def test_store_credentials_exception_handling(self, mock_logger, auth_manager):
        """Test exception handling during credential storage."""
        # Mock file operations to raise an exception
        with patch('builtins.open', side_effect=PermissionError("Access denied")):
            result = auth_manager.store_credentials("testuser", "testpass")
            
            assert result is False
            mock_logger.error.assert_called()
    
    @patch('services.authentication.logger')
    def test_retrieve_credentials_exception_handling(self, mock_logger, auth_manager):
        """Test exception handling during credential retrieval."""
        # Create a file that will cause decryption to fail
        with open(auth_manager._credentials_file, 'wb') as f:
            f.write(b'invalid_encrypted_data')
        
        result = auth_manager.retrieve_credentials()
        
        assert result is None
        mock_logger.error.assert_called()
    
    def test_encryption_decryption_roundtrip(self, auth_manager):
        """Test that encryption and decryption work correctly together."""
        original_data = {
            "username": "testuser",
            "password": "testpassword123",
            "stored_at": "/test/path"
        }
        
        # Encrypt data
        encrypted = auth_manager._fernet.encrypt(json.dumps(original_data).encode())
        
        # Decrypt data
        decrypted = json.loads(auth_manager._fernet.decrypt(encrypted).decode())
        
        assert decrypted == original_data
    
    def test_different_encryption_keys_fail(self, mock_config, temp_dir, monkeypatch):
        """Test that different encryption keys cannot decrypt each other's data."""
        monkeypatch.chdir(temp_dir)
        
        # Create first auth manager with one key
        auth_manager1 = AuthenticationManager(mock_config)
        auth_manager1.store_credentials("testuser", "testpass")
        
        # Create second auth manager with different key
        mock_config.ENCRYPTION_KEY = Fernet.generate_key().decode()
        auth_manager2 = AuthenticationManager(mock_config)
        
        # Second manager should not be able to decrypt first manager's data
        result = auth_manager2.retrieve_credentials()
        assert result is None