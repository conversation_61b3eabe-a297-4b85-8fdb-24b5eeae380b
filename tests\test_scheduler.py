"""
Tests for scheduler service functionality.

This module tests the scheduling system including job management,
persistence, retry logic, and error handling.
"""

import pytest
import tempfile
import time
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from services.scheduler import SchedulerService
from services.scheduler_manager import SchedulerManager
from config import Config


class TestSchedulerService:
    """Test cases for SchedulerService."""
    
    @pytest.fixture
    def temp_config(self):
        """Create temporary config for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = Mock(spec=Config)
            config.get_database_path.return_value = Path(temp_dir) / "test_scheduler.db"
            config.MONITORING_INTERVAL_HOURS = 2
            config.MAX_RETRIES = 3
            yield config
    
    @pytest.fixture
    def scheduler_service(self, temp_config):
        """Create scheduler service for testing."""
        service = SchedulerService(temp_config)
        yield service
        # Cleanup
        if service.is_running():
            service.stop(wait=False)
    
    def test_scheduler_initialization(self, scheduler_service):
        """Test scheduler service initialization."""
        assert scheduler_service is not None
        assert not scheduler_service.is_running()
        assert scheduler_service._monitoring_callback is None
    
    def test_scheduler_start_stop(self, scheduler_service):
        """Test starting and stopping the scheduler."""
        # Test start
        assert scheduler_service.start()
        assert scheduler_service.is_running()
        
        # Test start when already running
        assert scheduler_service.start()  # Should return True
        
        # Test stop
        assert scheduler_service.stop()
        assert not scheduler_service.is_running()
        
        # Test stop when not running
        assert scheduler_service.stop()  # Should return True
    
    def test_set_monitoring_callback(self, scheduler_service):
        """Test setting monitoring callback."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        assert scheduler_service._monitoring_callback == callback
    
    def test_schedule_monitoring_job_without_callback(self, scheduler_service):
        """Test scheduling job without callback fails."""
        scheduler_service.start()
        
        # Should fail without callback
        assert not scheduler_service.schedule_monitoring_job()
    
    def test_schedule_monitoring_job_without_running_scheduler(self, scheduler_service):
        """Test scheduling job without running scheduler fails."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        
        # Should fail when scheduler not running
        assert not scheduler_service.schedule_monitoring_job()
    
    def test_schedule_monitoring_job_success(self, scheduler_service):
        """Test successful job scheduling."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        scheduler_service.start()
        
        # Schedule job
        assert scheduler_service.schedule_monitoring_job(
            job_id='test_job',
            interval_hours=1
        )
        
        # Verify job exists
        assert scheduler_service.job_exists('test_job')
        
        # Get job info
        job_info = scheduler_service.get_job_info('test_job')
        assert job_info is not None
        assert job_info['id'] == 'test_job'
        assert 'interval' in job_info['trigger'].lower()
    
    def test_schedule_one_time_job(self, scheduler_service):
        """Test scheduling one-time job."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        scheduler_service.start()
        
        run_time = datetime.now(timezone.utc) + timedelta(seconds=10)
        
        # Schedule one-time job
        assert scheduler_service.schedule_one_time_job(
            job_id='one_time_test',
            run_time=run_time
        )
        
        # Verify job exists
        assert scheduler_service.job_exists('one_time_test')
        
        # Get job info
        job_info = scheduler_service.get_job_info('one_time_test')
        assert job_info is not None
        assert job_info['id'] == 'one_time_test'
    
    def test_remove_job(self, scheduler_service):
        """Test job removal."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        scheduler_service.start()
        
        # Schedule and then remove job
        scheduler_service.schedule_monitoring_job(job_id='remove_test')
        assert scheduler_service.job_exists('remove_test')
        
        assert scheduler_service.remove_job('remove_test')
        assert not scheduler_service.job_exists('remove_test')
    
    def test_pause_resume_job(self, scheduler_service):
        """Test pausing and resuming jobs."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        scheduler_service.start()
        
        # Schedule job
        scheduler_service.schedule_monitoring_job(job_id='pause_test')
        
        # Pause job
        assert scheduler_service.pause_job('pause_test')
        
        # Resume job
        assert scheduler_service.resume_job('pause_test')
    
    def test_modify_job_interval(self, scheduler_service):
        """Test modifying job interval."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        scheduler_service.start()
        
        # Schedule job
        scheduler_service.schedule_monitoring_job(job_id='modify_test', interval_hours=2)
        
        # Modify interval
        assert scheduler_service.modify_job_interval('modify_test', 4)
        
        # Verify modification
        job_info = scheduler_service.get_job_info('modify_test')
        assert job_info is not None
    
    def test_get_all_jobs(self, scheduler_service):
        """Test getting all jobs."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        scheduler_service.start()
        
        # Schedule multiple jobs
        scheduler_service.schedule_monitoring_job(job_id='job1')
        scheduler_service.schedule_monitoring_job(job_id='job2')
        
        # Get all jobs
        jobs = scheduler_service.get_all_jobs()
        assert len(jobs) == 2
        
        job_ids = [job['id'] for job in jobs]
        assert 'job1' in job_ids
        assert 'job2' in job_ids
    
    def test_run_job_now(self, scheduler_service):
        """Test running job immediately."""
        callback = Mock()
        scheduler_service.set_monitoring_callback(callback)
        scheduler_service.start()
        
        # Schedule job
        scheduler_service.schedule_monitoring_job(job_id='run_now_test')
        
        # Run job now
        assert scheduler_service.run_job_now('run_now_test')
    
    def test_get_scheduler_status(self, scheduler_service):
        """Test getting scheduler status."""
        status = scheduler_service.get_scheduler_status()
        
        assert 'running' in status
        assert 'job_count' in status
        assert 'statistics' in status
        
        # Test with running scheduler
        scheduler_service.start()
        status = scheduler_service.get_scheduler_status()
        assert status['running'] is True
    
    def test_job_execution_with_retry(self, scheduler_service):
        """Test job execution with retry logic."""
        # Mock callback that fails first two times, succeeds third time
        callback = Mock()
        callback.side_effect = [
            Exception("First failure"),
            Exception("Second failure"),
            {'success': True, 'message': 'Success on third try'}
        ]
        
        scheduler_service.set_monitoring_callback(callback)
        
        # Test retry logic
        result = scheduler_service._execute_with_retry(callback, max_retries=3, retry_delay=0.1)
        
        assert result == {'success': True, 'message': 'Success on third try'}
        assert callback.call_count == 3
    
    def test_job_execution_all_retries_fail(self, scheduler_service):
        """Test job execution when all retries fail."""
        callback = Mock()
        callback.side_effect = Exception("Always fails")
        
        scheduler_service.set_monitoring_callback(callback)
        
        # Test that exception is raised after all retries
        with pytest.raises(Exception, match="Always fails"):
            scheduler_service._execute_with_retry(callback, max_retries=2, retry_delay=0.1)
        
        assert callback.call_count == 3  # Initial + 2 retries
    
    @patch('time.sleep')  # Mock sleep to speed up test
    def test_monitoring_job_execution(self, mock_sleep, scheduler_service):
        """Test monitoring job execution."""
        callback = Mock()
        callback.return_value = {'success': True, 'message': 'Test success'}
        
        scheduler_service.set_monitoring_callback(callback)
        
        # Execute monitoring job
        scheduler_service._execute_monitoring_job()
        
        # Verify callback was called
        callback.assert_called_once()
        
        # Verify statistics updated
        stats = scheduler_service._job_stats
        assert stats['total_executions'] == 1
        assert stats['successful_executions'] == 1
        assert stats['failed_executions'] == 0
    
    @patch('time.sleep')
    def test_monitoring_job_execution_failure(self, mock_sleep, scheduler_service):
        """Test monitoring job execution with failure."""
        callback = Mock()
        callback.side_effect = Exception("Test failure")
        
        scheduler_service.set_monitoring_callback(callback)
        
        # Execute monitoring job (should handle exception)
        scheduler_service._execute_monitoring_job()
        
        # Verify statistics updated for failure
        stats = scheduler_service._job_stats
        assert stats['total_executions'] == 1
        assert stats['successful_executions'] == 0
        assert stats['failed_executions'] == 1
        assert stats['last_error'] == "Test failure"


class TestSchedulerManager:
    """Test cases for SchedulerManager."""
    
    @pytest.fixture
    def temp_config(self):
        """Create temporary config for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = Mock(spec=Config)
            config.get_database_path.return_value = Path(temp_dir) / "test_manager.db"
            config.MONITORING_INTERVAL_HOURS = 2
            config.MAX_RETRIES = 3
            yield config
    
    @pytest.fixture
    def monitoring_service(self):
        """Create mock monitoring service."""
        service = Mock()
        service.start_monitoring_cycle.return_value = {
            'success': True,
            'message': 'Test monitoring completed'
        }
        service.cleanup_old_data.return_value = {
            'success': True,
            'cleanup_stats': {'deleted_records': 10}
        }
        return service
    
    @pytest.fixture
    def scheduler_manager(self, temp_config):
        """Create scheduler manager for testing."""
        manager = SchedulerManager(temp_config)
        yield manager
        # Cleanup
        if manager.is_initialized():
            manager.shutdown(wait=False)
    
    def test_scheduler_manager_initialization(self, scheduler_manager, monitoring_service):
        """Test scheduler manager initialization."""
        assert not scheduler_manager.is_initialized()
        
        # Initialize
        assert scheduler_manager.initialize(monitoring_service)
        assert scheduler_manager.is_initialized()
        
        # Verify monitoring service is set
        assert scheduler_manager.monitoring_service == monitoring_service
    
    def test_scheduler_manager_shutdown(self, scheduler_manager, monitoring_service):
        """Test scheduler manager shutdown."""
        scheduler_manager.initialize(monitoring_service)
        
        # Shutdown
        assert scheduler_manager.shutdown()
        assert not scheduler_manager.is_initialized()
    
    def test_start_periodic_monitoring(self, scheduler_manager, monitoring_service):
        """Test starting periodic monitoring."""
        scheduler_manager.initialize(monitoring_service)
        
        # Start periodic monitoring
        result = scheduler_manager.start_periodic_monitoring(interval_hours=3)
        
        assert result['success'] is True
        assert result['interval_hours'] == 3
        assert 'job_id' in result
    
    def test_start_periodic_monitoring_with_immediate_run(self, scheduler_manager, monitoring_service):
        """Test starting periodic monitoring with immediate execution."""
        scheduler_manager.initialize(monitoring_service)
        
        # Start with immediate run
        result = scheduler_manager.start_periodic_monitoring(
            interval_hours=2,
            start_immediately=True
        )
        
        assert result['success'] is True
        assert result['started_immediately'] is True
    
    def test_stop_periodic_monitoring(self, scheduler_manager, monitoring_service):
        """Test stopping periodic monitoring."""
        scheduler_manager.initialize(monitoring_service)
        
        # Start then stop
        scheduler_manager.start_periodic_monitoring()
        result = scheduler_manager.stop_periodic_monitoring()
        
        assert result['success'] is True
    
    def test_pause_resume_periodic_monitoring(self, scheduler_manager, monitoring_service):
        """Test pausing and resuming periodic monitoring."""
        scheduler_manager.initialize(monitoring_service)
        
        # Start monitoring
        scheduler_manager.start_periodic_monitoring()
        
        # Pause
        result = scheduler_manager.pause_periodic_monitoring()
        assert result['success'] is True
        
        # Resume
        result = scheduler_manager.resume_periodic_monitoring()
        assert result['success'] is True
    
    def test_update_monitoring_interval(self, scheduler_manager, monitoring_service):
        """Test updating monitoring interval."""
        scheduler_manager.initialize(monitoring_service)
        
        # Start monitoring
        scheduler_manager.start_periodic_monitoring(interval_hours=2)
        
        # Update interval
        result = scheduler_manager.update_monitoring_interval(4)
        
        assert result['success'] is True
        assert result['interval_hours'] == 4
    
    def test_run_monitoring_now(self, scheduler_manager, monitoring_service):
        """Test running monitoring immediately."""
        scheduler_manager.initialize(monitoring_service)
        
        # Run monitoring now
        result = scheduler_manager.run_monitoring_now()
        
        assert result['success'] is True
        assert 'job_id' in result
        assert 'run_time' in result
    
    def test_get_monitoring_schedule_status(self, scheduler_manager, monitoring_service):
        """Test getting monitoring schedule status."""
        # Test uninitialized
        status = scheduler_manager.get_monitoring_schedule_status()
        assert status['initialized'] is False
        
        # Test initialized
        scheduler_manager.initialize(monitoring_service)
        status = scheduler_manager.get_monitoring_schedule_status()
        
        assert status['initialized'] is True
        assert 'scheduler_running' in status
        assert 'periodic_monitoring_active' in status
    
    def test_schedule_data_cleanup(self, scheduler_manager, monitoring_service):
        """Test scheduling data cleanup job."""
        scheduler_manager.initialize(monitoring_service)
        
        # Schedule cleanup
        result = scheduler_manager.schedule_data_cleanup(
            interval_hours=24,
            start_time_hour=3
        )
        
        assert result['success'] is True
        assert result['interval_hours'] == 24
        assert 'next_run_time' in result
    
    def test_get_next_monitoring_time(self, scheduler_manager, monitoring_service):
        """Test getting next monitoring time."""
        scheduler_manager.initialize(monitoring_service)
        
        # No monitoring scheduled
        assert scheduler_manager.get_next_monitoring_time() is None
        
        # Schedule monitoring
        scheduler_manager.start_periodic_monitoring()
        next_time = scheduler_manager.get_next_monitoring_time()
        
        assert next_time is not None
        assert isinstance(next_time, datetime)
    
    def test_operations_without_initialization(self, scheduler_manager):
        """Test that operations fail gracefully without initialization."""
        # All operations should return failure when not initialized
        result = scheduler_manager.start_periodic_monitoring()
        assert result['success'] is False
        assert 'not initialized' in result['message']
        
        result = scheduler_manager.stop_periodic_monitoring()
        assert result['success'] is False
        
        result = scheduler_manager.run_monitoring_now()
        assert result['success'] is False
    
    def test_invalid_interval_validation(self, scheduler_manager, monitoring_service):
        """Test validation of monitoring intervals."""
        scheduler_manager.initialize(monitoring_service)
        
        # Test invalid interval
        result = scheduler_manager.start_periodic_monitoring(interval_hours=0)
        assert result['success'] is False
        assert 'at least 1 hour' in result['message']
        
        result = scheduler_manager.update_monitoring_interval(-1)
        assert result['success'] is False


class TestSchedulerIntegration:
    """Integration tests for scheduler components."""
    
    @pytest.fixture
    def temp_config(self):
        """Create temporary config for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = Mock(spec=Config)
            config.get_database_path.return_value = Path(temp_dir) / "test_integration.db"
            config.MONITORING_INTERVAL_HOURS = 1
            config.MAX_RETRIES = 2
            yield config
    
    def test_job_persistence_across_restarts(self, temp_config):
        """Test that jobs persist across scheduler restarts."""
        # Create first scheduler instance
        scheduler1 = SchedulerService(temp_config)
        callback = Mock(return_value={'success': True})
        
        scheduler1.set_monitoring_callback(callback)
        scheduler1.start()
        
        # Schedule job
        scheduler1.schedule_monitoring_job(job_id='persistent_test')
        assert scheduler1.job_exists('persistent_test')
        
        # Stop scheduler
        scheduler1.stop()
        
        # Create new scheduler instance (simulating restart)
        scheduler2 = SchedulerService(temp_config)
        scheduler2.set_monitoring_callback(callback)
        scheduler2.start()
        
        # Job should still exist
        assert scheduler2.job_exists('persistent_test')
        
        # Cleanup
        scheduler2.stop()
    
    def test_scheduler_manager_full_workflow(self, temp_config):
        """Test complete scheduler manager workflow."""
        monitoring_service = Mock()
        monitoring_service.start_monitoring_cycle.return_value = {
            'success': True,
            'profiles_processed': 2,
            'changes_detected': 5
        }
        
        manager = SchedulerManager(temp_config)
        
        try:
            # Initialize
            assert manager.initialize(monitoring_service)
            
            # Start periodic monitoring
            result = manager.start_periodic_monitoring(interval_hours=1)
            assert result['success'] is True
            
            # Check status
            status = manager.get_monitoring_schedule_status()
            assert status['periodic_monitoring_active'] is True
            
            # Update interval
            result = manager.update_monitoring_interval(2)
            assert result['success'] is True
            
            # Pause and resume
            assert manager.pause_periodic_monitoring()['success']
            assert manager.resume_periodic_monitoring()['success']
            
            # Run immediately
            result = manager.run_monitoring_now()
            assert result['success'] is True
            
            # Stop monitoring
            result = manager.stop_periodic_monitoring()
            assert result['success'] is True
            
        finally:
            manager.shutdown(wait=False)
    
    @patch('time.sleep')
    def test_error_handling_and_recovery(self, mock_sleep, temp_config):
        """Test error handling and recovery mechanisms."""
        scheduler = SchedulerService(temp_config)
        
        # Mock callback that fails then succeeds
        callback = Mock()
        callback.side_effect = [
            Exception("Network error"),
            {'success': True, 'message': 'Recovered'}
        ]
        
        scheduler.set_monitoring_callback(callback)
        
        try:
            scheduler.start()
            
            # Execute job with retry
            result = scheduler._execute_with_retry(callback, max_retries=2, retry_delay=0.1)
            
            # Should succeed after retry
            assert result['success'] is True
            assert callback.call_count == 2
            
        finally:
            scheduler.stop(wait=False)