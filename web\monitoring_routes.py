"""
Monitoring and Error Tracking Routes

This module provides web routes for monitoring system health,
viewing error logs, and accessing system metrics.
"""

from flask import Blueprint, render_template, jsonify, request
from datetime import datetime, timedelta
from typing import Dict, Any, List

from services.logging_config import get_logger, get_critical_notifier
from services.system_monitor import get_system_monitor, AlertLevel
from services.log_manager import get_log_manager
from web.api_auth import require_api_key


logger = get_logger(__name__)
monitoring_bp = Blueprint('monitoring', __name__, url_prefix='/monitoring')


@monitoring_bp.route('/health')
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'instagram-follower-monitor'
    })


@monitoring_bp.route('/system-status')
def system_status():
    """Get comprehensive system status."""
    monitor = get_system_monitor()
    health_data = monitor.get_system_health()
    
    return render_template('monitoring/system_status.html', 
                         health_data=health_data,
                         page_title='System Status')


@monitoring_bp.route('/api/system-status')
@require_api_key()
def api_system_status():
    """API endpoint for system status."""
    monitor = get_system_monitor()
    return jsonify(monitor.get_system_health())


@monitoring_bp.route('/alerts')
def alerts_dashboard():
    """Dashboard for viewing system alerts."""
    monitor = get_system_monitor()
    
    # Get alerts for different time periods
    recent_alerts = monitor.get_recent_alerts(hours=24)
    critical_alerts = monitor.get_recent_alerts(hours=24, level=AlertLevel.CRITICAL)
    
    return render_template('monitoring/alerts.html',
                         recent_alerts=recent_alerts,
                         critical_alerts=critical_alerts,
                         page_title='System Alerts')


@monitoring_bp.route('/api/alerts')
@require_api_key()
def api_alerts():
    """API endpoint for system alerts."""
    hours = request.args.get('hours', 24, type=int)
    level = request.args.get('level')
    
    monitor = get_system_monitor()
    alert_level = AlertLevel(level) if level else None
    
    alerts = monitor.get_recent_alerts(hours=hours, level=alert_level)
    
    return jsonify({
        'alerts': alerts,
        'count': len(alerts),
        'hours': hours,
        'level': level
    })


@monitoring_bp.route('/errors')
def errors_dashboard():
    """Dashboard for viewing error logs and statistics."""
    notifier = get_critical_notifier()
    monitor = get_system_monitor()
    
    # Get recent critical errors
    critical_errors = notifier.get_recent_critical_errors(hours=24)
    
    # Get error statistics from monitor
    health_data = monitor.get_system_health()
    
    return render_template('monitoring/errors.html',
                         critical_errors=critical_errors,
                         health_data=health_data,
                         page_title='Error Monitoring')


@monitoring_bp.route('/api/errors')
@require_api_key()
def api_errors():
    """API endpoint for error information."""
    hours = request.args.get('hours', 24, type=int)
    
    notifier = get_critical_notifier()
    errors = notifier.get_recent_critical_errors(hours=hours)
    
    return jsonify({
        'errors': errors,
        'count': len(errors),
        'hours': hours
    })


@monitoring_bp.route('/performance')
def performance_dashboard():
    """Dashboard for viewing performance metrics."""
    monitor = get_system_monitor()
    
    # Get performance metrics for different components
    components = ['web_application', 'instagram_client', 'monitoring_service', 'database']
    metrics_by_component = {}
    
    for component in components:
        metrics_by_component[component] = monitor.get_performance_metrics(
            component=component, hours=24
        )
    
    return render_template('monitoring/performance.html',
                         metrics_by_component=metrics_by_component,
                         page_title='Performance Monitoring')


@monitoring_bp.route('/api/performance')
@require_api_key()
def api_performance():
    """API endpoint for performance metrics."""
    component = request.args.get('component')
    hours = request.args.get('hours', 24, type=int)
    
    monitor = get_system_monitor()
    metrics = monitor.get_performance_metrics(component=component, hours=hours)
    
    return jsonify({
        'metrics': metrics,
        'count': len(metrics),
        'component': component,
        'hours': hours
    })


@monitoring_bp.route('/logs')
def logs_dashboard():
    """Dashboard for viewing and managing log files."""
    log_manager = get_log_manager()
    
    # Get log statistics
    log_stats = log_manager.get_log_statistics()
    
    return render_template('monitoring/logs.html',
                         log_stats=log_stats,
                         page_title='Log Management')


@monitoring_bp.route('/api/logs/stats')
@require_api_key()
def api_log_stats():
    """API endpoint for log statistics."""
    log_manager = get_log_manager()
    return jsonify(log_manager.get_log_statistics())


@monitoring_bp.route('/api/logs/cleanup', methods=['POST'])
@require_api_key()
def api_log_cleanup():
    """API endpoint to trigger log cleanup."""
    days = request.json.get('days', 30) if request.json else 30
    
    log_manager = get_log_manager()
    
    try:
        log_manager.cleanup_old_logs()
        log_manager.compress_old_logs()
        
        return jsonify({
            'success': True,
            'message': f'Log cleanup completed for files older than {days} days'
        })
    
    except Exception as e:
        logger.error("Log cleanup failed", error=str(e))
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@monitoring_bp.route('/api/logs/export', methods=['POST'])
@require_api_key()
def api_log_export():
    """API endpoint to export logs."""
    data = request.json or {}
    
    try:
        start_date = datetime.fromisoformat(data.get('start_date', 
            (datetime.now() - timedelta(days=1)).isoformat()))
        end_date = datetime.fromisoformat(data.get('end_date', 
            datetime.now().isoformat()))
        components = data.get('components')
        
        log_manager = get_log_manager()
        export_file = log_manager.export_logs(start_date, end_date, components)
        
        return jsonify({
            'success': True,
            'export_file': str(export_file),
            'message': 'Log export completed'
        })
    
    except Exception as e:
        logger.error("Log export failed", error=str(e))
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@monitoring_bp.errorhandler(Exception)
def handle_monitoring_error(error):
    """Handle errors in monitoring routes."""
    logger.exception("Error in monitoring route")
    
    if request.path.startswith('/monitoring/api/'):
        return jsonify({
            'error': 'Internal monitoring error',
            'message': str(error)
        }), 500
    else:
        return render_template('errors/500.html'), 500