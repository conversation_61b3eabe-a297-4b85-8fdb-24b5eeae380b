{% extends "base.html" %}

{% block title %}Error {{ error_code }} - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error {{ error_code }}
                    </h4>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ error_message }}</h5>
                    
                    {% if error_details %}
                    <div class="mt-3">
                        <h6>Details:</h6>
                        <ul class="list-unstyled">
                            {% for key, value in error_details.items() %}
                            {% if value %}
                            <li><strong>{{ key|title }}:</strong> {{ value }}</li>
                            {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <div class="mt-4">
                        <h6>What you can do:</h6>
                        <ul>
                            <li>Check if the information you entered is correct</li>
                            <li>Try refreshing the page</li>
                            <li>Go back to the <a href="{{ url_for('main.dashboard') }}">dashboard</a></li>
                            <li>If the problem persists, check the system logs</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>
                            Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}