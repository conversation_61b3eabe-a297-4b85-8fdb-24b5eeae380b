"""
Security utilities for input validation, sanitization, and protection.

This module provides comprehensive security measures including input validation,
sanitization, CSRF protection, and XSS prevention for the Instagram Follower Monitor.
"""

import re
import html
import bleach
from typing import Any, Dict, List, Optional, Union
from functools import wraps
from flask import request, jsonify, session, current_app
from werkzeug.exceptions import BadRequest
import logging

logger = logging.getLogger(__name__)

# Allowed HTML tags and attributes for sanitization
ALLOWED_TAGS = ['b', 'i', 'u', 'em', 'strong', 'p', 'br']
ALLOWED_ATTRIBUTES = {}

class InputValidator:
    """Comprehensive input validation and sanitization."""
    
    @staticmethod
    def validate_username(username: str) -> str:
        """Validate and sanitize Instagram username."""
        if not username:
            raise ValueError("Username cannot be empty")
        
        # Remove @ symbol and convert to lowercase
        clean_username = username.lstrip('@').lower().strip()
        
        # Validate length
        if len(clean_username) < 1:
            raise ValueError("Username cannot be empty")
        if len(clean_username) > 30:
            raise ValueError("Username cannot exceed 30 characters")
        
        # Validate characters - Instagram usernames can contain letters, numbers, periods, and underscores
        if not re.match(r'^[a-zA-Z0-9._]+$', clean_username):
            raise ValueError("Username can only contain letters, numbers, periods, and underscores")
        
        # Additional Instagram-specific rules
        if clean_username.startswith('.') or clean_username.endswith('.'):
            raise ValueError("Username cannot start or end with a period")
        
        if '..' in clean_username:
            raise ValueError("Username cannot contain consecutive periods")
        
        return clean_username
    
    @staticmethod
    def validate_display_name(display_name: Optional[str]) -> Optional[str]:
        """Validate and sanitize display name."""
        if not display_name:
            return None
        
        # Strip whitespace
        clean_name = display_name.strip()
        
        if len(clean_name) > 100:
            raise ValueError("Display name cannot exceed 100 characters")
        
        # Sanitize HTML and remove potentially dangerous content
        clean_name = bleach.clean(clean_name, tags=[], attributes={}, strip=True)
        
        # Additional validation - no control characters
        if any(ord(char) < 32 for char in clean_name if char not in '\t\n\r'):
            raise ValueError("Display name contains invalid characters")
        
        return clean_name if clean_name else None
    
    @staticmethod
    def validate_integer(value: Any, min_val: int = None, max_val: int = None, field_name: str = "value") -> int:
        """Validate integer input with optional range checking."""
        try:
            int_val = int(value)
        except (ValueError, TypeError):
            raise ValueError(f"{field_name} must be a valid integer")
        
        if min_val is not None and int_val < min_val:
            raise ValueError(f"{field_name} must be at least {min_val}")
        
        if max_val is not None and int_val > max_val:
            raise ValueError(f"{field_name} cannot exceed {max_val}")
        
        return int_val
    
    @staticmethod
    def validate_float(value: Any, min_val: float = None, max_val: float = None, field_name: str = "value") -> float:
        """Validate float input with optional range checking."""
        try:
            float_val = float(value)
        except (ValueError, TypeError):
            raise ValueError(f"{field_name} must be a valid number")
        
        if min_val is not None and float_val < min_val:
            raise ValueError(f"{field_name} must be at least {min_val}")
        
        if max_val is not None and float_val > max_val:
            raise ValueError(f"{field_name} cannot exceed {max_val}")
        
        return float_val
    
    @staticmethod
    def validate_boolean(value: Any) -> bool:
        """Validate boolean input."""
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            lower_val = value.lower().strip()
            if lower_val in ('true', '1', 'yes', 'on'):
                return True
            elif lower_val in ('false', '0', 'no', 'off', ''):
                return False
        
        # For form checkboxes, None/missing means False
        if value is None:
            return False
        
        raise ValueError("Invalid boolean value")
    
    @staticmethod
    def validate_search_query(query: str) -> str:
        """Validate and sanitize search query."""
        if not query:
            return ""
        
        # Strip whitespace
        clean_query = query.strip()
        
        # Limit length
        if len(clean_query) > 100:
            raise ValueError("Search query cannot exceed 100 characters")
        
        # Remove HTML tags and escape special characters
        clean_query = bleach.clean(clean_query, tags=[], attributes={}, strip=True)
        
        # Validate characters - allow alphanumeric, spaces, and common punctuation
        if not re.match(r'^[a-zA-Z0-9\s._@-]*$', clean_query):
            raise ValueError("Search query contains invalid characters")
        
        return clean_query
    
    @staticmethod
    def validate_file_upload(file) -> bool:
        """Validate file upload for security."""
        if not file or not file.filename:
            raise ValueError("No file selected")
        
        # Check file extension
        allowed_extensions = {'.json'}
        file_ext = '.' + file.filename.rsplit('.', 1)[-1].lower() if '.' in file.filename else ''
        
        if file_ext not in allowed_extensions:
            raise ValueError("Only JSON files are allowed")
        
        # Check file size (max 10MB)
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning
        
        if file_size > 10 * 1024 * 1024:  # 10MB
            raise ValueError("File size cannot exceed 10MB")
        
        return True

class SecurityHeaders:
    """Security headers management."""
    
    @staticmethod
    def apply_security_headers(response):
        """Apply comprehensive security headers to response."""
        # Content Security Policy
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
            "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
            "img-src 'self' data: https:",
            "connect-src 'self'",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "object-src 'none'"
        ]
        response.headers['Content-Security-Policy'] = '; '.join(csp_directives)
        
        # Other security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # Remove server header for security
        response.headers.pop('Server', None)
        
        return response

def validate_form_input(validation_rules: Dict[str, Dict[str, Any]]):
    """Decorator for comprehensive form input validation."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    # Validate each field according to rules
                    validated_data = {}
                    
                    for field_name, rules in validation_rules.items():
                        field_value = request.form.get(field_name)
                        field_type = rules.get('type', 'string')
                        required = rules.get('required', False)
                        
                        # Check required fields
                        if required and (field_value is None or field_value.strip() == ''):
                            raise ValueError(f"{field_name} is required")
                        
                        # Skip validation for optional empty fields
                        if not required and (field_value is None or field_value.strip() == ''):
                            validated_data[field_name] = None
                            continue
                        
                        # Validate based on type
                        if field_type == 'username':
                            validated_data[field_name] = InputValidator.validate_username(field_value)
                        elif field_type == 'display_name':
                            validated_data[field_name] = InputValidator.validate_display_name(field_value)
                        elif field_type == 'integer':
                            validated_data[field_name] = InputValidator.validate_integer(
                                field_value, 
                                rules.get('min'), 
                                rules.get('max'), 
                                field_name
                            )
                        elif field_type == 'float':
                            validated_data[field_name] = InputValidator.validate_float(
                                field_value, 
                                rules.get('min'), 
                                rules.get('max'), 
                                field_name
                            )
                        elif field_type == 'boolean':
                            validated_data[field_name] = InputValidator.validate_boolean(field_value)
                        elif field_type == 'search':
                            validated_data[field_name] = InputValidator.validate_search_query(field_value)
                        else:  # string
                            # Basic string validation
                            clean_value = field_value.strip() if field_value else ''
                            max_length = rules.get('max_length', 1000)
                            
                            if len(clean_value) > max_length:
                                raise ValueError(f"{field_name} cannot exceed {max_length} characters")
                            
                            # Sanitize HTML
                            validated_data[field_name] = bleach.clean(clean_value, tags=[], attributes={}, strip=True)
                    
                    # Add validated data to request for use in route
                    request.validated_data = validated_data
                    
                except ValueError as e:
                    logger.warning(f"Form validation error in {f.__name__}: {e}")
                    if request.is_json:
                        return jsonify({'error': 'Validation error', 'message': str(e)}), 400
                    else:
                        from flask import flash, redirect, url_for
                        flash(str(e), 'error')
                        # Redirect to settings page for credentials routes, otherwise referrer
                        if 'credentials' in request.endpoint:
                            return redirect(url_for('main.settings'))
                        else:
                            return redirect(request.referrer or url_for('main.settings'))
                
                except Exception as e:
                    logger.error(f"Unexpected validation error in {f.__name__}: {e}")
                    if request.is_json:
                        return jsonify({'error': 'Internal validation error'}), 500
                    else:
                        from flask import flash, redirect, url_for
                        flash('An error occurred while processing your request', 'error')
                        # Redirect to settings page for credentials routes, otherwise referrer
                        if 'credentials' in request.endpoint:
                            return redirect(url_for('main.settings'))
                        else:
                            return redirect(request.referrer or url_for('main.settings'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_api_input(validation_rules: Dict[str, Dict[str, Any]]):
    """Decorator for API input validation."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    # Get JSON data
                    if not request.is_json:
                        return jsonify({'error': 'Content-Type must be application/json'}), 400
                    
                    data = request.get_json()
                    if not data:
                        return jsonify({'error': 'JSON data required'}), 400
                    
                    # Validate each field according to rules
                    validated_data = {}
                    
                    for field_name, rules in validation_rules.items():
                        field_value = data.get(field_name)
                        field_type = rules.get('type', 'string')
                        required = rules.get('required', False)
                        
                        # Check required fields
                        if required and field_value is None:
                            return jsonify({'error': f'{field_name} is required'}), 400
                        
                        # Skip validation for optional empty fields
                        if not required and field_value is None:
                            validated_data[field_name] = None
                            continue
                        
                        # Validate based on type
                        if field_type == 'username':
                            validated_data[field_name] = InputValidator.validate_username(field_value)
                        elif field_type == 'display_name':
                            validated_data[field_name] = InputValidator.validate_display_name(field_value)
                        elif field_type == 'integer':
                            validated_data[field_name] = InputValidator.validate_integer(
                                field_value, 
                                rules.get('min'), 
                                rules.get('max'), 
                                field_name
                            )
                        elif field_type == 'float':
                            validated_data[field_name] = InputValidator.validate_float(
                                field_value, 
                                rules.get('min'), 
                                rules.get('max'), 
                                field_name
                            )
                        elif field_type == 'boolean':
                            validated_data[field_name] = InputValidator.validate_boolean(field_value)
                        else:  # string
                            if not isinstance(field_value, str):
                                return jsonify({'error': f'{field_name} must be a string'}), 400
                            
                            max_length = rules.get('max_length', 1000)
                            if len(field_value) > max_length:
                                return jsonify({'error': f'{field_name} cannot exceed {max_length} characters'}), 400
                            
                            # Sanitize HTML
                            validated_data[field_name] = bleach.clean(field_value, tags=[], attributes={}, strip=True)
                    
                    # Add validated data to request for use in route
                    request.validated_data = validated_data
                    
                except ValueError as e:
                    logger.warning(f"API validation error in {f.__name__}: {e}")
                    return jsonify({'error': 'Validation error', 'message': str(e)}), 400
                
                except Exception as e:
                    logger.error(f"Unexpected API validation error in {f.__name__}: {e}")
                    return jsonify({'error': 'Internal validation error'}), 500
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def sanitize_output(data: Any) -> Any:
    """Sanitize output data to prevent XSS."""
    if isinstance(data, str):
        # Escape HTML characters
        return html.escape(data)
    elif isinstance(data, dict):
        return {key: sanitize_output(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [sanitize_output(item) for item in data]
    else:
        return data

def secure_session_management():
    """Enhanced session security management."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Regenerate session ID periodically for security
            if 'session_created' not in session:
                session['session_created'] = True
                session.permanent = True
            
            # Add CSRF token to session if not present
            if 'csrf_token' not in session:
                import secrets
                session['csrf_token'] = secrets.token_hex(32)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

class SQLInjectionPrevention:
    """SQL injection prevention utilities."""
    
    @staticmethod
    def validate_sql_params(*params) -> bool:
        """Validate parameters to prevent SQL injection."""
        for param in params:
            if isinstance(param, str):
                # Check for common SQL injection patterns
                dangerous_patterns = [
                    r"('|(\\')|(;)|(\\;))",  # Single quotes and semicolons
                    r"(--)|(/\\*.*\\*/)",     # SQL comments
                    r"\\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\\b",  # SQL keywords
                ]
                
                for pattern in dangerous_patterns:
                    if re.search(pattern, param.lower()):
                        logger.warning(f"Potential SQL injection attempt detected: {param}")
                        return False
        
        return True
    
    @staticmethod
    def escape_like_pattern(pattern: str) -> str:
        """Escape LIKE pattern to prevent SQL injection."""
        # Escape special LIKE characters
        escaped = pattern.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
        return escaped

# Template filters for output sanitization
def register_template_filters(app):
    """Register template filters for secure output."""
    
    @app.template_filter('safe_html')
    def safe_html_filter(text):
        """Safely render HTML by allowing only specific tags."""
        if not text:
            return ''
        return bleach.clean(text, tags=ALLOWED_TAGS, attributes=ALLOWED_ATTRIBUTES)
    
    @app.template_filter('escape_js')
    def escape_js_filter(text):
        """Escape text for safe use in JavaScript."""
        if not text:
            return ''
        # Escape characters that could break JavaScript
        escaped = text.replace('\\', '\\\\').replace('"', '\\"').replace("'", "\\'")
        escaped = escaped.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
        return escaped
    
    @app.template_filter('truncate_safe')
    def truncate_safe_filter(text, length=100):
        """Safely truncate text without breaking HTML entities."""
        if not text or len(text) <= length:
            return text
        
        # Truncate and add ellipsis
        truncated = text[:length].rstrip()
        if truncated != text:
            truncated += '...'
        
        return truncated