"""
Unit tests for DataProcessor.

Tests the data processing functionality for storing changes and updating baselines.
"""

import pytest
from unittest.mock import Mock
from datetime import datetime

from models.data_models import (
    Follower<PERSON>hange, ProfileInfo, MonitoringConfig, 
    ChangeType, validate_username
)
from services.data_processor import DataProcessor


class TestDataProcessor:
    """Test cases for DataProcessor class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.follower_repo = Mock()
        self.change_repo = Mock()
        self.profile_repo = Mock()
        self.processor = DataProcessor(
            self.follower_repo,
            self.change_repo,
            self.profile_repo
        )
    
    def test_process_changes_success(self):
        """Test successful processing of changes."""
        # Create test data
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=102,
            following_count=51,
            is_private=False
        )
        
        current_followers = {"user1", "user2", "user3"}
        current_following = {"follow1", "follow2"}
        
        changes = [
            Follower<PERSON><PERSON><PERSON>("testuser", "user3", ChangeType.GAINED, profile_id=1),
            Follower<PERSON>hange("testuser", "follow3", ChangeType.STOPPED_FOLLOWING, profile_id=1)
        ]
        
        # Mock successful operations
        self.change_repo.store_follower_changes.return_value = None
        self.follower_repo.store_current_followers.return_value = None
        self.follower_repo.store_current_following.return_value = None
        self.profile_repo.update_profile.return_value = True
        
        result = self.processor.process_changes(
            profile, profile_info, current_followers, current_following, changes
        )
        
        assert result['success'] is True
        assert result['changes_stored'] == 2
        assert result['followers_updated'] == 3
        assert result['following_updated'] == 2
        assert result['profile_updated'] is True
        
        # Verify repository calls
        self.change_repo.store_follower_changes.assert_called_once_with(changes)
        self.follower_repo.store_current_followers.assert_called_once_with(1, current_followers)
        self.follower_repo.store_current_following.assert_called_once_with(1, current_following)
        self.profile_repo.update_profile.assert_called_once()
        
        # Verify profile was updated
        updated_profile = self.profile_repo.update_profile.call_args[0][0]
        assert updated_profile.display_name == "Test User"
        assert updated_profile.is_private is False
        assert updated_profile.last_scan is not None
    
    def test_process_changes_no_profile_id(self):
        """Test processing when profile has no ID."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=None,  # No ID
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        
        result = self.processor.process_changes(
            profile, profile_info, set(), set(), []
        )
        
        assert result['success'] is False
        assert 'Profile ID is required' in result['error']
        assert result['changes_stored'] == 0
    
    def test_process_changes_no_changes(self):
        """Test processing when there are no changes."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        current_followers = {"user1", "user2"}
        current_following = {"follow1"}
        
        # Mock successful operations
        self.follower_repo.store_current_followers.return_value = None
        self.follower_repo.store_current_following.return_value = None
        self.profile_repo.update_profile.return_value = True
        
        result = self.processor.process_changes(
            profile, profile_info, current_followers, current_following, []
        )
        
        assert result['success'] is True
        assert result['changes_stored'] == 0
        assert result['followers_updated'] == 2
        assert result['following_updated'] == 1
        
        # Verify change storage was not called
        self.change_repo.store_follower_changes.assert_not_called()
        
        # But baseline updates should still happen
        self.follower_repo.store_current_followers.assert_called_once()
        self.follower_repo.store_current_following.assert_called_once()
    
    def test_process_changes_store_changes_failure(self):
        """Test processing when storing changes fails."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1)
        ]
        
        # Mock change storage failure
        self.change_repo.store_follower_changes.side_effect = Exception("Database error")
        
        result = self.processor.process_changes(
            profile, profile_info, set(), set(), changes
        )
        
        assert result['success'] is False
        assert 'Failed to store changes' in result['error']
        assert result['changes_stored'] == 0
    
    def test_process_changes_update_baseline_failure(self):
        """Test processing when updating baseline fails."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        current_followers = {"user1"}
        
        # Mock successful change storage but baseline update failure
        self.change_repo.store_follower_changes.return_value = None
        self.follower_repo.store_current_followers.side_effect = Exception("Database error")
        
        result = self.processor.process_changes(
            profile, profile_info, current_followers, set(), []
        )
        
        assert result['success'] is False
        assert 'Failed to update baselines' in result['error']
    
    def test_store_initial_baseline_success(self):
        """Test storing initial baseline data."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        followers = {"user1", "user2", "user3"}
        following = {"follow1", "follow2"}
        
        # Mock successful operations
        self.follower_repo.store_current_followers.return_value = None
        self.follower_repo.store_current_following.return_value = None
        self.profile_repo.update_profile.return_value = True
        
        result = self.processor.store_initial_baseline(profile, followers, following)
        
        assert result['success'] is True
        assert result['followers_stored'] == 3
        assert result['following_stored'] == 2
        assert 'Initial baseline stored successfully' in result['message']
        
        # Verify repository calls
        self.follower_repo.store_current_followers.assert_called_once_with(1, followers)
        self.follower_repo.store_current_following.assert_called_once_with(1, following)
        self.profile_repo.update_profile.assert_called_once()
        
        # Verify profile last_scan was updated
        updated_profile = self.profile_repo.update_profile.call_args[0][0]
        assert updated_profile.last_scan is not None
    
    def test_store_initial_baseline_no_profile_id(self):
        """Test storing baseline when profile has no ID."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=None,
            enabled=True
        )
        
        result = self.processor.store_initial_baseline(profile, set(), set())
        
        assert result['success'] is False
        assert 'Profile ID is required' in result['error']
    
    def test_update_baseline_only_success(self):
        """Test updating only baseline data."""
        followers = {"user1", "user2"}
        following = {"follow1"}
        
        # Mock successful operations
        self.follower_repo.store_current_followers.return_value = None
        self.follower_repo.store_current_following.return_value = None
        
        result = self.processor.update_baseline_only(1, followers, following)
        
        assert result['success'] is True
        assert result['followers_updated'] == 2
        assert result['following_updated'] == 1
        
        # Verify repository calls
        self.follower_repo.store_current_followers.assert_called_once_with(1, followers)
        self.follower_repo.store_current_following.assert_called_once_with(1, following)
    
    def test_update_baseline_only_failure(self):
        """Test updating baseline when operation fails."""
        # Mock failure
        self.follower_repo.store_current_followers.side_effect = Exception("Database error")
        
        result = self.processor.update_baseline_only(1, set(), set())
        
        assert result['success'] is False
        assert 'Database error' in result['error']
        assert result['followers_updated'] == 0
        assert result['following_updated'] == 0
    
    def test_validate_data_integrity_success(self):
        """Test successful data integrity validation."""
        profile_id = 1
        
        # Mock repository data
        current_followers = {"user1", "user2", "user3"}
        current_following = {"follow1", "follow2"}
        recent_changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1)
        ]
        
        self.follower_repo.get_current_followers.return_value = current_followers
        self.follower_repo.get_current_following.return_value = current_following
        self.change_repo.get_recent_changes.return_value = recent_changes
        
        result = self.processor.validate_data_integrity(profile_id)
        
        assert result['validation_passed'] is True
        assert result['profile_id'] == profile_id
        assert result['current_followers_count'] == 3
        assert result['current_following_count'] == 2
        assert result['recent_changes_count'] == 1
        assert result['issues'] == []
    
    def test_validate_data_integrity_with_issues(self):
        """Test data integrity validation with issues found."""
        profile_id = 1
        
        # Mock data with issues
        current_followers = {"user1", "user2", ""}  # Empty username
        current_following = {"follow1", "invalid@user"}  # Invalid username
        recent_changes = []
        
        self.follower_repo.get_current_followers.return_value = current_followers
        self.follower_repo.get_current_following.return_value = current_following
        self.change_repo.get_recent_changes.return_value = recent_changes
        
        result = self.processor.validate_data_integrity(profile_id)
        
        assert result['validation_passed'] is False
        assert len(result['issues']) > 0
        assert any('Invalid follower usernames' in issue for issue in result['issues'])
        assert any('Invalid following usernames' in issue for issue in result['issues'])
    
    def test_cleanup_invalid_data_success(self):
        """Test cleaning up invalid data."""
        profile_id = 1
        
        # Mock data with invalid usernames
        current_followers = {"user1", "user2", "", "invalid@user"}
        current_following = {"follow1", ""}
        
        self.follower_repo.get_current_followers.return_value = current_followers
        self.follower_repo.get_current_following.return_value = current_following
        
        # Mock successful storage
        self.follower_repo.store_current_followers.return_value = None
        self.follower_repo.store_current_following.return_value = None
        
        result = self.processor.cleanup_invalid_data(profile_id)
        
        assert result['profile_id'] == profile_id
        assert result['followers_cleaned'] == 2  # Empty and invalid@user
        assert result['following_cleaned'] == 1  # Empty
        assert result['followers_remaining'] == 2  # user1, user2
        assert result['following_remaining'] == 1  # follow1
        
        # Verify cleaned data was stored
        self.follower_repo.store_current_followers.assert_called_once()
        self.follower_repo.store_current_following.assert_called_once()
        
        # Check that cleaned data was passed
        stored_followers = self.follower_repo.store_current_followers.call_args[0][1]
        stored_following = self.follower_repo.store_current_following.call_args[0][1]
        
        assert "" not in stored_followers
        assert "invalid@user" not in stored_followers
        assert "" not in stored_following
    
    def test_cleanup_invalid_data_no_cleanup_needed(self):
        """Test cleanup when no invalid data exists."""
        profile_id = 1
        
        # Mock clean data
        current_followers = {"user1", "user2"}
        current_following = {"follow1"}
        
        self.follower_repo.get_current_followers.return_value = current_followers
        self.follower_repo.get_current_following.return_value = current_following
        
        result = self.processor.cleanup_invalid_data(profile_id)
        
        assert result['followers_cleaned'] == 0
        assert result['following_cleaned'] == 0
        
        # Verify no storage operations were called since no cleanup was needed
        self.follower_repo.store_current_followers.assert_not_called()
        self.follower_repo.store_current_following.assert_not_called()
    
    def test_processing_statistics_tracking(self):
        """Test that processing statistics are properly tracked."""
        # Reset statistics
        self.processor.reset_statistics()
        
        initial_stats = self.processor.get_processing_statistics()
        assert initial_stats['total_processing_runs'] == 0
        assert initial_stats['successful_runs'] == 0
        assert initial_stats['success_rate'] == 0.0
        
        # Perform successful processing
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1)
        ]
        
        # Mock successful operations
        self.change_repo.store_follower_changes.return_value = None
        self.follower_repo.store_current_followers.return_value = None
        self.follower_repo.store_current_following.return_value = None
        self.profile_repo.update_profile.return_value = True
        
        result = self.processor.process_changes(
            profile, profile_info, set(), set(), changes
        )
        
        assert result['success'] is True
        
        # Check updated statistics
        stats = self.processor.get_processing_statistics()
        assert stats['total_processing_runs'] == 1
        assert stats['successful_runs'] == 1
        assert stats['failed_runs'] == 0
        assert stats['changes_processed'] == 1
        assert stats['profiles_updated'] == 1
        assert stats['success_rate'] == 1.0
        assert stats['average_changes_per_run'] == 1.0
    
    def test_processing_statistics_failure_tracking(self):
        """Test that processing failures are tracked in statistics."""
        # Reset statistics
        self.processor.reset_statistics()
        
        # Perform failed processing - use an exception that will be caught
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        
        # Mock an exception during processing
        self.change_repo.store_follower_changes.side_effect = Exception("Database error")
        
        result = self.processor.process_changes(
            profile, profile_info, set(), set(), [
                FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1)
            ]
        )
        
        assert result['success'] is False
        
        # Check statistics - early returns may not update stats
        stats = self.processor.get_processing_statistics()
        # The failure should be tracked if it goes through the exception handler
        assert stats['success_rate'] <= 1.0
    
    def test_create_processing_summary(self):
        """Test creation of processing summary."""
        changes = [
            FollowerChange("testuser", "user1", ChangeType.GAINED, profile_id=1),
            FollowerChange("testuser", "user2", ChangeType.LOST, profile_id=1),
            FollowerChange("testuser", "follow1", ChangeType.STARTED_FOLLOWING, profile_id=1),
            FollowerChange("testuser", "follow2", ChangeType.STOPPED_FOLLOWING, profile_id=1),
        ]
        
        summary = self.processor._create_processing_summary(changes)
        
        assert summary['total_changes'] == 4
        assert summary['follower_changes'] == 2
        assert summary['following_changes'] == 2
        
        expected_breakdown = {
            'gained': 1,
            'lost': 1,
            'started_following': 1,
            'stopped_following': 1
        }
        assert summary['change_breakdown'] == expected_breakdown
    
    def test_create_processing_summary_empty(self):
        """Test processing summary with no changes."""
        summary = self.processor._create_processing_summary([])
        
        assert summary['total_changes'] == 0
        assert summary['follower_changes'] == 0
        assert summary['following_changes'] == 0
        assert summary['change_breakdown'] == {}


class TestDataProcessorEdgeCases:
    """Test edge cases and error conditions for DataProcessor."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.follower_repo = Mock()
        self.change_repo = Mock()
        self.profile_repo = Mock()
        self.processor = DataProcessor(
            self.follower_repo,
            self.change_repo,
            self.profile_repo
        )
    
    def test_process_changes_exception_handling(self):
        """Test that exceptions during processing are properly handled."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        
        # Mock unexpected exception
        self.follower_repo.store_current_followers.side_effect = Exception("Unexpected error")
        
        result = self.processor.process_changes(
            profile, profile_info, set(), set(), []
        )
        
        assert result['success'] is False
        assert "Unexpected error" in result['error']
        # Duration may not be included in early failure returns
    
    def test_validate_data_integrity_exception(self):
        """Test data integrity validation when an exception occurs."""
        # Mock exception during validation
        self.follower_repo.get_current_followers.side_effect = Exception("Database error")
        
        result = self.processor.validate_data_integrity(1)
        
        assert result['validation_passed'] is False
        assert 'Database error' in result['error']
        assert 'Validation error' in result['issues'][0]
    
    def test_cleanup_invalid_data_exception(self):
        """Test cleanup when an exception occurs."""
        # Mock exception during cleanup
        self.follower_repo.get_current_followers.side_effect = Exception("Database error")
        
        result = self.processor.cleanup_invalid_data(1)
        
        assert 'error' in result
        assert 'Database error' in result['error']
        assert result['followers_cleaned'] == 0
        assert result['following_cleaned'] == 0
    
    def test_process_changes_with_large_datasets(self):
        """Test processing with large numbers of changes."""
        profile = MonitoringConfig(
            profile_username="testuser",
            profile_id=1,
            enabled=True
        )
        
        profile_info = ProfileInfo(username="testuser")
        
        # Create large dataset
        large_followers = {f"user{i}" for i in range(1000)}
        large_following = {f"follow{i}" for i in range(500)}
        large_changes = [
            FollowerChange("testuser", f"user{i}", ChangeType.GAINED, profile_id=1)
            for i in range(100)
        ]
        
        # Mock successful operations
        self.change_repo.store_follower_changes.return_value = None
        self.follower_repo.store_current_followers.return_value = None
        self.follower_repo.store_current_following.return_value = None
        self.profile_repo.update_profile.return_value = True
        
        result = self.processor.process_changes(
            profile, profile_info, large_followers, large_following, large_changes
        )
        
        assert result['success'] is True
        assert result['changes_stored'] == 100
        assert result['followers_updated'] == 1000
        assert result['following_updated'] == 500