#!/usr/bin/env python3
"""Demo script showcasing the repository functionality."""

import os
import tempfile
from datetime import datetime, timedelta
from models.data_models import MonitoringConfig, FollowerChange, ChangeType
from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, FollowerRepository, ChangeRepository, DataRetentionManager
)


def main():
    """Demonstrate repository functionality."""
    print("=== Instagram Follower Monitor - Repository Demo ===\n")
    
    # Create temporary database for demo
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_file.close()
    
    try:
        # Initialize database
        print("1. Initializing database...")
        db_manager = DatabaseManager(temp_file.name)
        db_manager.migrate_schema()
        
        # Patch global db_manager for repositories
        import database.repositories
        database.repositories.db_manager = db_manager
        
        # Create repository instances
        profile_repo = ProfileRepository()
        follower_repo = FollowerRepository()
        change_repo = ChangeRepository()
        retention_manager = DataRetentionManager(retention_days=30)
        
        print("✓ Database initialized successfully\n")
        
        # Demo 1: Profile Management
        print("2. Creating profiles...")
        profiles = [
            MonitoringConfig(profile_username="john_doe", display_name="John Doe", enabled=True),
            MonitoringConfig(profile_username="jane_smith", display_name="Jane Smith", enabled=True),
            MonitoringConfig(profile_username="mike_wilson", display_name="Mike Wilson", enabled=False)
        ]
        
        profile_ids = {}
        for config in profiles:
            profile_id = profile_repo.create_profile(config)
            profile_ids[config.profile_username] = profile_id
            print(f"✓ Created profile: {config.profile_username} (ID: {profile_id})")
        
        print(f"\n✓ Created {len(profiles)} profiles\n")
        
        # Demo 2: Storing Current Data
        print("3. Storing current followers and following...")
        
        # Store data for john_doe
        john_id = profile_ids["john_doe"]
        john_followers = {"alice", "bob", "charlie", "diana", "eve"}
        john_following = {"tech_news", "photography", "travel_blog"}
        
        follower_repo.store_current_followers(john_id, john_followers)
        follower_repo.store_current_following(john_id, john_following)
        
        print(f"✓ Stored {len(john_followers)} followers and {len(john_following)} following for john_doe")
        
        # Store data for jane_smith
        jane_id = profile_ids["jane_smith"]
        jane_followers = {"frank", "grace", "henry"}
        jane_following = {"cooking", "fitness", "books"}
        
        follower_repo.store_current_followers(jane_id, jane_followers)
        follower_repo.store_current_following(jane_id, jane_following)
        
        print(f"✓ Stored {len(jane_followers)} followers and {len(jane_following)} following for jane_smith\n")
        
        # Demo 3: Simulating Changes
        print("4. Simulating follower changes...")
        
        # Simulate changes for john_doe
        changes = [
            # New followers
            FollowerChange("john_doe", "new_follower_1", ChangeType.GAINED, profile_id=john_id),
            FollowerChange("john_doe", "new_follower_2", ChangeType.GAINED, profile_id=john_id),
            # Lost follower
            FollowerChange("john_doe", "eve", ChangeType.LOST, profile_id=john_id),
            # Following changes
            FollowerChange("john_doe", "new_account", ChangeType.STARTED_FOLLOWING, profile_id=john_id),
            FollowerChange("john_doe", "travel_blog", ChangeType.STOPPED_FOLLOWING, profile_id=john_id),
        ]
        
        # Add timestamps to make them more realistic
        now = datetime.now()
        for i, change in enumerate(changes):
            change.timestamp = now - timedelta(hours=i)
        
        change_repo.store_follower_changes(changes)
        print(f"✓ Stored {len(changes)} changes for john_doe\n")
        
        # Demo 4: Querying Data
        print("5. Querying repository data...")
        
        # Get all profiles
        all_profiles = profile_repo.get_all_profiles()
        print(f"✓ Total profiles: {len(all_profiles)}")
        
        # Get enabled profiles only
        enabled_profiles = profile_repo.get_enabled_profiles()
        print(f"✓ Enabled profiles: {len(enabled_profiles)}")
        
        # Get current data
        current_followers = follower_repo.get_current_followers(john_id)
        current_following = follower_repo.get_current_following(john_id)
        print(f"✓ John's current followers: {len(current_followers)}")
        print(f"✓ John's current following: {len(current_following)}")
        
        # Get recent changes
        recent_changes = change_repo.get_recent_changes(profile_id=john_id, limit=10)
        print(f"✓ Recent changes for john_doe: {len(recent_changes)}")
        
        for change in recent_changes[:3]:  # Show first 3
            print(f"  - {change.change_type.value}: {change.affected_username} ({change.timestamp.strftime('%H:%M')})")
        
        print()
        
        # Demo 5: Statistics and Analytics
        print("6. Generating statistics...")
        
        # Profile statistics
        john_stats = profile_repo.get_profile_stats(john_id)
        print("✓ John's profile statistics:")
        for key, value in john_stats.items():
            print(f"  - {key}: {value}")
        
        # Change statistics
        change_stats = change_repo.get_change_statistics(profile_id=john_id, days=7)
        print("\n✓ Change statistics (last 7 days):")
        for key, value in change_stats.items():
            print(f"  - {key}: {value}")
        
        print()
        
        # Demo 6: Search Functionality
        print("7. Testing search functionality...")
        
        # Search for specific changes
        search_results = change_repo.search_changes("new", profile_id=john_id)
        print(f"✓ Search results for 'new': {len(search_results)} matches")
        
        for result in search_results:
            print(f"  - {result.change_type.value}: {result.affected_username}")
        
        print()
        
        # Demo 7: Bulk Operations
        print("8. Testing bulk operations...")
        
        # Bulk update last scan times
        bulk_updates = [
            (john_id, datetime.now()),
            (jane_id, datetime.now() - timedelta(hours=1))
        ]
        
        updated_count = profile_repo.bulk_update_last_scan(bulk_updates)
        print(f"✓ Bulk updated last_scan for {updated_count} profiles")
        
        # Bulk store followers for multiple profiles
        bulk_followers = {
            john_id: {"alice", "bob", "charlie", "new_follower_1", "new_follower_2"},
            jane_id: {"frank", "grace", "henry", "new_jane_follower"}
        }
        
        follower_repo.bulk_store_followers(bulk_followers)
        print("✓ Bulk stored followers for multiple profiles")
        
        print()
        
        # Demo 8: Data Retention
        print("9. Testing data retention...")
        
        # Get data size stats
        size_stats = retention_manager.get_data_size_stats()
        print("✓ Database size statistics:")
        for key, value in size_stats.items():
            if 'count' in key:
                print(f"  - {key}: {value}")
        
        # Create some old data for cleanup demo
        old_changes = [
            FollowerChange("john_doe", "old_user", ChangeType.GAINED, 
                          timestamp=datetime.now() - timedelta(days=35), profile_id=john_id)
        ]
        change_repo.store_follower_changes(old_changes)
        
        # Run cleanup
        cleanup_stats = retention_manager.cleanup_old_data()
        print(f"\n✓ Data cleanup completed:")
        for key, value in cleanup_stats.items():
            if value > 0:
                print(f"  - {key}: {value}")
        
        print()
        
        # Demo 9: Database Optimization
        print("10. Running database optimization...")
        
        optimization_stats = retention_manager.optimize_database()
        print("✓ Database optimization completed:")
        print(f"  - Database size: {optimization_stats['database_size_bytes']:,} bytes")
        print(f"  - Total rows: {sum(v for k, v in optimization_stats.items() if k.endswith('_rows'))}")
        
        print("\n=== Demo completed successfully! ===")
        
    finally:
        # Cleanup
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
            print(f"\n✓ Cleaned up temporary database: {temp_file.name}")


if __name__ == "__main__":
    main()