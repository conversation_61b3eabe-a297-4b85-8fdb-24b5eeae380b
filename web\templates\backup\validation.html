{% extends "base.html" %}

{% block title %}Database Validation - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle"></i> Database Integrity Validation
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Overall Status -->
                    <div class="alert {% if validation_result.valid %}alert-success{% else %}alert-danger{% endif %}">
                        <h5 class="mb-2">
                            <i class="fas {% if validation_result.valid %}fa-check-circle{% else %}fa-exclamation-triangle{% endif %}"></i>
                            Overall Status: {% if validation_result.valid %}VALID{% else %}INVALID{% endif %}
                        </h5>
                        {% if validation_result.valid %}
                        <p class="mb-0">Database integrity validation passed successfully. No issues detected.</p>
                        {% else %}
                        <p class="mb-0">Database integrity validation failed. Please review the errors below and consider restoring from a backup.</p>
                        {% endif %}
                    </div>

                    <!-- Checks Performed -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><i class="fas fa-list-check"></i> Checks Performed</h6>
                            {% if validation_result.get('checks_performed') %}
                            <ul class="list-group">
                                {% for check in validation_result.checks_performed %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ check.replace('_', ' ').title() }}
                                    <span class="badge bg-success rounded-pill">
                                        <i class="fas fa-check"></i>
                                    </span>
                                </li>
                                {% endfor %}
                            </ul>
                            {% else %}
                            <p class="text-muted">No checks were performed.</p>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-bar"></i> Database Statistics</h6>
                            {% if validation_result.get('statistics') %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    {% for key, value in validation_result.statistics.items() %}
                                    <tr>
                                        <td>{{ key.replace('_', ' ').title() }}</td>
                                        <td class="text-end">
                                            {% if key.endswith('_size_bytes') %}
                                                {{ (value / 1024 / 1024) | round(2) }} MB
                                            {% else %}
                                                {{ value }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted">No statistics available.</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Errors -->
                    {% if validation_result.get('errors') %}
                    <div class="mb-4">
                        <h6 class="text-danger"><i class="fas fa-exclamation-circle"></i> Errors Found</h6>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                {% for error in validation_result.errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> Recommended Actions</h6>
                            <ul class="mb-0">
                                <li>Create a backup of the current database immediately</li>
                                <li>Consider restoring from a known good backup</li>
                                <li>Contact support if errors persist</li>
                                <li>Check system logs for additional information</li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Warnings -->
                    {% if validation_result.get('warnings') %}
                    <div class="mb-4">
                        <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> Warnings</h6>
                        <div class="alert alert-warning">
                            <ul class="mb-0">
                                {% for warning in validation_result.warnings %}
                                <li>{{ warning }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> About Warnings</h6>
                            <p class="mb-0">
                                Warnings indicate potential issues that don't necessarily compromise data integrity 
                                but should be monitored. Consider cleaning up orphaned records or investigating 
                                unusual patterns.
                            </p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Actions -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('backup.backup_dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        
                        {% if not validation_result.valid %}
                        <a href="{{ url_for('backup.create_backup') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Emergency Backup
                        </a>
                        <a href="{{ url_for('backup.restore_backup') }}" class="btn btn-warning">
                            <i class="fas fa-undo"></i> Restore from Backup
                        </a>
                        {% else %}
                        <button type="button" class="btn btn-success" onclick="location.reload()">
                            <i class="fas fa-sync"></i> Re-run Validation
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Validation Details -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Validation Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-database"></i> Database Checks</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> File existence verification</li>
                                <li><i class="fas fa-check text-success"></i> SQLite integrity check</li>
                                <li><i class="fas fa-check text-success"></i> Foreign key constraints</li>
                                <li><i class="fas fa-check text-success"></i> Data consistency validation</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-search"></i> Data Validation</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Orphaned record detection</li>
                                <li><i class="fas fa-check text-success"></i> Duplicate profile check</li>
                                <li><i class="fas fa-check text-success"></i> Invalid timestamp detection</li>
                                <li><i class="fas fa-check text-success"></i> Statistics collection</li>
                            </ul>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-muted">
                        <small>
                            <i class="fas fa-clock"></i> 
                            Validation completed at {{ moment().format('YYYY-MM-DD HH:mm:ss') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh validation every 5 minutes if there are errors
{% if not validation_result.valid %}
setTimeout(function() {
    if (confirm('Database validation failed. Would you like to re-run the validation check?')) {
        location.reload();
    }
}, 300000); // 5 minutes
{% endif %}
</script>
{% endblock %}