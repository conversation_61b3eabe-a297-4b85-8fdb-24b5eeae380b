{% extends "base.html" %}

{% block title %}Access Forbidden - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-lock me-2"></i>
                        Access Forbidden (403)
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-ban fa-5x text-warning"></i>
                    </div>
                    
                    <h5 class="card-title">Access Denied</h5>
                    <p class="card-text text-muted">
                        You don't have permission to access this resource. This could be due to insufficient privileges or authentication requirements.
                    </p>
                    
                    <div class="mt-4">
                        <h6>What you can do:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-key me-2"></i>Check if you're properly authenticated</li>
                            <li><i class="fas fa-user-shield me-2"></i>Verify you have the required permissions</li>
                            <li><i class="fas fa-home me-2"></i>Return to the dashboard</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>
                            Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}