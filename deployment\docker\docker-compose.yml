version: '3.8'

services:
  instagram-monitor:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile
    container_name: instagram-monitor
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///data/instagram_monitor.db
      - SECRET_KEY=${SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - LOG_LEVEL=INFO
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - instagram-monitor-network

  nginx:
    image: nginx:alpine
    container_name: instagram-monitor-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ../web/static:/var/www/static:ro
    depends_on:
      - instagram-monitor
    networks:
      - instagram-monitor-network

networks:
  instagram-monitor-network:
    driver: bridge

volumes:
  data:
  logs:
  backups: