{% extends "base.html" %}

{% block title %}Edit Profile - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-pencil"></i> Edit Profile: @{{ profile.profile_username }}
                </h4>
            </div>
            
            <div class="card-body">
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST" id="editProfileForm">
                    {{ csrf_token() }}
                    <div class="mb-3">
                        <label for="username" class="form-label">Instagram Username</label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="text" class="form-control" id="username" 
                                   value="{{ profile.profile_username }}" disabled>
                        </div>
                        <div class="form-text">
                            Username cannot be changed after creation
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="display_name" class="form-label">Display Name</label>
                        <input type="text" class="form-control" id="display_name" name="display_name" 
                               value="{{ profile.display_name or '' }}" placeholder="Optional display name" maxlength="100">
                        <div class="form-text">
                            Optional friendly name for this profile
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="interval_hours" class="form-label">Monitoring Interval</label>
                        <select class="form-select" id="interval_hours" name="interval_hours">
                            <option value="1" {{ 'selected' if profile.interval_hours == 1 }}>Every hour</option>
                            <option value="2" {{ 'selected' if profile.interval_hours == 2 }}>Every 2 hours (recommended)</option>
                            <option value="4" {{ 'selected' if profile.interval_hours == 4 }}>Every 4 hours</option>
                            <option value="6" {{ 'selected' if profile.interval_hours == 6 }}>Every 6 hours</option>
                            <option value="12" {{ 'selected' if profile.interval_hours == 12 }}>Every 12 hours</option>
                            <option value="24" {{ 'selected' if profile.interval_hours == 24 }}>Daily</option>
                        </select>
                        <div class="form-text">
                            How often to check for follower changes
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_private" name="is_private"
                                       {{ 'checked' if profile.is_private }}>
                                <label class="form-check-label" for="is_private">
                                    Private Profile
                                </label>
                                <div class="form-text">
                                    Check if this is a private Instagram account
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enabled" name="enabled"
                                       {{ 'checked' if profile.enabled }}>
                                <label class="form-check-label" for="enabled">
                                    Enable Monitoring
                                </label>
                                <div class="form-text">
                                    Enable or disable monitoring for this profile
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if profile.last_scan %}
                        <div class="alert alert-info">
                            <i class="bi bi-clock"></i>
                            <strong>Last Scan:</strong> {{ profile.last_scan.strftime('%Y-%m-%d %H:%M:%S') }}
                            ({{ profile.last_scan | timeago }})
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Never Scanned:</strong> This profile has not been scanned yet.
                        </div>
                    {% endif %}
                    
                    {% if profile.created_at %}
                        <div class="small text-muted mb-3">
                            Profile added: {{ profile.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.profiles') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Profiles
                        </a>
                        <div>
                            <a href="{{ url_for('main.profile_detail', username=profile.profile_username) }}" 
                               class="btn btn-outline-primary me-2">
                                <i class="bi bi-eye"></i> View Details
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Save Changes
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Danger Zone -->
        <div class="card mt-4 border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i> Danger Zone
                </h5>
            </div>
            <div class="card-body">
                <p class="text-danger">
                    <strong>Delete Profile:</strong> This will permanently delete all follower data and 
                    change history for this profile. This action cannot be undone.
                </p>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="bi bi-trash"></i> Delete Profile
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete profile <strong>@{{ profile.profile_username }}</strong>?</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    This will permanently delete all follower data and change history for this profile.
                </p>
                <p>Type <strong>{{ profile.profile_username }}</strong> to confirm:</p>
                <input type="text" class="form-control" id="confirmUsername" placeholder="Enter username to confirm">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('main.delete_profile', username=profile.profile_username) }}" style="display: inline;">
                    {{ csrf_token() }}
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn" disabled>
                        <i class="bi bi-trash"></i> Delete Profile
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Enable delete button only when username is correctly typed
document.getElementById('confirmUsername').addEventListener('input', function(e) {
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    const expectedUsername = '{{ profile.profile_username }}';
    
    if (e.target.value === expectedUsername) {
        confirmBtn.disabled = false;
    } else {
        confirmBtn.disabled = true;
    }
});

document.getElementById('editProfileForm').addEventListener('submit', function(e) {
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving Changes...';
    submitBtn.disabled = true;
    
    // Re-enable button after a delay in case of server error
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const form = document.getElementById('editProfileForm');
    form.insertBefore(alertDiv, form.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}