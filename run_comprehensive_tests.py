#!/usr/bin/env python3
"""
Comprehensive test runner for Instagram Follower Monitor.

This script runs the complete test suite including unit tests, integration tests,
mock tests, end-to-end tests, and performance tests with proper reporting.
"""

import sys
import os
import subprocess
import argparse
import time
from pathlib import Path


def run_command(command, description):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=False
        )
        
        duration = time.time() - start_time
        
        print(f"Exit code: {result.returncode}")
        print(f"Duration: {duration:.2f} seconds")
        
        if result.stdout:
            print(f"\nSTDOUT:\n{result.stdout}")
        
        if result.stderr:
            print(f"\nSTDERR:\n{result.stderr}")
        
        return result.returncode == 0, result
    
    except Exception as e:
        print(f"Error running command: {e}")
        return False, None


def check_dependencies():
    """Check if required dependencies are installed."""
    print("Checking dependencies...")
    
    required_packages = [
        'pytest',
        'pytest-cov',
        'pytest-mock',
        'responses',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("All dependencies are installed.")
    return True


def run_unit_tests(verbose=False, coverage=False):
    """Run unit tests."""
    command = ['python', '-m', 'pytest', 'tests/test_comprehensive_unit.py', '-v']
    
    if coverage:
        command.extend(['--cov=services', '--cov=models', '--cov=database'])
        command.extend(['--cov-report=term-missing', '--cov-report=html:htmlcov/unit'])
    
    if verbose:
        command.append('-s')
    
    # Add markers for unit tests
    command.extend(['-m', 'not slow'])
    
    return run_command(command, "Unit Tests")


def run_integration_tests(verbose=False, coverage=False):
    """Run integration tests."""
    command = ['python', '-m', 'pytest', 'tests/test_integration_comprehensive.py', '-v']
    
    if coverage:
        command.extend(['--cov=database', '--cov=web'])
        command.extend(['--cov-report=term-missing', '--cov-report=html:htmlcov/integration'])
    
    if verbose:
        command.append('-s')
    
    return run_command(command, "Integration Tests")


def run_mock_tests(verbose=False):
    """Run mock tests for Instagram API interactions."""
    command = ['python', '-m', 'pytest', 'tests/test_instagram_mocks.py', '-v']
    
    if verbose:
        command.append('-s')
    
    return run_command(command, "Instagram API Mock Tests")


def run_end_to_end_tests(verbose=False):
    """Run end-to-end tests."""
    command = ['python', '-m', 'pytest', 'tests/test_end_to_end.py', '-v']
    
    if verbose:
        command.append('-s')
    
    return run_command(command, "End-to-End Tests")


def run_performance_tests(verbose=False, include_slow=False):
    """Run performance tests."""
    command = ['python', '-m', 'pytest', 'tests/test_performance.py', '-v']
    
    if verbose:
        command.append('-s')
    
    if not include_slow:
        command.extend(['-m', 'not slow'])
    
    return run_command(command, "Performance Tests")


def run_existing_tests(verbose=False):
    """Run existing test files."""
    existing_test_files = [
        'tests/test_authentication.py',
        'tests/test_backup_manager.py',
        'tests/test_change_detector.py',
        'tests/test_data_models.py',
        'tests/test_data_processor.py',
        'tests/test_database.py',
        'tests/test_error_handling.py',
        'tests/test_instagram_client.py',
        'tests/test_integration_auth_client.py',
        'tests/test_monitoring_service.py',
        'tests/test_profile_scanner.py',
        'tests/test_repository_integration.py',
        'tests/test_scheduler.py',
        'tests/test_security.py'
    ]
    
    # Filter to only existing files
    existing_files = [f for f in existing_test_files if os.path.exists(f)]
    
    if not existing_files:
        print("No existing test files found.")
        return True, None
    
    command = ['python', '-m', 'pytest'] + existing_files + ['-v']
    
    if verbose:
        command.append('-s')
    
    # Skip slow tests by default
    command.extend(['-m', 'not slow'])
    
    return run_command(command, "Existing Tests")


def run_all_tests(verbose=False, coverage=False, include_slow=False):
    """Run all tests in sequence."""
    print("Running comprehensive test suite...")
    
    test_results = []
    
    # Run each test category
    test_categories = [
        ("Unit Tests", lambda: run_unit_tests(verbose, coverage)),
        ("Integration Tests", lambda: run_integration_tests(verbose, coverage)),
        ("Mock Tests", lambda: run_mock_tests(verbose)),
        ("End-to-End Tests", lambda: run_end_to_end_tests(verbose)),
        ("Performance Tests", lambda: run_performance_tests(verbose, include_slow)),
        ("Existing Tests", lambda: run_existing_tests(verbose))
    ]
    
    for category_name, test_func in test_categories:
        success, result = test_func()
        test_results.append((category_name, success, result))
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for _, success, _ in test_results if success)
    
    for category, success, result in test_results:
        status = "PASSED" if success else "FAILED"
        print(f"{category:<25} {status}")
    
    print(f"\nOverall: {passed_tests}/{total_tests} test categories passed")
    
    if coverage:
        print("\nCoverage reports generated in htmlcov/ directory")
    
    return passed_tests == total_tests


def run_specific_test_file(test_file, verbose=False, coverage=False):
    """Run a specific test file."""
    if not os.path.exists(test_file):
        print(f"Test file not found: {test_file}")
        return False, None
    
    command = ['python', '-m', 'pytest', test_file, '-v']
    
    if coverage:
        command.extend(['--cov=.', '--cov-report=term-missing'])
    
    if verbose:
        command.append('-s')
    
    return run_command(command, f"Specific Test File: {test_file}")


def run_tests_by_marker(marker, verbose=False):
    """Run tests by pytest marker."""
    command = ['python', '-m', 'pytest', '-v', '-m', marker]
    
    if verbose:
        command.append('-s')
    
    return run_command(command, f"Tests with marker: {marker}")


def generate_test_report():
    """Generate a comprehensive test report."""
    print("Generating comprehensive test report...")
    
    command = [
        'python', '-m', 'pytest',
        '--html=test_report.html',
        '--self-contained-html',
        '--cov=.',
        '--cov-report=html:htmlcov/full',
        '--cov-report=term',
        '-v'
    ]
    
    return run_command(command, "Comprehensive Test Report Generation")


def main():
    """Main function to run tests based on command line arguments."""
    parser = argparse.ArgumentParser(
        description="Comprehensive test runner for Instagram Follower Monitor"
    )
    
    parser.add_argument(
        '--type', 
        choices=['unit', 'integration', 'mock', 'e2e', 'performance', 'existing', 'all'],
        default='all',
        help='Type of tests to run'
    )
    
    parser.add_argument(
        '--file',
        help='Run specific test file'
    )
    
    parser.add_argument(
        '--marker',
        help='Run tests with specific pytest marker'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )
    
    parser.add_argument(
        '--coverage', '-c',
        action='store_true',
        help='Generate coverage report'
    )
    
    parser.add_argument(
        '--include-slow',
        action='store_true',
        help='Include slow tests (performance tests)'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='Generate HTML test report'
    )
    
    parser.add_argument(
        '--check-deps',
        action='store_true',
        help='Check dependencies only'
    )
    
    args = parser.parse_args()
    
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    if args.check_deps:
        print("Dependencies check passed.")
        sys.exit(0)
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    success = False
    
    try:
        if args.file:
            success, _ = run_specific_test_file(args.file, args.verbose, args.coverage)
        elif args.marker:
            success, _ = run_tests_by_marker(args.marker, args.verbose)
        elif args.report:
            success, _ = generate_test_report()
        elif args.type == 'unit':
            success, _ = run_unit_tests(args.verbose, args.coverage)
        elif args.type == 'integration':
            success, _ = run_integration_tests(args.verbose, args.coverage)
        elif args.type == 'mock':
            success, _ = run_mock_tests(args.verbose)
        elif args.type == 'e2e':
            success, _ = run_end_to_end_tests(args.verbose)
        elif args.type == 'performance':
            success, _ = run_performance_tests(args.verbose, args.include_slow)
        elif args.type == 'existing':
            success, _ = run_existing_tests(args.verbose)
        elif args.type == 'all':
            success = run_all_tests(args.verbose, args.coverage, args.include_slow)
        
        if success:
            print(f"\n✅ Tests completed successfully!")
            sys.exit(0)
        else:
            print(f"\n❌ Some tests failed!")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print(f"\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Error running tests: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()