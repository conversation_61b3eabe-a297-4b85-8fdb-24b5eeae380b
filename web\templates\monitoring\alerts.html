{% extends "base.html" %}

{% block title %}System Alerts - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    System Alerts
                </h2>
                <div>
                    <button onclick="location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh
                    </button>
                    <a href="{{ url_for('monitoring.system_status') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-heartbeat me-2"></i>
                        System Status
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-danger">
                <div class="card-body">
                    <h3 class="text-danger">{{ critical_alerts|length }}</h3>
                    <p class="card-text">Critical Alerts</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <h3 class="text-warning">
                        {{ recent_alerts|selectattr('level', 'equalto', 'error')|list|length }}
                    </h3>
                    <p class="card-text">Error Alerts</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <h3 class="text-info">
                        {{ recent_alerts|selectattr('level', 'equalto', 'warning')|list|length }}
                    </h3>
                    <p class="card-text">Warning Alerts</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <h3 class="text-success">{{ recent_alerts|length }}</h3>
                    <p class="card-text">Total (24h)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Critical Alerts -->
    {% if critical_alerts %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Critical Alerts
                    </h5>
                </div>
                <div class="card-body">
                    {% for alert in critical_alerts %}
                    <div class="alert alert-danger d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="alert-heading">{{ alert.component }}</h6>
                            <p class="mb-1">{{ alert.message }}</p>
                            <small class="text-muted">
                                {{ alert.timestamp }} 
                                {% if alert.count > 1 %}
                                    ({{ alert.count }} occurrences)
                                {% endif %}
                            </small>
                            {% if alert.details %}
                            <div class="mt-2">
                                <small>
                                    {% for key, value in alert.details.items() %}
                                        <span class="badge bg-secondary me-1">{{ key }}: {{ value }}</span>
                                    {% endfor %}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                        <span class="badge bg-danger">CRITICAL</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- All Recent Alerts -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Recent Alerts (Last 24 Hours)
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_alerts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Level</th>
                                    <th>Component</th>
                                    <th>Message</th>
                                    <th>Count</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for alert in recent_alerts|sort(attribute='timestamp', reverse=true) %}
                                <tr>
                                    <td>
                                        <small>{{ alert.timestamp }}</small>
                                    </td>
                                    <td>
                                        {% if alert.level == 'critical' %}
                                            <span class="badge bg-danger">Critical</span>
                                        {% elif alert.level == 'error' %}
                                            <span class="badge bg-warning">Error</span>
                                        {% elif alert.level == 'warning' %}
                                            <span class="badge bg-info">Warning</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ alert.level.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ alert.component }}</code>
                                    </td>
                                    <td>{{ alert.message }}</td>
                                    <td>
                                        {% if alert.count > 1 %}
                                            <span class="badge bg-secondary">{{ alert.count }}</span>
                                        {% else %}
                                            1
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if alert.details %}
                                            <button class="btn btn-sm btn-outline-secondary" 
                                                    data-bs-toggle="collapse" 
                                                    data-bs-target="#details-{{ loop.index }}">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                            <div class="collapse mt-2" id="details-{{ loop.index }}">
                                                <div class="card card-body">
                                                    <pre><code>{{ alert.details|tojson(indent=2) }}</code></pre>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>No Recent Alerts</h5>
                        <p class="text-muted">System is running smoothly with no alerts in the last 24 hours.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 60 seconds
setTimeout(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}