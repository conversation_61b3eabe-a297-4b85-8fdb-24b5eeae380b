"""
Scheduler Manager for Instagram Follower Monitor.

This module provides high-level management of the scheduling system,
integrating the scheduler service with the monitoring service.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta, timezone

from services.scheduler import SchedulerService
from services.monitoring_service import MonitoringService
from config import Config

logger = logging.getLogger(__name__)


class SchedulerManager:
    """High-level manager for scheduling and monitoring integration."""
    
    def __init__(self, config: Config):
        """Initialize scheduler manager.
        
        Args:
            config: Application configuration instance
        """
        self.config = config
        self.scheduler_service = SchedulerService(config)
        self.monitoring_service = None
        self._initialized = False
    
    def initialize(self, monitoring_service: MonitoringService) -> bool:
        """Initialize the scheduler manager with monitoring service.
        
        Args:
            monitoring_service: MonitoringService instance
            
        Returns:
            bool: True if initialized successfully
        """
        try:
            self.monitoring_service = monitoring_service
            
            # Set monitoring callback for scheduler
            self.scheduler_service.set_monitoring_callback(
                self.monitoring_service.start_monitoring_cycle
            )
            
            # Start the scheduler
            if not self.scheduler_service.start():
                logger.error("Failed to start scheduler service")
                return False
            
            self._initialized = True
            logger.info("Scheduler manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize scheduler manager: {e}")
            return False
    
    def shutdown(self, wait: bool = True) -> bool:
        """Shutdown the scheduler manager.
        
        Args:
            wait: Whether to wait for running jobs to complete
            
        Returns:
            bool: True if shutdown successfully
        """
        try:
            if not self._initialized:
                return True
            
            success = self.scheduler_service.stop(wait=wait)
            self._initialized = False
            
            logger.info("Scheduler manager shutdown completed")
            return success
            
        except Exception as e:
            logger.error(f"Error during scheduler manager shutdown: {e}")
            return False
    
    def start_periodic_monitoring(self, 
                                interval_hours: Optional[int] = None,
                                start_immediately: bool = False) -> Dict[str, Any]:
        """Start periodic monitoring with specified interval.
        
        Args:
            interval_hours: Monitoring interval in hours (uses config default if None)
            start_immediately: Whether to run first monitoring cycle immediately
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if not self._initialized:
                return {
                    'success': False,
                    'message': 'Scheduler manager not initialized'
                }
            
            # Use config default if not specified
            if interval_hours is None:
                interval_hours = self.config.MONITORING_INTERVAL_HOURS
            
            # Validate interval
            if interval_hours < 1:
                return {
                    'success': False,
                    'message': 'Monitoring interval must be at least 1 hour'
                }
            
            # Schedule the periodic job
            job_id = 'periodic_monitoring'
            success = self.scheduler_service.schedule_monitoring_job(
                job_id=job_id,
                interval_hours=interval_hours,
                replace_existing=True
            )
            
            if not success:
                return {
                    'success': False,
                    'message': 'Failed to schedule periodic monitoring job'
                }
            
            # Run immediately if requested
            if start_immediately:
                self.scheduler_service.run_job_now(job_id)
            
            logger.info(f"Started periodic monitoring with {interval_hours}h interval")
            
            return {
                'success': True,
                'message': f'Periodic monitoring started with {interval_hours}h interval',
                'interval_hours': interval_hours,
                'job_id': job_id,
                'started_immediately': start_immediately
            }
            
        except Exception as e:
            logger.error(f"Failed to start periodic monitoring: {e}")
            return {
                'success': False,
                'message': f'Error starting periodic monitoring: {e}',
                'error': str(e)
            }
    
    def stop_periodic_monitoring(self) -> Dict[str, Any]:
        """Stop periodic monitoring.
        
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if not self._initialized:
                return {
                    'success': False,
                    'message': 'Scheduler manager not initialized'
                }
            
            job_id = 'periodic_monitoring'
            
            if not self.scheduler_service.job_exists(job_id):
                return {
                    'success': True,
                    'message': 'Periodic monitoring was not running'
                }
            
            success = self.scheduler_service.remove_job(job_id)
            
            if success:
                logger.info("Stopped periodic monitoring")
                return {
                    'success': True,
                    'message': 'Periodic monitoring stopped'
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to stop periodic monitoring'
                }
                
        except Exception as e:
            logger.error(f"Failed to stop periodic monitoring: {e}")
            return {
                'success': False,
                'message': f'Error stopping periodic monitoring: {e}',
                'error': str(e)
            }
    
    def pause_periodic_monitoring(self) -> Dict[str, Any]:
        """Pause periodic monitoring (can be resumed later).
        
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if not self._initialized:
                return {
                    'success': False,
                    'message': 'Scheduler manager not initialized'
                }
            
            job_id = 'periodic_monitoring'
            
            if not self.scheduler_service.job_exists(job_id):
                return {
                    'success': False,
                    'message': 'Periodic monitoring job not found'
                }
            
            success = self.scheduler_service.pause_job(job_id)
            
            if success:
                logger.info("Paused periodic monitoring")
                return {
                    'success': True,
                    'message': 'Periodic monitoring paused'
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to pause periodic monitoring'
                }
                
        except Exception as e:
            logger.error(f"Failed to pause periodic monitoring: {e}")
            return {
                'success': False,
                'message': f'Error pausing periodic monitoring: {e}',
                'error': str(e)
            }
    
    def resume_periodic_monitoring(self) -> Dict[str, Any]:
        """Resume paused periodic monitoring.
        
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if not self._initialized:
                return {
                    'success': False,
                    'message': 'Scheduler manager not initialized'
                }
            
            job_id = 'periodic_monitoring'
            
            if not self.scheduler_service.job_exists(job_id):
                return {
                    'success': False,
                    'message': 'Periodic monitoring job not found'
                }
            
            success = self.scheduler_service.resume_job(job_id)
            
            if success:
                logger.info("Resumed periodic monitoring")
                return {
                    'success': True,
                    'message': 'Periodic monitoring resumed'
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to resume periodic monitoring'
                }
                
        except Exception as e:
            logger.error(f"Failed to resume periodic monitoring: {e}")
            return {
                'success': False,
                'message': f'Error resuming periodic monitoring: {e}',
                'error': str(e)
            }
    
    def update_monitoring_interval(self, interval_hours: int) -> Dict[str, Any]:
        """Update the monitoring interval for periodic monitoring.
        
        Args:
            interval_hours: New interval in hours
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if not self._initialized:
                return {
                    'success': False,
                    'message': 'Scheduler manager not initialized'
                }
            
            # Validate interval
            if interval_hours < 1:
                return {
                    'success': False,
                    'message': 'Monitoring interval must be at least 1 hour'
                }
            
            job_id = 'periodic_monitoring'
            
            if not self.scheduler_service.job_exists(job_id):
                # If job doesn't exist, create it
                return self.start_periodic_monitoring(interval_hours=interval_hours)
            
            # Modify existing job
            success = self.scheduler_service.modify_job_interval(job_id, interval_hours)
            
            if success:
                logger.info(f"Updated monitoring interval to {interval_hours}h")
                return {
                    'success': True,
                    'message': f'Monitoring interval updated to {interval_hours}h',
                    'interval_hours': interval_hours
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to update monitoring interval'
                }
                
        except Exception as e:
            logger.error(f"Failed to update monitoring interval: {e}")
            return {
                'success': False,
                'message': f'Error updating monitoring interval: {e}',
                'error': str(e)
            }
    
    def run_monitoring_now(self) -> Dict[str, Any]:
        """Run monitoring immediately (one-time execution).
        
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if not self._initialized:
                return {
                    'success': False,
                    'message': 'Scheduler manager not initialized'
                }
            
            # Create a one-time job to run immediately
            job_id = f"manual_monitoring_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
            run_time = datetime.now(timezone.utc) + timedelta(seconds=5)  # Run in 5 seconds
            
            success = self.scheduler_service.schedule_one_time_job(
                job_id=job_id,
                run_time=run_time,
                replace_existing=True
            )
            
            if success:
                logger.info("Scheduled immediate monitoring execution")
                return {
                    'success': True,
                    'message': 'Monitoring scheduled to run immediately',
                    'job_id': job_id,
                    'run_time': run_time.isoformat()
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to schedule immediate monitoring'
                }
                
        except Exception as e:
            logger.error(f"Failed to run monitoring now: {e}")
            return {
                'success': False,
                'message': f'Error scheduling immediate monitoring: {e}',
                'error': str(e)
            }
    
    def get_monitoring_schedule_status(self) -> Dict[str, Any]:
        """Get status of monitoring schedule and jobs.
        
        Returns:
            Dict[str, Any]: Schedule status information
        """
        try:
            if not self._initialized:
                return {
                    'initialized': False,
                    'scheduler_running': False,
                    'periodic_monitoring_active': False
                }
            
            # Get scheduler status
            scheduler_status = self.scheduler_service.get_scheduler_status()
            
            # Get periodic monitoring job info
            periodic_job = self.scheduler_service.get_job_info('periodic_monitoring')
            
            # Get all jobs
            all_jobs = self.scheduler_service.get_all_jobs()
            
            return {
                'initialized': self._initialized,
                'scheduler_running': scheduler_status.get('running', False),
                'scheduler_statistics': scheduler_status.get('statistics', {}),
                'periodic_monitoring_active': periodic_job is not None,
                'periodic_job_info': periodic_job,
                'total_jobs': len(all_jobs),
                'all_jobs': all_jobs
            }
            
        except Exception as e:
            logger.error(f"Failed to get monitoring schedule status: {e}")
            return {
                'initialized': self._initialized,
                'scheduler_running': False,
                'periodic_monitoring_active': False,
                'error': str(e)
            }
    
    def schedule_data_cleanup(self, 
                            interval_hours: int = 24,
                            start_time_hour: int = 2) -> Dict[str, Any]:
        """Schedule periodic data cleanup job.
        
        Args:
            interval_hours: How often to run cleanup (default: daily)
            start_time_hour: Hour of day to start cleanup (0-23, default: 2 AM)
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            if not self._initialized:
                return {
                    'success': False,
                    'message': 'Scheduler manager not initialized'
                }
            
            if not self.monitoring_service:
                return {
                    'success': False,
                    'message': 'Monitoring service not available'
                }
            
            # Calculate next run time (next occurrence of start_time_hour)
            now = datetime.now(timezone.utc)
            next_run = now.replace(hour=start_time_hour, minute=0, second=0, microsecond=0)
            
            # If the time has passed today, schedule for tomorrow
            if next_run <= now:
                next_run += timedelta(days=1)
            
            # Schedule cleanup job
            job_id = 'data_cleanup'
            
            # Remove existing cleanup job if it exists
            if self.scheduler_service.job_exists(job_id):
                self.scheduler_service.remove_job(job_id)
            
            # Add new cleanup job
            success = self.scheduler_service._scheduler.add_job(
                func=self.monitoring_service.cleanup_old_data,
                trigger='interval',
                hours=interval_hours,
                id=job_id,
                name=f'Data Cleanup (every {interval_hours}h)',
                next_run_time=next_run,
                replace_existing=True
            )
            
            if success:
                logger.info(f"Scheduled data cleanup job to run every {interval_hours}h starting at {next_run}")
                return {
                    'success': True,
                    'message': f'Data cleanup scheduled every {interval_hours}h',
                    'next_run_time': next_run.isoformat(),
                    'interval_hours': interval_hours
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to schedule data cleanup job'
                }
                
        except Exception as e:
            logger.error(f"Failed to schedule data cleanup: {e}")
            return {
                'success': False,
                'message': f'Error scheduling data cleanup: {e}',
                'error': str(e)
            }
    
    def is_initialized(self) -> bool:
        """Check if scheduler manager is initialized.
        
        Returns:
            bool: True if initialized
        """
        return self._initialized
    
    def get_next_monitoring_time(self) -> Optional[datetime]:
        """Get the next scheduled monitoring time.
        
        Returns:
            Optional[datetime]: Next monitoring time or None if not scheduled
        """
        try:
            if not self._initialized:
                return None
            
            job_info = self.scheduler_service.get_job_info('periodic_monitoring')
            if not job_info or not job_info.get('next_run_time'):
                return None
            
            return datetime.fromisoformat(job_info['next_run_time'].replace('Z', '+00:00'))
            
        except Exception as e:
            logger.error(f"Failed to get next monitoring time: {e}")
            return None