-- Instagram Follower Monitor Database Schema

-- Profile tracking configuration
CREATE TABLE IF NOT EXISTS profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    display_name TEXT,
    is_private BOOLEAN DEFAULT FALSE,
    monitoring_enabled BOOLEAN DEFAULT TRUE,
    last_scan TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Current follower snapshots
CREATE TABLE IF NOT EXISTS current_followers (
    profile_id INTEGER,
    follower_username TEXT,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE,
    PRIMARY KEY (profile_id, follower_username)
);

-- Current following snapshots
CREATE TABLE IF NOT EXISTS current_following (
    profile_id INTEGER,
    following_username TEXT,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE,
    PRIMARY KEY (profile_id, following_username)
);

-- Follower change history
CREATE TABLE IF NOT EXISTS follower_changes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    profile_id INTEGER,
    username TEXT NOT NULL,
    change_type TEXT CHECK(change_type IN ('gained', 'lost')) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE
);

-- Following change history
CREATE TABLE IF NOT EXISTS following_changes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    profile_id INTEGER,
    username TEXT NOT NULL,
    change_type TEXT CHECK(change_type IN ('started_following', 'stopped_following')) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE
);

-- System configuration
CREATE TABLE IF NOT EXISTS settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
CREATE INDEX IF NOT EXISTS idx_profiles_monitoring_enabled ON profiles(monitoring_enabled);
CREATE INDEX IF NOT EXISTS idx_profiles_last_scan ON profiles(last_scan);
CREATE INDEX IF NOT EXISTS idx_follower_changes_profile_timestamp ON follower_changes(profile_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_following_changes_profile_timestamp ON following_changes(profile_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_follower_changes_timestamp ON follower_changes(timestamp);
CREATE INDEX IF NOT EXISTS idx_following_changes_timestamp ON following_changes(timestamp);
CREATE INDEX IF NOT EXISTS idx_follower_changes_username ON follower_changes(username);
CREATE INDEX IF NOT EXISTS idx_following_changes_username ON following_changes(username);
CREATE INDEX IF NOT EXISTS idx_current_followers_profile ON current_followers(profile_id);
CREATE INDEX IF NOT EXISTS idx_current_following_profile ON current_following(profile_id);
CREATE INDEX IF NOT EXISTS idx_current_followers_added_at ON current_followers(added_at);
CREATE INDEX IF NOT EXISTS idx_current_following_added_at ON current_following(added_at);