#!/bin/bash

# Instagram Follower Monitor - Deployment Script
# This script automates the deployment process for production environments

set -e  # Exit on any error

# Configuration
APP_NAME="instagram-monitor"
APP_DIR="/var/www/instagram-monitor"
APP_USER="www-data"
PYTHON_VERSION="3.11"
VENV_DIR="$APP_DIR/venv"
SERVICE_NAME="instagram-monitor"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        error "Cannot detect operating system"
        exit 1
    fi
    
    log "Detected OS: $OS $VER"
}

# Install system dependencies
install_dependencies() {
    log "Installing system dependencies..."
    
    case $OS in
        "Ubuntu"*|"Debian"*)
            apt update
            apt install -y \
                python${PYTHON_VERSION} \
                python${PYTHON_VERSION}-venv \
                python${PYTHON_VERSION}-dev \
                python3-pip \
                nginx \
                sqlite3 \
                git \
                curl \
                build-essential \
                libffi-dev \
                libssl-dev \
                supervisor \
                logrotate \
                fail2ban \
                ufw
            ;;
        "CentOS"*|"Red Hat"*|"Rocky"*)
            yum update -y
            yum install -y \
                python${PYTHON_VERSION} \
                python${PYTHON_VERSION}-devel \
                python3-pip \
                nginx \
                sqlite \
                git \
                curl \
                gcc \
                gcc-c++ \
                make \
                openssl-devel \
                libffi-devel \
                supervisor \
                logrotate \
                fail2ban \
                firewalld
            ;;
        *)
            error "Unsupported operating system: $OS"
            exit 1
            ;;
    esac
    
    success "System dependencies installed"
}

# Create application user and directories
setup_user_and_directories() {
    log "Setting up application user and directories..."
    
    # Create application user if it doesn't exist
    if ! id "$APP_USER" &>/dev/null; then
        useradd -r -s /bin/false -d "$APP_DIR" "$APP_USER"
        log "Created user: $APP_USER"
    fi
    
    # Create application directory
    mkdir -p "$APP_DIR"
    mkdir -p "$APP_DIR/logs"
    mkdir -p "$APP_DIR/backups"
    mkdir -p "$APP_DIR/data"
    
    # Set ownership
    chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    
    success "User and directories created"
}

# Clone or update application code
deploy_application() {
    log "Deploying application code..."
    
    if [[ -d "$APP_DIR/.git" ]]; then
        log "Updating existing installation..."
        cd "$APP_DIR"
        sudo -u "$APP_USER" git pull origin main
    else
        log "Cloning application repository..."
        # Replace with your actual repository URL
        git clone https://github.com/your-org/instagram-follower-monitor.git "$APP_DIR"
        chown -R "$APP_USER:$APP_USER" "$APP_DIR"
    fi
    
    success "Application code deployed"
}

# Setup Python virtual environment
setup_python_environment() {
    log "Setting up Python virtual environment..."
    
    cd "$APP_DIR"
    
    # Create virtual environment
    sudo -u "$APP_USER" python${PYTHON_VERSION} -m venv "$VENV_DIR"
    
    # Upgrade pip
    sudo -u "$APP_USER" "$VENV_DIR/bin/pip" install --upgrade pip setuptools wheel
    
    # Install Python dependencies
    sudo -u "$APP_USER" "$VENV_DIR/bin/pip" install -r requirements.txt
    
    success "Python environment configured"
}

# Generate configuration files
generate_config() {
    log "Generating configuration files..."
    
    # Generate Flask secret key
    SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")
    
    # Generate encryption key
    ENCRYPTION_KEY=$(python3 -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
    
    # Create .env file
    cat > "$APP_DIR/.env" << EOF
# Flask configuration
SECRET_KEY=$SECRET_KEY
FLASK_ENV=production

# Database configuration
DATABASE_URL=sqlite:///$APP_DIR/data/instagram_monitor.db

# Encryption key for Instagram credentials
ENCRYPTION_KEY=$ENCRYPTION_KEY

# Logging configuration
LOG_LEVEL=INFO

# Monitoring configuration
MONITORING_INTERVAL=2
MAX_PROFILES=10

# Security settings
SESSION_TIMEOUT=3600
CSRF_TIME_LIMIT=3600

# Rate limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Backup configuration
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=true
EOF
    
    # Set secure permissions
    chmod 600 "$APP_DIR/.env"
    chown "$APP_USER:$APP_USER" "$APP_DIR/.env"
    
    success "Configuration files generated"
}

# Initialize database
initialize_database() {
    log "Initializing database..."
    
    cd "$APP_DIR"
    sudo -u "$APP_USER" "$VENV_DIR/bin/python" init_database.py
    
    # Set database permissions
    chmod 640 "$APP_DIR/data/instagram_monitor.db"
    chown "$APP_USER:$APP_USER" "$APP_DIR/data/instagram_monitor.db"
    
    success "Database initialized"
}

# Setup systemd service
setup_systemd_service() {
    log "Setting up systemd service..."
    
    # Update service file with correct paths
    sed -e "s|/var/www/instagram-monitor|$APP_DIR|g" \
        -e "s|www-data|$APP_USER|g" \
        "$APP_DIR/deployment/systemd/instagram-monitor.service" > \
        "/etc/systemd/system/$SERVICE_NAME.service"
    
    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    success "Systemd service configured"
}

# Setup Nginx
setup_nginx() {
    log "Setting up Nginx..."
    
    # Update Nginx configuration with correct paths
    sed -e "s|/var/www/instagram-monitor|$APP_DIR|g" \
        -e "s|your-domain.com|$(hostname -f)|g" \
        "$APP_DIR/deployment/nginx.conf" > \
        "/etc/nginx/sites-available/$APP_NAME"
    
    # Enable site
    ln -sf "/etc/nginx/sites-available/$APP_NAME" "/etc/nginx/sites-enabled/"
    
    # Remove default site
    rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    nginx -t
    
    # Enable and start Nginx
    systemctl enable nginx
    systemctl restart nginx
    
    success "Nginx configured"
}

# Setup log rotation
setup_log_rotation() {
    log "Setting up log rotation..."
    
    cat > "/etc/logrotate.d/$APP_NAME" << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_USER
    postrotate
        systemctl reload $SERVICE_NAME
    endscript
}
EOF
    
    success "Log rotation configured"
}

# Setup firewall
setup_firewall() {
    log "Setting up firewall..."
    
    case $OS in
        "Ubuntu"*|"Debian"*)
            # UFW configuration
            ufw --force reset
            ufw default deny incoming
            ufw default allow outgoing
            ufw allow ssh
            ufw allow 'Nginx Full'
            ufw --force enable
            ;;
        "CentOS"*|"Red Hat"*|"Rocky"*)
            # Firewalld configuration
            systemctl enable firewalld
            systemctl start firewalld
            firewall-cmd --permanent --add-service=ssh
            firewall-cmd --permanent --add-service=http
            firewall-cmd --permanent --add-service=https
            firewall-cmd --reload
            ;;
    esac
    
    success "Firewall configured"
}

# Setup fail2ban
setup_fail2ban() {
    log "Setting up fail2ban..."
    
    cat > "/etc/fail2ban/jail.d/$APP_NAME.conf" << EOF
[instagram-monitor]
enabled = true
port = http,https
filter = instagram-monitor
logpath = $APP_DIR/logs/web.log
maxretry = 5
bantime = 3600
findtime = 600

[nginx-req-limit]
enabled = true
filter = nginx-req-limit
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
EOF
    
    # Create filter
    cat > "/etc/fail2ban/filter.d/instagram-monitor.conf" << EOF
[Definition]
failregex = ^.*"POST /login.*" 40[01] .*$
            ^.*"POST /api/.*" 40[13] .*$
ignoreregex =
EOF
    
    systemctl enable fail2ban
    systemctl restart fail2ban
    
    success "Fail2ban configured"
}

# Start services
start_services() {
    log "Starting services..."
    
    # Start application service
    systemctl start "$SERVICE_NAME"
    
    # Check service status
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        success "Application service started successfully"
    else
        error "Failed to start application service"
        systemctl status "$SERVICE_NAME"
        exit 1
    fi
    
    # Check Nginx status
    if systemctl is-active --quiet nginx; then
        success "Nginx started successfully"
    else
        error "Failed to start Nginx"
        systemctl status nginx
        exit 1
    fi
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Check if application responds
    sleep 5  # Give services time to start
    
    if curl -f -s http://localhost/health > /dev/null; then
        success "Application health check passed"
    else
        error "Application health check failed"
        exit 1
    fi
    
    # Check database
    if sudo -u "$APP_USER" "$VENV_DIR/bin/python" -c "
from database.connection import DatabaseManager
db = DatabaseManager()
assert db.test_connection(), 'Database connection failed'
print('Database connection OK')
"; then
        success "Database connection verified"
    else
        error "Database connection failed"
        exit 1
    fi
}

# Setup SSL certificate (optional)
setup_ssl() {
    if [[ "$1" == "--ssl" ]]; then
        log "Setting up SSL certificate..."
        
        # Install certbot
        case $OS in
            "Ubuntu"*|"Debian"*)
                apt install -y certbot python3-certbot-nginx
                ;;
            "CentOS"*|"Red Hat"*|"Rocky"*)
                yum install -y certbot python3-certbot-nginx
                ;;
        esac
        
        # Get domain name
        read -p "Enter your domain name: " DOMAIN_NAME
        
        if [[ -n "$DOMAIN_NAME" ]]; then
            certbot --nginx -d "$DOMAIN_NAME" --non-interactive --agree-tos --email admin@"$DOMAIN_NAME"
            
            # Setup auto-renewal
            echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
            
            success "SSL certificate configured for $DOMAIN_NAME"
        else
            warning "No domain name provided, skipping SSL setup"
        fi
    fi
}

# Create backup script
create_backup_script() {
    log "Creating backup script..."
    
    cat > "$APP_DIR/backup.sh" << 'EOF'
#!/bin/bash
# Automated backup script for Instagram Follower Monitor

BACKUP_DIR="/var/backups/instagram-monitor"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/var/www/instagram-monitor"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Backup database
sqlite3 "$APP_DIR/data/instagram_monitor.db" ".backup $BACKUP_DIR/database_$DATE.db"

# Backup configuration
cp "$APP_DIR/.env" "$BACKUP_DIR/config_$DATE.env"

# Compress and encrypt backup
tar -czf - "$BACKUP_DIR/database_$DATE.db" "$BACKUP_DIR/config_$DATE.env" | \
    gpg --cipher-algo AES256 --compress-algo 1 --s2k-mode 3 \
    --s2k-digest-algo SHA512 --s2k-count 65536 --force-mdc \
    --quiet --no-greeting --batch --yes \
    --passphrase-file /etc/instagram-monitor/backup.key \
    --output "$BACKUP_DIR/backup_$DATE.tar.gz.gpg" \
    --symmetric

# Clean up temporary files
rm -f "$BACKUP_DIR/database_$DATE.db" "$BACKUP_DIR/config_$DATE.env"

# Remove old backups (keep 30 days)
find "$BACKUP_DIR" -name "backup_*.tar.gz.gpg" -mtime +30 -delete

echo "Backup completed: backup_$DATE.tar.gz.gpg"
EOF
    
    chmod +x "$APP_DIR/backup.sh"
    chown "$APP_USER:$APP_USER" "$APP_DIR/backup.sh"
    
    # Setup daily backup cron job
    echo "0 2 * * * $APP_USER $APP_DIR/backup.sh" >> /etc/crontab
    
    success "Backup script created and scheduled"
}

# Print deployment summary
print_summary() {
    echo
    echo "=========================================="
    echo "  Instagram Follower Monitor Deployment"
    echo "=========================================="
    echo
    success "Deployment completed successfully!"
    echo
    echo "Application Details:"
    echo "  - Application Directory: $APP_DIR"
    echo "  - Service Name: $SERVICE_NAME"
    echo "  - User: $APP_USER"
    echo "  - Python Version: $PYTHON_VERSION"
    echo
    echo "Access Information:"
    echo "  - Web Interface: http://$(hostname -f)/"
    echo "  - Health Check: http://$(hostname -f)/health"
    echo "  - API Documentation: http://$(hostname -f)/api/docs"
    echo
    echo "Service Management:"
    echo "  - Start: systemctl start $SERVICE_NAME"
    echo "  - Stop: systemctl stop $SERVICE_NAME"
    echo "  - Restart: systemctl restart $SERVICE_NAME"
    echo "  - Status: systemctl status $SERVICE_NAME"
    echo "  - Logs: journalctl -u $SERVICE_NAME -f"
    echo
    echo "Configuration:"
    echo "  - Environment: $APP_DIR/.env"
    echo "  - Nginx Config: /etc/nginx/sites-available/$APP_NAME"
    echo "  - Service Config: /etc/systemd/system/$SERVICE_NAME.service"
    echo
    echo "Next Steps:"
    echo "  1. Configure Instagram credentials via web interface"
    echo "  2. Add profiles to monitor"
    echo "  3. Review security settings"
    echo "  4. Setup monitoring and alerting"
    echo "  5. Configure SSL certificate (if not done already)"
    echo
    warning "Remember to:"
    warning "  - Change default passwords"
    warning "  - Review firewall settings"
    warning "  - Setup monitoring"
    warning "  - Configure backups"
    echo
}

# Main deployment function
main() {
    log "Starting Instagram Follower Monitor deployment..."
    
    check_root
    detect_os
    install_dependencies
    setup_user_and_directories
    deploy_application
    setup_python_environment
    generate_config
    initialize_database
    setup_systemd_service
    setup_nginx
    setup_log_rotation
    setup_firewall
    setup_fail2ban
    create_backup_script
    start_services
    verify_deployment
    setup_ssl "$@"
    print_summary
    
    success "Deployment completed successfully!"
}

# Run main function with all arguments
main "$@"