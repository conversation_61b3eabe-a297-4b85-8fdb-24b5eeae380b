{% extends "base.html" %}

{% block title %}Settings - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Settings & Configuration</h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('main.export_settings') }}" class="btn btn-outline-primary">
                        <i class="fas fa-download"></i> Export Settings
                    </a>
                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="fas fa-upload"></i> Import Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Monitoring Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> Monitoring Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('main.update_monitoring_settings') }}">
                        {{ csrf_token() }}
                        <div class="mb-3">
                            <label for="monitoring_interval_hours" class="form-label">Monitoring Interval (hours)</label>
                            <input type="number" class="form-control" id="monitoring_interval_hours" 
                                   name="monitoring_interval_hours" min="1" max="168" 
                                   value="{{ monitoring_settings.monitoring_interval_hours }}" required>
                            <div class="form-text">How often to check for follower changes (1-168 hours)</div>
                        </div>

                        <div class="mb-3">
                            <label for="data_retention_days" class="form-label">Data Retention (days)</label>
                            <input type="number" class="form-control" id="data_retention_days" 
                                   name="data_retention_days" min="1" max="3650" 
                                   value="{{ monitoring_settings.data_retention_days }}" required>
                            <div class="form-text">How long to keep historical data (1-3650 days)</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="min_request_delay" class="form-label">Min Request Delay (seconds)</label>
                                <input type="number" class="form-control" id="min_request_delay" 
                                       name="min_request_delay" min="0" max="60" step="0.1" 
                                       value="{{ monitoring_settings.min_request_delay }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="max_request_delay" class="form-label">Max Request Delay (seconds)</label>
                                <input type="number" class="form-control" id="max_request_delay" 
                                       name="max_request_delay" min="0" max="60" step="0.1" 
                                       value="{{ monitoring_settings.max_request_delay }}" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="max_retries" class="form-label">Max Retries</label>
                                <input type="number" class="form-control" id="max_retries" 
                                       name="max_retries" min="0" max="10" 
                                       value="{{ monitoring_settings.max_retries }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="profile_processing_delay" class="form-label">Profile Processing Delay (seconds)</label>
                                <input type="number" class="form-control" id="profile_processing_delay" 
                                       name="profile_processing_delay" min="0" max="300" step="0.1" 
                                       value="{{ monitoring_settings.profile_processing_delay }}" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Monitoring Settings
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Instagram Credentials -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fab fa-instagram"></i> Instagram Credentials
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <span class="badge {{ 'bg-success' if auth_status.has_credentials else 'bg-warning' }} me-2">
                                {{ 'Configured' if auth_status.has_credentials else 'Not Configured' }}
                            </span>
                            {% if auth_status.has_credentials and auth_status.credentials %}
                                <small class="text-muted">Username: {{ auth_status.credentials.username }}</small>
                            {% endif %}
                        </div>
                    </div>

                    <form method="POST" action="{{ url_for('main.update_credentials') }}">
                        {{ csrf_token() }}
                        <div class="mb-3">
                            <label for="instagram_username" class="form-label">Instagram Username</label>
                            <input type="text" class="form-control" id="instagram_username" 
                                   name="instagram_username" 
                                   value="{{ auth_status.credentials.username if auth_status.credentials else '' }}" 
                                   placeholder="your_instagram_username" required>
                        </div>

                        <div class="mb-3">
                            <label for="instagram_password" class="form-label">Instagram Password</label>
                            <input type="password" class="form-control" id="instagram_password" 
                                   name="instagram_password" placeholder="Enter password" required>
                            <div class="form-text">Password is encrypted and stored securely</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Credentials
                            </button>
                            {% if auth_status.has_credentials %}
                                <form method="POST" action="{{ url_for('main.clear_credentials') }}" class="d-inline">
                                    {{ csrf_token() }}
                                    <button type="submit" class="btn btn-outline-danger" 
                                            onclick="return confirm('Are you sure you want to clear stored credentials?')">
                                        <i class="fas fa-trash"></i> Clear Credentials
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> System Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="h4 text-primary mb-1">{{ system_status.total_profiles }}</div>
                                <div class="text-muted small">Total Profiles</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="h4 text-success mb-1">{{ system_status.enabled_profiles }}</div>
                                <div class="text-muted small">Enabled Profiles</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="h4 text-info mb-1">{{ system_status.profiles_with_recent_scans }}</div>
                                <div class="text-muted small">Recent Scans (24h)</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="h4 text-secondary mb-1">{{ system_status.database_size }}</div>
                                <div class="text-muted small">Database Size</div>
                            </div>
                        </div>
                    </div>

                    {% if system_status.last_scan_times %}
                        <hr>
                        <h6>Last Scan Times</h6>
                        <div class="row">
                            {% for username, last_scan in system_status.last_scan_times.items() %}
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-truncate">@{{ username }}</span>
                                        <small class="text-muted">{{ last_scan | timeago }}</small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Authentication Status Details -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt"></i> Authentication Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Credential Status</h6>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas {{ 'fa-check-circle text-success' if auth_status.has_credentials else 'fa-exclamation-triangle text-warning' }} me-2"></i>
                                <span>{{ 'Credentials stored securely' if auth_status.has_credentials else 'No credentials configured' }}</span>
                            </div>
                            {% if auth_status.has_credentials and auth_status.credentials %}
                                <div class="small text-muted">
                                    <strong>Username:</strong> {{ auth_status.credentials.username }}<br>
                                    <strong>Password:</strong> ••••••••••••
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6>Security Information</h6>
                            <ul class="list-unstyled small text-muted">
                                <li><i class="fas fa-lock me-1"></i> Credentials encrypted with Fernet</li>
                                <li><i class="fas fa-file-shield me-1"></i> Stored with 600 permissions</li>
                                <li><i class="fas fa-key me-1"></i> Encryption key managed separately</li>
                                <li><i class="fas fa-history me-1"></i> Session data encrypted</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Settings Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Import Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('main.import_settings') }}" enctype="multipart/form-data">
                {{ csrf_token() }}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="backup_file" class="form-label">Backup File</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" 
                               accept=".json" required>
                        <div class="form-text">Select a JSON backup file exported from this application</div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="import_profiles" name="import_profiles">
                        <label class="form-check-label" for="import_profiles">
                            Import profiles (will skip existing profiles)
                        </label>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning:</strong> Importing settings will overwrite current configuration. 
                        Consider exporting current settings first as a backup.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Import Settings</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    // Validate delay settings
    const minDelayInput = document.getElementById('min_request_delay');
    const maxDelayInput = document.getElementById('max_request_delay');
    
    function validateDelays() {
        const minDelay = parseFloat(minDelayInput.value);
        const maxDelay = parseFloat(maxDelayInput.value);
        
        if (maxDelay < minDelay) {
            maxDelayInput.setCustomValidity('Max delay must be greater than or equal to min delay');
        } else {
            maxDelayInput.setCustomValidity('');
        }
    }
    
    minDelayInput.addEventListener('input', validateDelays);
    maxDelayInput.addEventListener('input', validateDelays);
    
    // Auto-refresh system status every 30 seconds
    setInterval(function() {
        fetch('/api/monitoring/status')
            .then(response => response.json())
            .then(data => {
                // Update status indicators if needed
                console.log('System status updated:', data);
            })
            .catch(error => console.error('Error updating status:', error));
    }, 30000);
});
</script>
{% endblock %}