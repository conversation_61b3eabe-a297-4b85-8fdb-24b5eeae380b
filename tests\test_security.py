"""
Tests for security features including input validation, CSRF protection,
and session management.
"""

import pytest
from unittest.mock import Mock, patch
from flask import Flask, request, session
from web.security import (
    InputValidator, validate_form_input, validate_api_input,
    sanitize_output, SQLInjectionPrevention, SecurityHeaders
)
from web.session_security import (
    SecureSessionManager, CSRFProtection, rate_limit_manager
)


class TestInputValidator:
    """Test input validation functionality."""
    
    def test_validate_username_valid(self):
        """Test valid username validation."""
        assert InputValidator.validate_username("test_user") == "test_user"
        assert InputValidator.validate_username("@test.user") == "test.user"
        assert InputValidator.validate_username("TEST123") == "test123"
    
    def test_validate_username_invalid(self):
        """Test invalid username validation."""
        with pytest.raises(ValueError, match="Username cannot be empty"):
            InputValidator.validate_username("")
        
        with pytest.raises(ValueError, match="Username cannot exceed 30 characters"):
            InputValidator.validate_username("a" * 31)
        
        with pytest.raises(ValueError, match="Username can only contain"):
            InputValidator.validate_username("test@user!")
        
        with pytest.raises(ValueError, match="Username cannot start or end with a period"):
            InputValidator.validate_username(".testuser")
        
        with pytest.raises(ValueError, match="Username cannot contain consecutive periods"):
            InputValidator.validate_username("test..user")
    
    def test_validate_display_name(self):
        """Test display name validation."""
        assert InputValidator.validate_display_name("Test User") == "Test User"
        assert InputValidator.validate_display_name("") is None
        assert InputValidator.validate_display_name(None) is None
        
        # Test HTML sanitization
        assert InputValidator.validate_display_name("<script>alert('xss')</script>Test") == "Test"
        
        with pytest.raises(ValueError, match="Display name cannot exceed 100 characters"):
            InputValidator.validate_display_name("a" * 101)
    
    def test_validate_integer(self):
        """Test integer validation."""
        assert InputValidator.validate_integer("5") == 5
        assert InputValidator.validate_integer(10, min_val=5, max_val=15) == 10
        
        with pytest.raises(ValueError, match="value must be a valid integer"):
            InputValidator.validate_integer("not_a_number")
        
        with pytest.raises(ValueError, match="value must be at least 5"):
            InputValidator.validate_integer(3, min_val=5)
        
        with pytest.raises(ValueError, match="value cannot exceed 10"):
            InputValidator.validate_integer(15, max_val=10)
    
    def test_validate_float(self):
        """Test float validation."""
        assert InputValidator.validate_float("5.5") == 5.5
        assert InputValidator.validate_float(10.0, min_val=5.0, max_val=15.0) == 10.0
        
        with pytest.raises(ValueError, match="value must be a valid number"):
            InputValidator.validate_float("not_a_number")
    
    def test_validate_boolean(self):
        """Test boolean validation."""
        assert InputValidator.validate_boolean(True) is True
        assert InputValidator.validate_boolean("true") is True
        assert InputValidator.validate_boolean("1") is True
        assert InputValidator.validate_boolean("yes") is True
        assert InputValidator.validate_boolean("on") is True
        
        assert InputValidator.validate_boolean(False) is False
        assert InputValidator.validate_boolean("false") is False
        assert InputValidator.validate_boolean("0") is False
        assert InputValidator.validate_boolean("no") is False
        assert InputValidator.validate_boolean("off") is False
        assert InputValidator.validate_boolean("") is False
        assert InputValidator.validate_boolean(None) is False
    
    def test_validate_search_query(self):
        """Test search query validation."""
        assert InputValidator.validate_search_query("test user") == "test user"
        assert InputValidator.validate_search_query("") == ""
        
        # Test HTML sanitization
        assert InputValidator.validate_search_query("<script>test</script>") == "test"
        
        with pytest.raises(ValueError, match="Search query cannot exceed 100 characters"):
            InputValidator.validate_search_query("a" * 101)
        
        with pytest.raises(ValueError, match="Search query contains invalid characters"):
            InputValidator.validate_search_query("test<>query")


class TestSQLInjectionPrevention:
    """Test SQL injection prevention."""
    
    def test_validate_sql_params_safe(self):
        """Test safe SQL parameters."""
        assert SQLInjectionPrevention.validate_sql_params("test", "user", 123) is True
    
    def test_validate_sql_params_dangerous(self):
        """Test dangerous SQL parameters."""
        assert SQLInjectionPrevention.validate_sql_params("test'; DROP TABLE users; --") is False
        assert SQLInjectionPrevention.validate_sql_params("1 UNION SELECT * FROM users") is False
        assert SQLInjectionPrevention.validate_sql_params("test/*comment*/") is False
    
    def test_escape_like_pattern(self):
        """Test LIKE pattern escaping."""
        assert SQLInjectionPrevention.escape_like_pattern("test%user") == "test\\%user"
        assert SQLInjectionPrevention.escape_like_pattern("test_user") == "test\\_user"
        assert SQLInjectionPrevention.escape_like_pattern("test\\user") == "test\\\\user"


class TestSecurityHeaders:
    """Test security headers functionality."""
    
    def test_apply_security_headers(self):
        """Test security headers application."""
        mock_response = Mock()
        mock_response.headers = {}
        
        result = SecurityHeaders.apply_security_headers(mock_response)
        
        assert 'Content-Security-Policy' in result.headers
        assert 'X-Content-Type-Options' in result.headers
        assert 'X-Frame-Options' in result.headers
        assert 'X-XSS-Protection' in result.headers
        assert 'Referrer-Policy' in result.headers
        
        assert result.headers['X-Content-Type-Options'] == 'nosniff'
        assert result.headers['X-Frame-Options'] == 'DENY'
        assert result.headers['X-XSS-Protection'] == '1; mode=block'


class TestSanitizeOutput:
    """Test output sanitization."""
    
    def test_sanitize_string(self):
        """Test string sanitization."""
        assert sanitize_output("<script>alert('xss')</script>") == "&lt;script&gt;alert('xss')&lt;/script&gt;"
        assert sanitize_output("normal text") == "normal text"
    
    def test_sanitize_dict(self):
        """Test dictionary sanitization."""
        input_dict = {
            'safe': 'normal text',
            'unsafe': '<script>alert("xss")</script>'
        }
        result = sanitize_output(input_dict)
        
        assert result['safe'] == 'normal text'
        assert result['unsafe'] == '&lt;script&gt;alert("xss")&lt;/script&gt;'
    
    def test_sanitize_list(self):
        """Test list sanitization."""
        input_list = ['normal', '<script>xss</script>']
        result = sanitize_output(input_list)
        
        assert result[0] == 'normal'
        assert result[1] == '&lt;script&gt;xss&lt;/script&gt;'


class TestSecureSessionManager:
    """Test secure session management."""
    
    def test_initialize_session(self):
        """Test session initialization."""
        with patch('web.session_security.session', {}) as mock_session:
            with patch('web.session_security.request') as mock_request:
                mock_request.environ = {'HTTP_USER_AGENT': 'test', 'REMOTE_ADDR': '127.0.0.1'}
                
                SecureSessionManager.initialize_session()
                
                assert 'csrf_token' in mock_session
                assert 'session_created' in mock_session
                assert 'session_fingerprint' in mock_session
                assert 'last_activity' in mock_session
    
    def test_validate_session_valid(self):
        """Test valid session validation."""
        from datetime import datetime
        
        mock_session = {
            'session_created': datetime.now().isoformat(),
            'last_activity': datetime.now().isoformat(),
            'session_fingerprint': 'test_fingerprint'
        }
        
        with patch('web.session_security.session', mock_session):
            with patch('web.session_security.request') as mock_request:
                mock_request.environ = {'HTTP_USER_AGENT': 'test', 'REMOTE_ADDR': '127.0.0.1'}
                
                # Mock the fingerprint calculation to match
                with patch('hashlib.sha256') as mock_hash:
                    mock_hash.return_value.hexdigest.return_value = 'test_fingerprint'
                    
                    assert SecureSessionManager.validate_session() is True
    
    def test_validate_session_expired(self):
        """Test expired session validation."""
        from datetime import datetime, timedelta
        
        # Session older than 24 hours
        old_time = (datetime.now() - timedelta(hours=25)).isoformat()
        mock_session = {
            'session_created': old_time,
            'last_activity': old_time
        }
        
        with patch('web.session_security.session', mock_session):
            assert SecureSessionManager.validate_session() is False


class TestCSRFProtection:
    """Test CSRF protection."""
    
    def test_validate_csrf_token_valid(self):
        """Test valid CSRF token validation."""
        token = "test_token_123"
        mock_session = {'csrf_token': token}
        
        with patch('web.session_security.session', mock_session):
            assert CSRFProtection.validate_csrf_token(token) is True
    
    def test_validate_csrf_token_invalid(self):
        """Test invalid CSRF token validation."""
        mock_session = {'csrf_token': 'correct_token'}
        
        with patch('web.session_security.session', mock_session):
            assert CSRFProtection.validate_csrf_token('wrong_token') is False
            assert CSRFProtection.validate_csrf_token('') is False
    
    def test_get_csrf_token(self):
        """Test CSRF token retrieval."""
        token = "test_token_123"
        mock_session = {'csrf_token': token}
        
        with patch('web.session_security.session', mock_session):
            assert CSRFProtection.get_csrf_token() == token


class TestRateLimitManager:
    """Test rate limiting functionality."""
    
    def test_rate_limit_within_limits(self):
        """Test rate limiting within allowed limits."""
        rate_limit_manager.attempts = {}  # Reset
        
        # Should allow first few attempts
        for i in range(3):
            assert rate_limit_manager.check_rate_limit('test_user', max_attempts=5) is True
    
    def test_rate_limit_exceeded(self):
        """Test rate limiting when exceeded."""
        rate_limit_manager.attempts = {}  # Reset
        
        # Fill up the attempts
        for i in range(5):
            rate_limit_manager.check_rate_limit('test_user', max_attempts=5)
        
        # Next attempt should be blocked
        assert rate_limit_manager.check_rate_limit('test_user', max_attempts=5) is False
    
    def test_rate_limit_reset(self):
        """Test rate limit reset."""
        rate_limit_manager.attempts = {'test_user': []}
        rate_limit_manager.reset_attempts('test_user')
        
        assert 'test_user' not in rate_limit_manager.attempts


@pytest.fixture
def app():
    """Create test Flask app."""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test_secret_key'
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False  # Disable for testing
    
    return app


class TestFormValidationDecorator:
    """Test form validation decorator."""
    
    def test_form_validation_success(self, app):
        """Test successful form validation."""
        validation_rules = {
            'username': {'type': 'username', 'required': True},
            'display_name': {'type': 'display_name', 'required': False}
        }
        
        @validate_form_input(validation_rules)
        def test_route():
            return "success"
        
        with app.test_request_context('/test', method='POST', data={
            'username': 'test_user',
            'display_name': 'Test User'
        }):
            result = test_route()
            assert result == "success"
            assert hasattr(request, 'validated_data')
            assert request.validated_data['username'] == 'test_user'
    
    def test_form_validation_failure(self, app):
        """Test form validation failure."""
        validation_rules = {
            'username': {'type': 'username', 'required': True}
        }
        
        @validate_form_input(validation_rules)
        def test_route():
            return "success"
        
        with app.test_request_context('/test', method='POST', data={}):
            with patch('flask.flash') as mock_flash:
                with patch('flask.redirect') as mock_redirect:
                    test_route()
                    mock_flash.assert_called_once()
                    mock_redirect.assert_called_once()


class TestAPIValidationDecorator:
    """Test API validation decorator."""
    
    def test_api_validation_success(self, app):
        """Test successful API validation."""
        validation_rules = {
            'username': {'type': 'username', 'required': True}
        }
        
        @validate_api_input(validation_rules)
        def test_api():
            return {"status": "success"}
        
        with app.test_request_context('/api/test', method='POST', 
                                    json={'username': 'test_user'},
                                    content_type='application/json'):
            result = test_api()
            assert result == {"status": "success"}
    
    def test_api_validation_missing_json(self, app):
        """Test API validation with missing JSON."""
        validation_rules = {
            'username': {'type': 'username', 'required': True}
        }
        
        @validate_api_input(validation_rules)
        def test_api():
            return {"status": "success"}
        
        with app.test_request_context('/api/test', method='POST'):
            result = test_api()
            assert result[1] == 400  # Should return 400 status code


if __name__ == '__main__':
    pytest.main([__file__])