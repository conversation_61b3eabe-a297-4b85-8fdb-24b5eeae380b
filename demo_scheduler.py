#!/usr/bin/env python3
"""
Demo script for testing the scheduler functionality.

This script demonstrates the basic scheduler operations including
job scheduling, management, and persistence.
"""

import logging
import time
from datetime import datetime, timedelta
from pathlib import Path

from config import Config
from services.scheduler import SchedulerService
from services.scheduler_manager import SchedulerManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def demo_monitoring_callback():
    """Mock monitoring callback for testing."""
    logger.info("Executing monitoring callback...")
    time.sleep(1)  # Simulate work
    return {
        'success': True,
        'message': 'Demo monitoring completed',
        'profiles_processed': 2,
        'changes_detected': 3
    }


def demo_scheduler_service():
    """Demonstrate SchedulerService functionality."""
    logger.info("=== SchedulerService Demo ===")
    
    # Create config
    config = Config()
    
    # Create scheduler service
    scheduler = SchedulerService(config)
    
    try:
        # Set callback and start
        scheduler.set_monitoring_callback(demo_monitoring_callback)
        
        logger.info("Starting scheduler...")
        if not scheduler.start():
            logger.error("Failed to start scheduler")
            return
        
        # Schedule a job
        logger.info("Scheduling monitoring job...")
        success = scheduler.schedule_monitoring_job(
            job_id='demo_job',
            interval_hours=1
        )
        
        if success:
            logger.info("Job scheduled successfully")
            
            # Get job info
            job_info = scheduler.get_job_info('demo_job')
            logger.info(f"Job info: {job_info}")
            
            # Get all jobs
            all_jobs = scheduler.get_all_jobs()
            logger.info(f"Total jobs: {len(all_jobs)}")
            
            # Get scheduler status
            status = scheduler.get_scheduler_status()
            logger.info(f"Scheduler status: {status}")
            
        else:
            logger.error("Failed to schedule job")
        
        # Wait a bit
        logger.info("Waiting 5 seconds...")
        time.sleep(5)
        
    finally:
        # Stop scheduler
        logger.info("Stopping scheduler...")
        scheduler.stop()
        logger.info("Scheduler stopped")


def demo_scheduler_manager():
    """Demonstrate SchedulerManager functionality."""
    logger.info("=== SchedulerManager Demo ===")
    
    # Create config
    config = Config()
    
    # Create mock monitoring service
    class MockMonitoringService:
        def start_monitoring_cycle(self):
            return demo_monitoring_callback()
        
        def cleanup_old_data(self):
            return {
                'success': True,
                'cleanup_stats': {'deleted_records': 5}
            }
    
    monitoring_service = MockMonitoringService()
    
    # Create scheduler manager
    manager = SchedulerManager(config)
    
    try:
        # Initialize
        logger.info("Initializing scheduler manager...")
        if not manager.initialize(monitoring_service):
            logger.error("Failed to initialize scheduler manager")
            return
        
        # Start periodic monitoring
        logger.info("Starting periodic monitoring...")
        result = manager.start_periodic_monitoring(interval_hours=1)
        logger.info(f"Start result: {result}")
        
        # Get status
        status = manager.get_monitoring_schedule_status()
        logger.info(f"Schedule status: {status}")
        
        # Run monitoring now
        logger.info("Running monitoring immediately...")
        result = manager.run_monitoring_now()
        logger.info(f"Immediate run result: {result}")
        
        # Wait a bit
        logger.info("Waiting 10 seconds...")
        time.sleep(10)
        
        # Update interval
        logger.info("Updating monitoring interval...")
        result = manager.update_monitoring_interval(2)
        logger.info(f"Update result: {result}")
        
        # Get next monitoring time
        next_time = manager.get_next_monitoring_time()
        logger.info(f"Next monitoring time: {next_time}")
        
    finally:
        # Shutdown
        logger.info("Shutting down scheduler manager...")
        manager.shutdown()
        logger.info("Scheduler manager shutdown complete")


def demo_job_persistence():
    """Demonstrate job persistence across restarts."""
    logger.info("=== Job Persistence Demo ===")
    
    config = Config()
    
    # First scheduler instance
    logger.info("Creating first scheduler instance...")
    scheduler1 = SchedulerService(config)
    scheduler1.set_monitoring_callback(demo_monitoring_callback)
    
    try:
        scheduler1.start()
        
        # Schedule persistent job
        logger.info("Scheduling persistent job...")
        scheduler1.schedule_monitoring_job(job_id='persistent_job', interval_hours=2)
        
        # Verify job exists
        if scheduler1.job_exists('persistent_job'):
            logger.info("Persistent job created successfully")
        else:
            logger.error("Failed to create persistent job")
            return
        
        # Stop first scheduler
        logger.info("Stopping first scheduler...")
        scheduler1.stop()
        
    except Exception as e:
        logger.error(f"Error with first scheduler: {e}")
        scheduler1.stop()
        return
    
    # Wait a moment
    time.sleep(2)
    
    # Second scheduler instance (simulating restart)
    logger.info("Creating second scheduler instance (simulating restart)...")
    scheduler2 = SchedulerService(config)
    scheduler2.set_monitoring_callback(demo_monitoring_callback)
    
    try:
        scheduler2.start()
        
        # Check if job persisted
        if scheduler2.job_exists('persistent_job'):
            logger.info("Job persistence verified - job exists after restart!")
            
            # Get job info
            job_info = scheduler2.get_job_info('persistent_job')
            logger.info(f"Persistent job info: {job_info}")
            
        else:
            logger.warning("Job did not persist across restart")
        
    finally:
        scheduler2.stop()


def main():
    """Run all demos."""
    logger.info("Starting scheduler demos...")
    
    try:
        # Demo 1: Basic scheduler service
        demo_scheduler_service()
        
        print("\n" + "="*50 + "\n")
        
        # Demo 2: Scheduler manager
        demo_scheduler_manager()
        
        print("\n" + "="*50 + "\n")
        
        # Demo 3: Job persistence
        demo_job_persistence()
        
        logger.info("All demos completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()