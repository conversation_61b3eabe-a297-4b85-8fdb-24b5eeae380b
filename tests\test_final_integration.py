"""
Final integration and system testing for Instagram Follower Monitor.

This module provides comprehensive integration tests that validate:
- Complete monitoring workflow
- Load testing with multiple profiles
- Error recovery scenarios and system resilience
- Security measures and credential protection
- User acceptance testing for dashboard functionality

Requirements covered: 1.1-1.5, 2.1-2.5, 3.1-3.5, 4.1-4.5, 5.1-5.5, 6.1-6.5, 7.1-7.5
"""

import pytest
import tempfile
import os
import json
import time
import threading
import sqlite3
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from flask import Flask
from concurrent.futures import ThreadPoolExecutor, as_completed

from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, ChangeRepository, FollowerRepository, SettingsRepository
)
from models.data_models import (
    MonitoringConfig, FollowerChange, ChangeType, ProfileInfo
)
from services.monitoring_service import MonitoringService
from services.change_detector import ChangeDetector
from services.data_processor import DataProcessor
from services.profile_scanner import ProfileScanner
from services.authentication import AuthenticationManager
from services.instagram_client import InstagramClient
from services.scheduler_manager import SchedulerManager
from web.app import create_app
from web.api import api_v1


class TestCompleteMonitoringWorkflow:
    """Test complete end-to-end monitoring workflow."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database for testing."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        db_manager = DatabaseManager(temp_file.name)
        db_manager.initialize_schema()
        
        yield temp_file.name
        
        os.unlink(temp_file.name)
    
    @pytest.fixture
    def monitoring_components(self, temp_db):
        """Create monitoring service components."""
        profile_repo = ProfileRepository(temp_db)
        change_repo = ChangeRepository(temp_db)
        follower_repo = FollowerRepository(temp_db)
        settings_repo = SettingsRepository(temp_db)
        
        # Mock Instagram client
        mock_client = Mock(spec=InstagramClient)
        mock_client.is_authenticated.return_value = True
        
        # Create service components
        change_detector = ChangeDetector()
        data_processor = DataProcessor(
            profile_repo, change_repo, follower_repo, settings_repo
        )
        profile_scanner = ProfileScanner(mock_client, follower_repo)
        monitoring_service = MonitoringService(
            profile_repo, change_detector, data_processor, profile_scanner
        )
        
        return {
            'monitoring_service': monitoring_service,
            'profile_repo': profile_repo,
            'change_repo': change_repo,
            'follower_repo': follower_repo,
            'settings_repo': settings_repo,
            'mock_client': mock_client,
            'change_detector': change_detector,
            'data_processor': data_processor,
            'profile_scanner': profile_scanner
        }    def
 test_complete_monitoring_workflow(self, monitoring_components):
        """Test complete monitoring workflow from profile creation to change detection.
        
        Requirements: 1.1, 1.2, 1.3, 1.4, 4.1, 4.2, 4.3
        """
        components = monitoring_components
        monitoring_service = components['monitoring_service']
        profile_repo = components['profile_repo']
        change_repo = components['change_repo']
        follower_repo = components['follower_repo']
        mock_client = components['mock_client']
        
        # Step 1: Create test profile
        config = MonitoringConfig(
            profile_username="test_workflow_user",
            display_name="Test Workflow User",
            enabled=True,
            interval_hours=2
        )
        profile_id = profile_repo.create_profile(config)
        assert profile_id is not None
        
        # Step 2: Mock initial Instagram data
        initial_followers = {"follower1", "follower2", "follower3", "follower4"}
        initial_following = {"following1", "following2", "following3"}
        
        mock_client.get_followers.return_value = initial_followers
        mock_client.get_following.return_value = initial_following
        mock_client.get_profile_info.return_value = ProfileInfo(
            username="test_workflow_user",
            display_name="Test Workflow User",
            follower_count=len(initial_followers),
            following_count=len(initial_following),
            is_private=False,
            last_updated=datetime.now()
        )
        
        # Step 3: Perform initial scan
        result = monitoring_service.scan_profile("test_workflow_user")
        assert result['success'] is True
        assert result['changes_detected'] == 0  # No changes on initial scan
        
        # Verify initial data stored
        stored_followers = follower_repo.get_current_followers(profile_id)
        stored_following = follower_repo.get_current_following(profile_id)
        assert stored_followers == initial_followers
        assert stored_following == initial_following
        
        # Step 4: Simulate changes in Instagram data
        updated_followers = {"follower1", "follower2", "follower5", "follower6"}  # Lost 3,4 gained 5,6
        updated_following = {"following1", "following4"}  # Lost 2,3 gained 4
        
        mock_client.get_followers.return_value = updated_followers
        mock_client.get_following.return_value = updated_following
        mock_client.get_profile_info.return_value = ProfileInfo(
            username="test_workflow_user",
            display_name="Test Workflow User",
            follower_count=len(updated_followers),
            following_count=len(updated_following),
            is_private=False,
            last_updated=datetime.now()
        )
        
        # Step 5: Perform second scan to detect changes
        result = monitoring_service.scan_profile("test_workflow_user")
        assert result['success'] is True
        assert result['changes_detected'] > 0
        
        # Step 6: Verify changes were detected and stored
        changes = change_repo.get_changes_for_profile(profile_id)
        assert len(changes) == 6  # 2 lost followers + 2 gained followers + 2 lost following + 0 gained following
        
        # Verify specific changes
        change_types = {change.change_type for change in changes}
        assert ChangeType.GAINED in change_types
        assert ChangeType.LOST in change_types
        assert ChangeType.STOPPED_FOLLOWING in change_types
        
        # Verify current data updated
        current_followers = follower_repo.get_current_followers(profile_id)
        current_following = follower_repo.get_current_following(profile_id)
        assert current_followers == updated_followers
        assert current_following == updated_following
        
        # Step 7: Test monitoring statistics
        stats = change_repo.get_change_statistics(profile_id=profile_id, days=1)
        assert stats['total_changes'] == 6
        assert stats['followers_gained'] == 2
        assert stats['followers_lost'] == 2
        assert stats['following_stopped'] == 2    def 
test_monitoring_with_authentication_flow(self, monitoring_components):
        """Test monitoring workflow with Instagram authentication.
        
        Requirements: 2.1, 2.2, 2.3, 2.4, 2.5
        """
        components = monitoring_components
        monitoring_service = components['monitoring_service']
        profile_repo = components['profile_repo']
        mock_client = components['mock_client']
        
        # Create private profile that requires authentication
        config = MonitoringConfig(
            profile_username="private_user",
            display_name="Private User",
            enabled=True,
            is_private=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Test authentication failure scenario
        mock_client.is_authenticated.return_value = False
        mock_client.authenticate.return_value = False
        
        result = monitoring_service.scan_profile("private_user")
        assert result['success'] is False
        assert 'authentication' in result['error'].lower()
        
        # Test successful authentication
        mock_client.is_authenticated.return_value = True
        mock_client.authenticate.return_value = True
        mock_client.get_followers.return_value = {"private_follower1", "private_follower2"}
        mock_client.get_following.return_value = {"private_following1"}
        mock_client.get_profile_info.return_value = ProfileInfo(
            username="private_user",
            display_name="Private User",
            follower_count=2,
            following_count=1,
            is_private=True,
            last_updated=datetime.now()
        )
        
        result = monitoring_service.scan_profile("private_user")
        assert result['success'] is True
        
        # Verify data was stored for private profile
        stored_followers = follower_repo.get_current_followers(profile_id)
        assert len(stored_followers) == 2
    
    def test_error_recovery_scenarios(self, monitoring_components):
        """Test system resilience and error recovery scenarios.
        
        Requirements: 3.1, 3.2, 3.3, 3.4, 3.5
        """
        components = monitoring_components
        monitoring_service = components['monitoring_service']
        profile_repo = components['profile_repo']
        mock_client = components['mock_client']
        
        # Create test profile
        config = MonitoringConfig(
            profile_username="error_test_user",
            display_name="Error Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        
        # Test rate limiting error (429)
        from services.exceptions import RateLimitError
        mock_client.get_followers.side_effect = RateLimitError("Rate limited")
        
        result = monitoring_service.scan_profile("error_test_user")
        assert result['success'] is False
        assert 'rate limit' in result['error'].lower()
        
        # Test network error recovery
        from services.exceptions import NetworkError
        mock_client.get_followers.side_effect = NetworkError("Network timeout")
        
        result = monitoring_service.scan_profile("error_test_user")
        assert result['success'] is False
        assert 'network' in result['error'].lower()
        
        # Test recovery after errors
        mock_client.get_followers.side_effect = None
        mock_client.get_followers.return_value = {"recovered_follower1"}
        mock_client.get_following.return_value = {"recovered_following1"}
        mock_client.get_profile_info.return_value = ProfileInfo(
            username="error_test_user",
            display_name="Error Test User",
            follower_count=1,
            following_count=1,
            is_private=False,
            last_updated=datetime.now()
        )
        
        result = monitoring_service.scan_profile("error_test_user")
        assert result['success'] is True
        
        # Verify system recovered and stored data
        stored_followers = follower_repo.get_current_followers(profile_id)
        assert "recovered_follower1" in stored_followers