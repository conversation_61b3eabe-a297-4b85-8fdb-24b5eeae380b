{% extends "base.html" %}

{% block title %}Internal Server Error - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Internal Server Error (500)
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-server fa-5x text-danger"></i>
                    </div>
                    
                    <h5 class="card-title">Something went wrong on our end</h5>
                    <p class="card-text text-muted">
                        We're experiencing some technical difficulties. Our team has been notified and is working to fix the issue.
                    </p>
                    
                    <div class="mt-4">
                        <h6>What you can try:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-refresh me-2"></i>Refresh the page in a few minutes</li>
                            <li><i class="fas fa-home me-2"></i>Go back to the dashboard</li>
                            <li><i class="fas fa-bug me-2"></i>Check the system logs for more details</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                        <button onclick="location.reload()" class="btn btn-outline-primary ms-2">
                            <i class="fas fa-refresh me-2"></i>
                            Try Again
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}