{% extends "base.html" %}

{% block title %}Changes - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active">Changes</li>
            </ol>
        </nav>
        
        <h1 class="h2 mb-0">
            <i class="bi bi-activity"></i> Recent Changes
        </h1>
        <p class="text-muted">Track follower and following changes across all monitored profiles</p>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <form method="GET" id="filterForm" class="row g-3">
                    <div class="col-md-12">
                        <label for="search" class="form-label">Search Users</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="search" name="q" 
                                   placeholder="Search by username..." 
                                   value="{{ current_filters.q or '' }}">
                            <button type="button" class="btn btn-outline-secondary" id="clearSearch">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="profile" class="form-label">Profile</label>
                        <select class="form-select" id="profile" name="profile">
                            <option value="">All Profiles</option>
                            {% for profile in profiles %}
                            <option value="{{ profile.profile_username }}" 
                                    {% if current_filters.profile == profile.profile_username %}selected{% endif %}>
                                @{{ profile.profile_username }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="days" class="form-label">Time Period</label>
                        <select class="form-select" id="days" name="days">
                            <option value="1" {% if current_filters.days == 1 %}selected{% endif %}>Last 24 hours</option>
                            <option value="3" {% if current_filters.days == 3 %}selected{% endif %}>Last 3 days</option>
                            <option value="7" {% if current_filters.days == 7 %}selected{% endif %}>Last week</option>
                            <option value="30" {% if current_filters.days == 30 %}selected{% endif %}>Last month</option>
                            <option value="90" {% if current_filters.days == 90 %}selected{% endif %}>Last 3 months</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">Change Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option value="gained" {% if current_filters.type == 'gained' %}selected{% endif %}>Followers Gained</option>
                            <option value="lost" {% if current_filters.type == 'lost' %}selected{% endif %}>Followers Lost</option>
                            <option value="started_following" {% if current_filters.type == 'started_following' %}selected{% endif %}>Started Following</option>
                            <option value="stopped_following" {% if current_filters.type == 'stopped_following' %}selected{% endif %}>Stopped Following</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sort_by" class="form-label">Sort By</label>
                        <div class="input-group">
                            <select class="form-select" id="sort_by" name="sort_by">
                                <option value="timestamp" {% if current_filters.sort_by == 'timestamp' %}selected{% endif %}>Date</option>
                                <option value="username" {% if current_filters.sort_by == 'username' %}selected{% endif %}>Username</option>
                                <option value="profile" {% if current_filters.sort_by == 'profile' %}selected{% endif %}>Profile</option>
                            </select>
                            <button type="button" class="btn btn-outline-secondary" id="toggleSort" 
                                    data-order="{{ current_filters.sort_order or 'desc' }}">
                                <i class="bi bi-sort-{{ 'up' if current_filters.sort_order == 'asc' else 'down' }}"></i>
                            </button>
                        </div>
                        <input type="hidden" id="sort_order" name="sort_order" value="{{ current_filters.sort_order or 'desc' }}">
                    </div>
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-funnel"></i> Apply Filters
                            </button>
                            <a href="{{ url_for('main.changes') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Clear All
                            </a>
                            <button type="button" class="btn btn-outline-info" id="liveSearch">
                                <i class="bi bi-lightning"></i> Live Search
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Changes List -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    Changes 
                    {% if current_filters.profile %}for @{{ current_filters.profile }}{% endif %}
                    {% if current_filters.q %}- "{{ current_filters.q }}"{% endif %}
                    {% if current_filters.days %}({{ current_filters.days }} day{{ 's' if current_filters.days != 1 else '' }}){% endif %}
                </h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-primary">
                        {{ pagination.total_count }} result{{ 's' if pagination.total_count != 1 else '' }}
                    </span>
                    {% if pagination.total_pages > 1 %}
                    <span class="text-muted small">
                        Page {{ pagination.page }} of {{ pagination.total_pages }}
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body" id="changesContainer">
                {% if changes %}
                <div class="timeline">
                    {% for change in changes %}
                    <div class="timeline-item change-item change-{{ change.change_type.value.replace('_', '-') }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="fw-medium">
                                    {{ change|format_change }}
                                </div>
                                <div class="d-flex align-items-center gap-3 mt-1">
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> {{ change.timestamp|timeago }}
                                    </small>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar"></i> {{ change.timestamp.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                    <a href="{{ url_for('main.profile_detail', username=change.profile_username) }}" 
                                       class="text-decoration-none small">
                                        <i class="bi bi-person"></i> View Profile
                                    </a>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-secondary">{{ change.change_type.value.replace('_', ' ').title() }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if pagination.total_pages > 1 %}
                <nav aria-label="Changes pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <!-- Previous page -->
                        <li class="page-item {% if pagination.page <= 1 %}disabled{% endif %}">
                            <a class="page-link" href="{% if pagination.page > 1 %}{{ url_for('main.changes', page=pagination.page-1, **current_filters) }}{% else %}#{% endif %}">
                                <i class="bi bi-chevron-left"></i> Previous
                            </a>
                        </li>
                        
                        <!-- Page numbers -->
                        {% set start_page = [1, pagination.page - 2]|max %}
                        {% set end_page = [pagination.total_pages, pagination.page + 2]|min %}
                        
                        {% if start_page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.changes', page=1, **current_filters) }}">1</a>
                        </li>
                        {% if start_page > 2 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                        {% endif %}
                        
                        {% for page_num in range(start_page, end_page + 1) %}
                        <li class="page-item {% if page_num == pagination.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('main.changes', page=page_num, **current_filters) }}">{{ page_num }}</a>
                        </li>
                        {% endfor %}
                        
                        {% if end_page < pagination.total_pages %}
                        {% if end_page < pagination.total_pages - 1 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.changes', page=pagination.total_pages, **current_filters) }}">{{ pagination.total_pages }}</a>
                        </li>
                        {% endif %}
                        
                        <!-- Next page -->
                        <li class="page-item {% if pagination.page >= pagination.total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{% if pagination.page < pagination.total_pages %}{{ url_for('main.changes', page=pagination.page+1, **current_filters) }}{% else %}#{% endif %}">
                                Next <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="bi bi-search fs-1"></i>
                    <h4 class="mt-3">No Changes Found</h4>
                    <p>No changes match your current filters.</p>
                    <div class="mt-3">
                        <a href="{{ url_for('main.changes') }}" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-clockwise"></i> View All Changes
                        </a>
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-house"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
{% if changes %}
<div class="row mt-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i> Summary Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="stat-number text-success">
                            {{ changes|selectattr('change_type.value', 'equalto', 'gained')|list|length }}
                        </div>
                        <div class="stat-label">Followers Gained</div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-number text-danger">
                            {{ changes|selectattr('change_type.value', 'equalto', 'lost')|list|length }}
                        </div>
                        <div class="stat-label">Followers Lost</div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-number text-primary">
                            {{ changes|selectattr('change_type.value', 'equalto', 'started_following')|list|length }}
                        </div>
                        <div class="stat-label">Started Following</div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-number text-warning">
                            {{ changes|selectattr('change_type.value', 'equalto', 'stopped_following')|list|length }}
                        </div>
                        <div class="stat-label">Stopped Following</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
    let searchTimeout;
    let liveSearchEnabled = false;
    
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('filterForm');
        const searchInput = document.getElementById('search');
        const clearSearchBtn = document.getElementById('clearSearch');
        const toggleSortBtn = document.getElementById('toggleSort');
        const sortOrderInput = document.getElementById('sort_order');
        const liveSearchBtn = document.getElementById('liveSearch');
        const changesContainer = document.getElementById('changesContainer');
        
        // Clear search functionality
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            if (liveSearchEnabled) {
                performSearch();
            }
        });
        
        // Sort toggle functionality
        toggleSortBtn.addEventListener('click', function() {
            const currentOrder = this.dataset.order;
            const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
            
            this.dataset.order = newOrder;
            sortOrderInput.value = newOrder;
            
            const icon = this.querySelector('i');
            icon.className = `bi bi-sort-${newOrder === 'asc' ? 'up' : 'down'}`;
            
            if (liveSearchEnabled) {
                performSearch();
            }
        });
        
        // Live search toggle
        liveSearchBtn.addEventListener('click', function() {
            liveSearchEnabled = !liveSearchEnabled;
            
            if (liveSearchEnabled) {
                this.innerHTML = '<i class="bi bi-lightning-fill"></i> Live Search ON';
                this.classList.remove('btn-outline-info');
                this.classList.add('btn-info');
                
                // Enable live search on input
                searchInput.addEventListener('input', handleSearchInput);
                
                // Enable live filtering on selects
                const selects = form.querySelectorAll('select');
                selects.forEach(select => {
                    select.addEventListener('change', performSearch);
                });
            } else {
                this.innerHTML = '<i class="bi bi-lightning"></i> Live Search';
                this.classList.remove('btn-info');
                this.classList.add('btn-outline-info');
                
                // Disable live search
                searchInput.removeEventListener('input', handleSearchInput);
                
                const selects = form.querySelectorAll('select');
                selects.forEach(select => {
                    select.removeEventListener('change', performSearch);
                });
            }
        });
        
        // Handle search input with debouncing
        function handleSearchInput() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(performSearch, 300);
        }
        
        // Perform AJAX search
        function performSearch() {
            const formData = new FormData(form);
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }
            
            // Show loading state
            changesContainer.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Searching changes...</p>
                </div>
            `;
            
            // Make AJAX request
            fetch(`/api/search/changes?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    updateChangesDisplay(data);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    changesContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            Error loading changes: ${error.message}
                        </div>
                    `;
                });
        }
        
        // Update changes display with AJAX results
        function updateChangesDisplay(data) {
            if (data.changes.length === 0) {
                changesContainer.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-search fs-1"></i>
                        <h4 class="mt-3">No Changes Found</h4>
                        <p>No changes match your current filters.</p>
                        <div class="mt-3">
                            <a href="/changes" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-clockwise"></i> View All Changes
                            </a>
                            <a href="/" class="btn btn-outline-secondary">
                                <i class="bi bi-house"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                `;
                return;
            }
            
            let html = '<div class="timeline">';
            
            data.changes.forEach(change => {
                const changeClass = change.change_type.replace('_', '-');
                const changeTypeLabel = change.change_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                const timestamp = new Date(change.timestamp);
                const timeAgo = formatTimeAgo(timestamp);
                const formattedDate = timestamp.toLocaleString();
                
                html += `
                    <div class="timeline-item change-item change-${changeClass}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="fw-medium">
                                    ${change.display_text}
                                </div>
                                <div class="d-flex align-items-center gap-3 mt-1">
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> ${timeAgo}
                                    </small>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar"></i> ${formattedDate}
                                    </small>
                                    <a href="/profile/${change.profile_username}" 
                                       class="text-decoration-none small">
                                        <i class="bi bi-person"></i> View Profile
                                    </a>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-secondary">${changeTypeLabel}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            // Add pagination info
            if (data.pagination.total_pages > 1) {
                html += `
                    <div class="mt-4 text-center">
                        <p class="text-muted">
                            Showing ${data.changes.length} of ${data.pagination.total_count} results
                            (Page ${data.pagination.page} of ${data.pagination.total_pages})
                        </p>
                        <p class="small text-muted">
                            Use the form above to navigate pages or disable live search for full pagination.
                        </p>
                    </div>
                `;
            }
            
            changesContainer.innerHTML = html;
        }
        
        // Format time ago helper
        function formatTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) {
                return 'Just now';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} day${days !== 1 ? 's' : ''} ago`;
            }
        }
        
        // Auto-submit form when not in live search mode
        const selects = form.querySelectorAll('select');
        selects.forEach(select => {
            select.addEventListener('change', function() {
                if (!liveSearchEnabled) {
                    setTimeout(() => {
                        form.submit();
                    }, 100);
                }
            });
        });
    });
    
    // Page-specific refresh function
    function pageRefresh() {
        return new Promise((resolve) => {
            if (liveSearchEnabled) {
                performSearch();
                setTimeout(resolve, 1000);
            } else {
                location.reload();
            }
        });
    }
</script>
{% endblock %}