# Disaster Recovery Procedures for Instagram Follower Monitor

## Overview
This document outlines the procedures for recovering from various disaster scenarios
that may affect the Instagram Follower Monitor application.

## Backup Strategy
- **Full Backups**: Complete system backup including database, configuration, and data
- **Incremental Backups**: Changes since last backup for efficient storage
- **Automated Backups**: Scheduled backups with configurable retention
- **Encryption**: All backups are encrypted using Fernet symmetric encryption

## Recovery Scenarios

### 1. Database Corruption
**Symptoms**: SQLite integrity check failures, application crashes, data inconsistencies

**Recovery Steps**:
1. Stop the application immediately
2. Run database integrity validation: `backup_manager.validate_database_integrity()`
3. If corruption is confirmed, restore from latest backup:
   ```python
   backup_manager.restore_from_backup('path/to/backup.backup', {'database': True})
   ```
4. Verify restored database integrity
5. Restart application and monitor for issues

### 2. Complete Data Loss
**Symptoms**: Database file missing, storage device failure

**Recovery Steps**:
1. Identify the most recent full backup
2. Restore complete system:
   ```python
   backup_manager.restore_from_backup('path/to/backup.backup')
   ```
3. Verify all components are restored correctly
4. Update configuration if necessary
5. Restart monitoring services

### 3. Configuration Loss
**Symptoms**: Settings reset to defaults, authentication failures

**Recovery Steps**:
1. Restore configuration from backup:
   ```python
   backup_manager.restore_from_backup('path/to/backup.backup', {'configuration': True})
   ```
2. Verify Instagram credentials are restored
3. Check monitoring intervals and other settings
4. Restart application

### 4. Partial Data Loss (Profile-Specific)
**Symptoms**: Missing data for specific profiles

**Recovery Steps**:
1. Export profile data from backup if available
2. Import specific profile data:
   ```python
   backup_manager.import_profile_data('profile_export.json')
   ```
3. Verify data integrity for affected profiles
4. Resume monitoring for restored profiles

## Prevention Measures

### Regular Backups
- Enable automated backups with appropriate frequency
- Test backup restoration procedures regularly
- Monitor backup job success/failure
- Maintain multiple backup copies in different locations

### Monitoring
- Set up database integrity checks
- Monitor disk space and storage health
- Log all critical operations
- Implement alerting for backup failures

### Best Practices
- Keep backup encryption keys secure and separate from backups
- Document all configuration changes
- Test disaster recovery procedures in non-production environment
- Maintain offline copies of critical documentation

## Emergency Contacts and Resources
- Application logs: `logs/` directory
- Backup location: `backups/` directory
- Configuration files: `.env`, `config.py`
- Database file: `instagram_monitor.db`

## Recovery Validation Checklist
After any recovery operation, verify:
- [ ] Database integrity check passes
- [ ] All profiles are present and configured correctly
- [ ] Recent changes data is available
- [ ] Authentication credentials work
- [ ] Monitoring jobs are scheduled and running
- [ ] Web interface is accessible
- [ ] API endpoints respond correctly
- [ ] Logs show normal operation

## Backup File Formats
- **Full Backup**: Encrypted tar.gz containing database, configuration, and data export
- **Profile Export**: JSON file with complete profile data and history
- **Configuration Backup**: JSON file with all system settings

## Recovery Time Objectives (RTO)
- Database corruption: 15-30 minutes
- Complete data loss: 30-60 minutes
- Configuration loss: 5-15 minutes
- Profile-specific loss: 10-20 minutes

## Recovery Point Objectives (RPO)
- With daily backups: Up to 24 hours of data loss
- With hourly backups: Up to 1 hour of data loss
- With real-time replication: Minimal data loss

For additional support or complex recovery scenarios, consult the application
documentation and logs for detailed error information.