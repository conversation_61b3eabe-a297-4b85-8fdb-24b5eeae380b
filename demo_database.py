#!/usr/bin/env python3
"""
Demo script to showcase the database schema and core data models functionality.
This script demonstrates the implementation of task 2.
"""

import os
from datetime import datetime, timedelta

from database import DatabaseManager, ProfileRepository, FollowerRepository, ChangeRepository, DataRetentionManager
from models import ProfileInfo, FollowerChange, MonitoringConfig, ChangeType


def main():
    """Demonstrate database functionality."""
    print("=== Instagram Follower Monitor Database Demo ===\n")
    
    # Initialize database
    db_path = "demo_instagram_monitor.db"
    if os.path.exists(db_path):
        os.unlink(db_path)
    
    # Update the global db_manager to use our demo database
    from database.connection import db_manager
    db_manager.db_path = db_path
    db_manager._ensure_database_exists()
    db_manager.migrate_schema()
    print(f"✓ Database initialized at: {db_path}")
    print(f"✓ Schema version: {db_manager.get_schema_version()}\n")
    
    # Initialize repositories
    profile_repo = ProfileRepository()
    follower_repo = FollowerRepository()
    change_repo = ChangeRepository()
    retention_manager = DataRetentionManager()
    
    # Demo 1: Create and manage profiles
    print("=== Demo 1: Profile Management ===")
    
    # Create monitoring configurations
    configs = [
        MonitoringConfig(
            profile_username="@TestUser1",
            display_name="Test User 1",
            enabled=True,
            is_private=False
        ),
        MonitoringConfig(
            profile_username="testuser2",
            display_name="Test User 2", 
            enabled=True,
            is_private=True
        ),
        MonitoringConfig(
            profile_username="DisabledUser",
            display_name="Disabled User",
            enabled=False
        )
    ]
    
    profile_ids = []
    for config in configs:
        profile_id = profile_repo.create_profile(config)
        profile_ids.append(profile_id)
        print(f"✓ Created profile: {config.profile_username} (ID: {profile_id})")
    
    # Retrieve profiles
    all_profiles = profile_repo.get_all_profiles()
    enabled_profiles = profile_repo.get_enabled_profiles()
    
    print(f"✓ Total profiles: {len(all_profiles)}")
    print(f"✓ Enabled profiles: {len(enabled_profiles)}")
    
    # Demo 2: Store follower/following data
    print("\n=== Demo 2: Follower/Following Data ===")
    
    test_profile_id = profile_ids[0]
    
    # Store current followers and following
    current_followers = {"follower1", "follower2", "follower3", "follower4"}
    current_following = {"following1", "following2", "following3"}
    
    follower_repo.store_current_followers(test_profile_id, current_followers)
    follower_repo.store_current_following(test_profile_id, current_following)
    
    print(f"✓ Stored {len(current_followers)} followers")
    print(f"✓ Stored {len(current_following)} following")
    
    # Retrieve and verify
    retrieved_followers = follower_repo.get_current_followers(test_profile_id)
    retrieved_following = follower_repo.get_current_following(test_profile_id)
    
    print(f"✓ Retrieved {len(retrieved_followers)} followers")
    print(f"✓ Retrieved {len(retrieved_following)} following")
    
    # Demo 3: Track changes
    print("\n=== Demo 3: Change Tracking ===")
    
    # Simulate some changes
    changes = [
        FollowerChange(
            profile_username="testuser1",
            affected_username="new_follower",
            change_type=ChangeType.GAINED,
            profile_id=test_profile_id
        ),
        FollowerChange(
            profile_username="testuser1", 
            affected_username="lost_follower",
            change_type=ChangeType.LOST,
            profile_id=test_profile_id
        ),
        FollowerChange(
            profile_username="testuser1",
            affected_username="new_following",
            change_type=ChangeType.STARTED_FOLLOWING,
            profile_id=test_profile_id
        ),
        FollowerChange(
            profile_username="testuser1",
            affected_username="stopped_following",
            change_type=ChangeType.STOPPED_FOLLOWING,
            profile_id=test_profile_id
        )
    ]
    
    change_repo.store_follower_changes(changes)
    print(f"✓ Stored {len(changes)} changes")
    
    # Retrieve recent changes
    recent_changes = change_repo.get_recent_changes(profile_id=test_profile_id, limit=10)
    print(f"✓ Retrieved {len(recent_changes)} recent changes:")
    
    for change in recent_changes:
        print(f"  - {change.change_type.value}: {change.affected_username} at {change.timestamp.strftime('%H:%M:%S')}")
    
    # Demo 4: Data validation
    print("\n=== Demo 4: Data Model Validation ===")
    
    try:
        # This should work
        valid_profile = ProfileInfo(
            username="@ValidUser",
            follower_count=100,
            following_count=50
        )
        print(f"✓ Valid profile created: {valid_profile.username}")
        
        # This should fail
        invalid_profile = ProfileInfo(
            username="",  # Empty username should fail
            follower_count=100
        )
    except ValueError as e:
        print(f"✓ Validation caught invalid data: {e}")
    
    try:
        # This should fail
        invalid_change = FollowerChange(
            profile_username="test",
            affected_username="user",
            change_type="invalid_type"  # Invalid change type
        )
    except ValueError as e:
        print(f"✓ Validation caught invalid change type: {e}")
    
    # Demo 5: Data retention
    print("\n=== Demo 5: Data Retention ===")
    
    # Get current data stats
    stats_before = retention_manager.get_data_size_stats()
    print(f"✓ Data stats before cleanup:")
    for key, value in stats_before.items():
        print(f"  - {key}: {value}")
    
    # Run cleanup (won't delete much since data is recent)
    cleanup_stats = retention_manager.cleanup_old_data()
    print(f"✓ Cleanup completed:")
    for key, value in cleanup_stats.items():
        print(f"  - {key}: {value}")
    
    # Demo 6: Profile updates
    print("\n=== Demo 6: Profile Updates ===")
    
    # Update a profile
    profile_to_update = profile_repo.get_profile_by_username("testuser1")
    if profile_to_update:
        profile_to_update.display_name = "Updated Test User 1"
        profile_to_update.last_scan = datetime.now()
        
        success = profile_repo.update_profile(profile_to_update)
        print(f"✓ Profile update {'successful' if success else 'failed'}")
        
        # Verify update
        updated_profile = profile_repo.get_profile_by_username("testuser1")
        print(f"✓ Updated display name: {updated_profile.display_name}")
        print(f"✓ Last scan: {updated_profile.last_scan}")
    
    print(f"\n=== Demo Complete ===")
    print(f"Database file: {db_path}")
    print("All database operations completed successfully!")


if __name__ == "__main__":
    main()