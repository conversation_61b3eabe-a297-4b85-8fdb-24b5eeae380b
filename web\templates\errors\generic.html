{% extends "base.html" %}

{% block title %}Error {{ error_code }} - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error {{ error_code }}
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-question-circle fa-5x text-secondary"></i>
                    </div>
                    
                    <h5 class="card-title">{{ error_message }}</h5>
                    <p class="card-text text-muted">
                        An unexpected error occurred. Please try again or contact support if the problem persists.
                    </p>
                    
                    <div class="mt-4">
                        <h6>What you can try:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-refresh me-2"></i>Refresh the page</li>
                            <li><i class="fas fa-arrow-left me-2"></i>Go back and try again</li>
                            <li><i class="fas fa-home me-2"></i>Return to the dashboard</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <button onclick="location.reload()" class="btn btn-primary">
                            <i class="fas fa-refresh me-2"></i>
                            Refresh Page
                        </button>
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}