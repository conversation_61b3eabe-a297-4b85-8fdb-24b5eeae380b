{% extends "base.html" %}

{% block title %}Search - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active">Search</li>
            </ol>
        </nav>
        
        <h1 class="h2 mb-0">
            <i class="bi bi-search"></i> Search Users & Changes
        </h1>
        <p class="text-muted">Find specific followers, following users, and track their changes</p>
    </div>
</div>

<!-- Search Interface -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-8">
                        <label for="searchQuery" class="form-label">Search Query</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control form-control-lg" id="searchQuery" 
                                   placeholder="Enter username to search..." autocomplete="off">
                            <button type="button" class="btn btn-primary" id="searchBtn">
                                <i class="bi bi-search"></i> Search
                            </button>
                        </div>
                        <div class="form-text">
                            Search for Instagram usernames in followers, following lists, and change history
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="searchType" class="form-label">Search In</label>
                        <select class="form-select form-select-lg" id="searchType">
                            <option value="both">All Data</option>
                            <option value="current">Current Users</option>
                            <option value="changes">Change History</option>
                        </select>
                    </div>
                </div>
                
                <!-- Advanced Filters (Initially Hidden) -->
                <div class="collapse mt-3" id="advancedFilters">
                    <hr>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="profileFilter" class="form-label">Profile</label>
                            <select class="form-select" id="profileFilter">
                                <option value="">All Profiles</option>
                                {% for profile in profiles %}
                                <option value="{{ profile.profile_username }}">@{{ profile.profile_username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="userTypeFilter" class="form-label">User Type</label>
                            <select class="form-select" id="userTypeFilter">
                                <option value="both">Followers & Following</option>
                                <option value="followers">Followers Only</option>
                                <option value="following">Following Only</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="changeTypeFilter" class="form-label">Change Type</label>
                            <select class="form-select" id="changeTypeFilter">
                                <option value="">All Changes</option>
                                <option value="gained">Followers Gained</option>
                                <option value="lost">Followers Lost</option>
                                <option value="started_following">Started Following</option>
                                <option value="stopped_following">Stopped Following</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" 
                            data-bs-target="#advancedFilters" aria-expanded="false">
                        <i class="bi bi-sliders"></i> Advanced Filters
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" id="clearFilters">
                        <i class="bi bi-x-circle"></i> Clear All
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
<div class="row" id="searchResults" style="display: none;">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Search Results</h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-primary" id="resultCount">0 results</span>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" id="sortByName">
                            <i class="bi bi-sort-alpha-down"></i> Name
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="sortByDate">
                            <i class="bi bi-sort-numeric-down"></i> Date
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body" id="resultsContainer">
                <!-- Results will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Loading State -->
<div class="row" id="loadingState" style="display: none;">
    <div class="col">
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Searching...</p>
        </div>
    </div>
</div>

<!-- Quick Search Suggestions -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightbulb"></i> Quick Search Tips
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Search Examples:</h6>
                        <ul class="list-unstyled">
                            <li><code>john</code> - Find users with "john" in username</li>
                            <li><code>_official</code> - Find users ending with "_official"</li>
                            <li><code>photo</code> - Find users with "photo" in username</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Search Features:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success"></i> Real-time search as you type</li>
                            <li><i class="bi bi-check-circle text-success"></i> Search across all profiles</li>
                            <li><i class="bi bi-check-circle text-success"></i> Filter by user type and changes</li>
                            <li><i class="bi bi-check-circle text-success"></i> Sort results by name or date</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    let searchTimeout;
    let currentPage = 1;
    let currentSort = { by: 'username', order: 'asc' };
    
    document.addEventListener('DOMContentLoaded', function() {
        const searchQuery = document.getElementById('searchQuery');
        const searchBtn = document.getElementById('searchBtn');
        const searchType = document.getElementById('searchType');
        const profileFilter = document.getElementById('profileFilter');
        const userTypeFilter = document.getElementById('userTypeFilter');
        const changeTypeFilter = document.getElementById('changeTypeFilter');
        const clearFiltersBtn = document.getElementById('clearFilters');
        const sortByNameBtn = document.getElementById('sortByName');
        const sortByDateBtn = document.getElementById('sortByDate');
        
        const searchResults = document.getElementById('searchResults');
        const loadingState = document.getElementById('loadingState');
        const resultsContainer = document.getElementById('resultsContainer');
        const resultCount = document.getElementById('resultCount');
        
        // Search functionality
        searchQuery.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            if (this.value.trim().length >= 2) {
                searchTimeout = setTimeout(performSearch, 300);
            } else if (this.value.trim().length === 0) {
                hideResults();
            }
        });
        
        searchBtn.addEventListener('click', performSearch);
        
        searchQuery.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Filter change handlers
        [searchType, profileFilter, userTypeFilter, changeTypeFilter].forEach(element => {
            element.addEventListener('change', function() {
                if (searchQuery.value.trim().length >= 2) {
                    performSearch();
                }
            });
        });
        
        // Clear filters
        clearFiltersBtn.addEventListener('click', function() {
            searchQuery.value = '';
            searchType.value = 'both';
            profileFilter.value = '';
            userTypeFilter.value = 'both';
            changeTypeFilter.value = '';
            hideResults();
        });
        
        // Sort handlers
        sortByNameBtn.addEventListener('click', function() {
            setSortMode('username', this);
        });
        
        sortByDateBtn.addEventListener('click', function() {
            setSortMode('timestamp', this);
        });
        
        function setSortMode(sortBy, button) {
            // Toggle order if same sort type
            if (currentSort.by === sortBy) {
                currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.by = sortBy;
                currentSort.order = sortBy === 'username' ? 'asc' : 'desc';
            }
            
            // Update button states
            document.querySelectorAll('#searchResults .btn-group button').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            
            // Update icon
            const icon = button.querySelector('i');
            const isAsc = currentSort.order === 'asc';
            if (sortBy === 'username') {
                icon.className = `bi bi-sort-alpha-${isAsc ? 'down' : 'up'}`;
            } else {
                icon.className = `bi bi-sort-numeric-${isAsc ? 'down' : 'up'}`;
            }
            
            // Re-search with new sort
            if (searchQuery.value.trim().length >= 2) {
                performSearch();
            }
        }
        
        function performSearch() {
            const query = searchQuery.value.trim();
            if (query.length < 2) {
                hideResults();
                return;
            }
            
            showLoading();
            
            const searchTypeValue = searchType.value;
            let endpoint, params;
            
            if (searchTypeValue === 'current' || searchTypeValue === 'both') {
                endpoint = '/api/search/users';
                params = new URLSearchParams({
                    q: query,
                    type: userTypeFilter.value,
                    sort_by: currentSort.by,
                    sort_order: currentSort.order,
                    per_page: 50
                });
                
                if (profileFilter.value) {
                    params.append('profile', profileFilter.value);
                }
            } else {
                endpoint = '/api/search/changes';
                params = new URLSearchParams({
                    q: query,
                    sort_by: currentSort.by,
                    sort_order: currentSort.order,
                    per_page: 50
                });
                
                if (profileFilter.value) {
                    params.append('profile', profileFilter.value);
                }
                if (changeTypeFilter.value) {
                    params.append('type', changeTypeFilter.value);
                }
            }
            
            fetch(`${endpoint}?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    if (searchTypeValue === 'both') {
                        // Search both current users and changes
                        searchBoth(query);
                    } else {
                        displayResults(data, searchTypeValue);
                    }
                })
                .catch(error => {
                    console.error('Search error:', error);
                    showError(error.message);
                });
        }
        
        function searchBoth(query) {
            const promises = [];
            
            // Search current users
            const userParams = new URLSearchParams({
                q: query,
                type: userTypeFilter.value,
                sort_by: currentSort.by,
                sort_order: currentSort.order,
                per_page: 25
            });
            if (profileFilter.value) {
                userParams.append('profile', profileFilter.value);
            }
            promises.push(fetch(`/api/search/users?${userParams.toString()}`).then(r => r.json()));
            
            // Search changes
            const changeParams = new URLSearchParams({
                q: query,
                sort_by: currentSort.by,
                sort_order: currentSort.order,
                per_page: 25
            });
            if (profileFilter.value) {
                changeParams.append('profile', profileFilter.value);
            }
            if (changeTypeFilter.value) {
                changeParams.append('type', changeTypeFilter.value);
            }
            promises.push(fetch(`/api/search/changes?${changeParams.toString()}`).then(r => r.json()));
            
            Promise.all(promises)
                .then(([usersData, changesData]) => {
                    displayCombinedResults(usersData, changesData);
                })
                .catch(error => {
                    console.error('Combined search error:', error);
                    showError(error.message);
                });
        }
        
        function displayResults(data, type) {
            hideLoading();
            showResults();
            
            const items = type === 'current' ? data.users : data.changes;
            const totalCount = data.pagination.total_count;
            
            resultCount.textContent = `${totalCount} result${totalCount !== 1 ? 's' : ''}`;
            
            if (items.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-search fs-1"></i>
                        <h5 class="mt-3">No Results Found</h5>
                        <p>Try adjusting your search terms or filters.</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            
            if (type === 'current') {
                html = '<div class="list-group list-group-flush">';
                items.forEach(user => {
                    const userTypeIcon = user.user_type === 'follower' ? 'person-plus' : 'person-check';
                    const userTypeLabel = user.user_type === 'follower' ? 'Follower' : 'Following';
                    const addedDate = user.added_at ? new Date(user.added_at).toLocaleDateString() : 'Unknown';
                    
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">@${user.username}</h6>
                                    <p class="mb-1 text-muted">
                                        <i class="bi bi-${userTypeIcon}"></i> ${userTypeLabel} of @${user.profile_username}
                                    </p>
                                    <small class="text-muted">Added: ${addedDate}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">${userTypeLabel}</span>
                                    <br>
                                    <a href="/profile/${user.profile_username}" class="btn btn-sm btn-outline-primary mt-1">
                                        View Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
            } else {
                html = '<div class="timeline">';
                items.forEach(change => {
                    const changeClass = change.change_type.replace('_', '-');
                    const changeTypeLabel = change.change_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const timestamp = new Date(change.timestamp);
                    const timeAgo = formatTimeAgo(timestamp);
                    const formattedDate = timestamp.toLocaleString();
                    
                    html += `
                        <div class="timeline-item change-item change-${changeClass}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="fw-medium">${change.display_text}</div>
                                    <div class="d-flex align-items-center gap-3 mt-1">
                                        <small class="text-muted">
                                            <i class="bi bi-clock"></i> ${timeAgo}
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar"></i> ${formattedDate}
                                        </small>
                                        <a href="/profile/${change.profile_username}" class="text-decoration-none small">
                                            <i class="bi bi-person"></i> View Profile
                                        </a>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">${changeTypeLabel}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
            }
            
            resultsContainer.innerHTML = html;
        }
        
        function displayCombinedResults(usersData, changesData) {
            hideLoading();
            showResults();
            
            const totalUsers = usersData.pagination.total_count;
            const totalChanges = changesData.pagination.total_count;
            const totalCount = totalUsers + totalChanges;
            
            resultCount.textContent = `${totalCount} result${totalCount !== 1 ? 's' : ''} (${totalUsers} users, ${totalChanges} changes)`;
            
            if (totalCount === 0) {
                resultsContainer.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-search fs-1"></i>
                        <h5 class="mt-3">No Results Found</h5>
                        <p>Try adjusting your search terms or filters.</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            
            // Show current users section
            if (usersData.users.length > 0) {
                html += `
                    <h6 class="mb-3">
                        <i class="bi bi-people"></i> Current Users (${totalUsers})
                        ${totalUsers > usersData.users.length ? `<small class="text-muted">- showing first ${usersData.users.length}</small>` : ''}
                    </h6>
                    <div class="list-group list-group-flush mb-4">
                `;
                
                usersData.users.forEach(user => {
                    const userTypeIcon = user.user_type === 'follower' ? 'person-plus' : 'person-check';
                    const userTypeLabel = user.user_type === 'follower' ? 'Follower' : 'Following';
                    const addedDate = user.added_at ? new Date(user.added_at).toLocaleDateString() : 'Unknown';
                    
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">@${user.username}</h6>
                                    <p class="mb-1 text-muted">
                                        <i class="bi bi-${userTypeIcon}"></i> ${userTypeLabel} of @${user.profile_username}
                                    </p>
                                    <small class="text-muted">Added: ${addedDate}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">${userTypeLabel}</span>
                                    <br>
                                    <a href="/profile/${user.profile_username}" class="btn btn-sm btn-outline-primary mt-1">
                                        View Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
            }
            
            // Show changes section
            if (changesData.changes.length > 0) {
                html += `
                    <h6 class="mb-3">
                        <i class="bi bi-activity"></i> Recent Changes (${totalChanges})
                        ${totalChanges > changesData.changes.length ? `<small class="text-muted">- showing first ${changesData.changes.length}</small>` : ''}
                    </h6>
                    <div class="timeline">
                `;
                
                changesData.changes.forEach(change => {
                    const changeClass = change.change_type.replace('_', '-');
                    const changeTypeLabel = change.change_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const timestamp = new Date(change.timestamp);
                    const timeAgo = formatTimeAgo(timestamp);
                    const formattedDate = timestamp.toLocaleString();
                    
                    html += `
                        <div class="timeline-item change-item change-${changeClass}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="fw-medium">${change.display_text}</div>
                                    <div class="d-flex align-items-center gap-3 mt-1">
                                        <small class="text-muted">
                                            <i class="bi bi-clock"></i> ${timeAgo}
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar"></i> ${formattedDate}
                                        </small>
                                        <a href="/profile/${change.profile_username}" class="text-decoration-none small">
                                            <i class="bi bi-person"></i> View Profile
                                        </a>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">${changeTypeLabel}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
            }
            
            resultsContainer.innerHTML = html;
        }
        
        function showLoading() {
            searchResults.style.display = 'none';
            loadingState.style.display = 'block';
        }
        
        function hideLoading() {
            loadingState.style.display = 'none';
        }
        
        function showResults() {
            searchResults.style.display = 'block';
        }
        
        function hideResults() {
            searchResults.style.display = 'none';
            loadingState.style.display = 'none';
        }
        
        function showError(message) {
            hideLoading();
            showResults();
            resultCount.textContent = 'Error';
            resultsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error: ${message}
                </div>
            `;
        }
        
        function formatTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);
            
            if (diffInSeconds < 60) {
                return 'Just now';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} day${days !== 1 ? 's' : ''} ago`;
            }
        }
    });
</script>
{% endblock %}