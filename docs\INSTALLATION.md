# Instagram Follower Monitor - Installation Guide

This guide provides comprehensive instructions for installing and configuring the Instagram Follower Monitor application in various environments.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Quick Start with Docker](#quick-start-with-docker)
3. [Manual Installation](#manual-installation)
4. [Production Deployment](#production-deployment)
5. [Configuration](#configuration)
6. [First-Time Setup](#first-time-setup)
7. [Verification](#verification)

## System Requirements

### Minimum Requirements
- **Operating System**: Linux (Ubuntu 20.04+, CentOS 8+), macOS 10.15+, Windows 10+
- **Python**: 3.9 or higher
- **Memory**: 512 MB RAM (2 GB recommended)
- **Storage**: 1 GB free space (more for historical data)
- **Network**: Internet connection for Instagram API access

### Recommended Requirements
- **CPU**: 2+ cores
- **Memory**: 2 GB RAM
- **Storage**: 10 GB free space
- **Network**: Stable broadband connection

### Dependencies
- Python 3.9+
- SQLite 3.31+
- Git (for installation from source)

## Quick Start with Docker

The fastest way to get started is using Docker Compose.

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+

### Installation Steps

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/instagram-follower-monitor.git
   cd instagram-follower-monitor
   ```

2. **Configure environment**:
   ```bash
   cd deployment/docker
   cp .env.example .env
   ```

3. **Edit the `.env` file**:
   ```bash
   nano .env
   ```
   
   Update the following required values:
   ```env
   SECRET_KEY=your-unique-secret-key-here
   ENCRYPTION_KEY=your-encryption-key-here
   ```

4. **Generate encryption key**:
   ```bash
   python3 -c "from cryptography.fernet import Fernet; print('ENCRYPTION_KEY=' + Fernet.generate_key().decode())"
   ```

5. **Start the application**:
   ```bash
   docker-compose up -d
   ```

6. **Access the application**:
   Open your browser and navigate to `http://localhost`

### Docker Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f instagram-monitor

# Restart services
docker-compose restart

# Update application
docker-compose pull
docker-compose up -d --force-recreate
```

## Manual Installation

### Step 1: System Preparation

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install -y python3 python3-pip python3-venv git sqlite3 nginx
```

#### CentOS/RHEL
```bash
sudo yum update
sudo yum install -y python3 python3-pip git sqlite nginx
```

#### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install python@3.11 git sqlite nginx
```

### Step 2: Application Installation

1. **Create application directory**:
   ```bash
   sudo mkdir -p /var/www/instagram-monitor
   sudo chown $USER:$USER /var/www/instagram-monitor
   cd /var/www/instagram-monitor
   ```

2. **Clone repository**:
   ```bash
   git clone https://github.com/your-org/instagram-follower-monitor.git .
   ```

3. **Create virtual environment**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

4. **Install Python dependencies**:
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

5. **Create necessary directories**:
   ```bash
   mkdir -p logs backups data
   chmod 755 logs backups data
   ```

### Step 3: Database Initialization

1. **Initialize the database**:
   ```bash
   python init_database.py
   ```

2. **Verify database creation**:
   ```bash
   ls -la instagram_monitor.db
   sqlite3 instagram_monitor.db ".tables"
   ```

### Step 4: Configuration

1. **Create environment file**:
   ```bash
   cp .env.example .env
   ```

2. **Generate secret keys**:
   ```bash
   # Generate Flask secret key
   python3 -c "import secrets; print('SECRET_KEY=' + secrets.token_hex(32))" >> .env
   
   # Generate encryption key
   python3 -c "from cryptography.fernet import Fernet; print('ENCRYPTION_KEY=' + Fernet.generate_key().decode())" >> .env
   ```

3. **Edit configuration**:
   ```bash
   nano .env
   ```

## Production Deployment

### Step 1: Gunicorn Setup

1. **Install Gunicorn** (if not already installed):
   ```bash
   pip install gunicorn
   ```

2. **Test Gunicorn**:
   ```bash
   gunicorn --config deployment/gunicorn.conf.py app:app
   ```

### Step 2: Systemd Service

1. **Copy service file**:
   ```bash
   sudo cp deployment/systemd/instagram-monitor.service /etc/systemd/system/
   ```

2. **Update service file paths**:
   ```bash
   sudo nano /etc/systemd/system/instagram-monitor.service
   ```
   
   Update the `WorkingDirectory` and `ExecStart` paths to match your installation.

3. **Enable and start service**:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable instagram-monitor
   sudo systemctl start instagram-monitor
   ```

4. **Check service status**:
   ```bash
   sudo systemctl status instagram-monitor
   ```

### Step 3: Nginx Configuration

1. **Copy Nginx configuration**:
   ```bash
   sudo cp deployment/nginx.conf /etc/nginx/sites-available/instagram-monitor
   ```

2. **Update configuration**:
   ```bash
   sudo nano /etc/nginx/sites-available/instagram-monitor
   ```
   
   Update `server_name` and paths as needed.

3. **Enable site**:
   ```bash
   sudo ln -s /etc/nginx/sites-available/instagram-monitor /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### Step 4: SSL Configuration (Optional but Recommended)

1. **Install Certbot**:
   ```bash
   sudo apt install certbot python3-certbot-nginx
   ```

2. **Obtain SSL certificate**:
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```

3. **Test auto-renewal**:
   ```bash
   sudo certbot renew --dry-run
   ```

## Configuration

### Environment Variables

Create and configure the `.env` file with the following variables:

```env
# Required Configuration
SECRET_KEY=your-flask-secret-key
ENCRYPTION_KEY=your-fernet-encryption-key
DATABASE_URL=sqlite:///instagram_monitor.db

# Optional Configuration
FLASK_ENV=production
LOG_LEVEL=INFO
MONITORING_INTERVAL=2
SESSION_TIMEOUT=3600
RATE_LIMIT_PER_MINUTE=60
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=true
```

### Application Configuration

The application can be configured through:

1. **Environment variables** (`.env` file)
2. **Web interface** (Settings page)
3. **Database settings** (stored in `settings` table)

### Security Configuration

1. **File permissions**:
   ```bash
   chmod 600 .env
   chmod 644 instagram_monitor.db
   chmod 755 logs backups
   ```

2. **User permissions**:
   ```bash
   sudo chown -R www-data:www-data /var/www/instagram-monitor
   ```

## First-Time Setup

### Step 1: Access the Application

1. Open your web browser
2. Navigate to `http://your-server-ip` or `http://localhost`
3. You should see the Instagram Follower Monitor dashboard

### Step 2: Configure Instagram Credentials

1. Click on "Settings" in the navigation menu
2. Go to the "Instagram Authentication" section
3. Enter your Instagram username and password
4. Click "Save Credentials"
5. Test the connection by clicking "Test Authentication"

### Step 3: Add Profiles to Monitor

1. Go to the "Profiles" section
2. Click "Add Profile"
3. Enter the Instagram username you want to monitor
4. Configure monitoring settings (interval, enabled status)
5. Click "Add Profile"

### Step 4: Verify Monitoring

1. Check the "Dashboard" for profile status
2. Monitor the logs for any errors:
   ```bash
   tail -f logs/application.log
   ```
3. Verify that scheduled jobs are running:
   ```bash
   tail -f logs/scheduler.log
   ```

## Verification

### Health Checks

1. **Application health**:
   ```bash
   curl http://localhost:8000/health
   ```

2. **Database connectivity**:
   ```bash
   python3 -c "
   from database.connection import DatabaseManager
   db = DatabaseManager()
   print('Database connection:', 'OK' if db.test_connection() else 'FAILED')
   "
   ```

3. **Service status**:
   ```bash
   sudo systemctl status instagram-monitor
   sudo systemctl status nginx
   ```

### Log Monitoring

Monitor the following log files:

```bash
# Application logs
tail -f logs/application.log

# Web server logs
tail -f logs/web.log

# Scheduler logs
tail -f logs/scheduler.log

# Error logs
tail -f logs/errors.log

# System logs
sudo journalctl -u instagram-monitor -f
```

### Performance Verification

1. **Memory usage**:
   ```bash
   ps aux | grep gunicorn
   ```

2. **Database size**:
   ```bash
   ls -lh instagram_monitor.db
   ```

3. **Response time**:
   ```bash
   curl -w "@curl-format.txt" -o /dev/null -s http://localhost/
   ```

## Troubleshooting

If you encounter issues during installation, refer to the [Troubleshooting Guide](TROUBLESHOOTING.md) for common problems and solutions.

## Next Steps

After successful installation:

1. Read the [User Guide](USER_GUIDE.md) for detailed usage instructions
2. Review [Security Best Practices](SECURITY.md) for production deployments
3. Set up monitoring and alerting as described in the [Operations Guide](OPERATIONS.md)

## Support

For additional help:

- Check the [FAQ](FAQ.md)
- Review [Common Issues](TROUBLESHOOTING.md)
- Submit issues on GitHub
- Contact <NAME_EMAIL>