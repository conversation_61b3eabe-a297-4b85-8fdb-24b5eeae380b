{% extends "base.html" %}

{% block title %}Import Profile Data - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-upload"></i> Import Profile Data
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="import_file" class="form-label">Profile Export File</label>
                            <input type="file" class="form-control" id="import_file" name="import_file" 
                                   accept=".json" required>
                            <div class="form-text">
                                Select a JSON file exported from the profile export feature.
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="overwrite_existing" name="overwrite_existing">
                                <label class="form-check-label" for="overwrite_existing">
                                    Overwrite existing profile data
                                </label>
                                <div class="form-text">
                                    If checked, existing profile data will be replaced. Otherwise, import will fail if profile already exists.
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Import Information</h6>
                            <ul class="mb-0">
                                <li><strong>Profile Data:</strong> Username, display name, privacy settings</li>
                                <li><strong>Current Lists:</strong> Current followers and following</li>
                                <li><strong>Change History:</strong> All historical changes for the profile</li>
                                <li><strong>Statistics:</strong> Profile statistics and metadata</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Important Notes</h6>
                            <ul class="mb-0">
                                <li>Only JSON files exported from this application are supported</li>
                                <li>Large import files may take several minutes to process</li>
                                <li>Existing monitoring schedules will not be affected</li>
                                <li>A backup is recommended before importing large datasets</li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('backup.backup_dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Import Profile Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Import Process Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Import Process</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-list-ol"></i> Steps</h6>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item">Validate import file format</li>
                                <li class="list-group-item">Check for existing profile</li>
                                <li class="list-group-item">Create or update profile</li>
                                <li class="list-group-item">Import current followers/following</li>
                                <li class="list-group-item">Import change history</li>
                                <li class="list-group-item">Update statistics</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-file-code"></i> Supported Format</h6>
                            <p>The import file must be a JSON file with the following structure:</p>
                            <pre class="bg-light p-2 rounded"><code>{
  "profile": {
    "username": "example_user",
    "display_name": "Example User",
    "is_private": false,
    "monitoring_enabled": true
  },
  "current_followers": ["user1", "user2"],
  "current_following": ["user3", "user4"],
  "change_history": [
    {
      "affected_username": "user1",
      "change_type": "gained",
      "timestamp": "2024-01-01T12:00:00"
    }
  ]
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// File validation
document.getElementById('import_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (!file.name.toLowerCase().endsWith('.json')) {
            alert('Please select a JSON file.');
            e.target.value = '';
            return;
        }
        
        if (file.size > 100 * 1024 * 1024) { // 100MB limit
            alert('File is too large. Maximum size is 100MB.');
            e.target.value = '';
            return;
        }
    }
});

// Form submission with progress indication
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = document.querySelector('button[type="submit"]');
    const file = document.getElementById('import_file').files[0];
    
    if (file && file.size > 10 * 1024 * 1024) { // Show warning for files > 10MB
        if (!confirm('This is a large file and may take several minutes to import. Continue?')) {
            e.preventDefault();
            return;
        }
    }
    
    // Disable submit button and show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Importing...';
});
</script>
{% endblock %}