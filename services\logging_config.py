"""
Centralized Logging Configuration

This module provides structured logging configuration with different levels,
log rotation, and retention policies for the Instagram Follower Monitor application.
"""

import logging
import logging.handlers
import os
import sys
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from collections import deque

from config import Config


class CustomFormatter(logging.Formatter):
    """Custom formatter with color support and structured format."""
    
    # Color codes for different log levels
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
    }
    RESET = '\033[0m'
    
    def __init__(self, use_colors: bool = True, include_extra: bool = True):
        """
        Initialize custom formatter.
        
        Args:
            use_colors: Whether to use color codes in output
            include_extra: Whether to include extra fields in log messages
        """
        self.use_colors = use_colors
        self.include_extra = include_extra
        
        # Base format with timestamp, level, logger name, and message
        base_format = '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s'
        
        # Extended format with function and line number for debugging
        debug_format = '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
        
        super().__init__(base_format)
        self.debug_formatter = logging.Formatter(debug_format)
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors and extra information."""
        # Use debug format for DEBUG level
        if record.levelno == logging.DEBUG:
            formatted = self.debug_formatter.format(record)
        else:
            formatted = super().format(record)
        
        # Add color if enabled and outputting to terminal
        if self.use_colors and hasattr(sys.stderr, 'isatty') and sys.stderr.isatty():
            color = self.COLORS.get(record.levelname, '')
            formatted = f"{color}{formatted}{self.RESET}"
        
        # Add extra fields if available and enabled
        if self.include_extra and hasattr(record, 'extra_data'):
            extra_str = ' | '.join(f"{k}={v}" for k, v in record.extra_data.items())
            formatted = f"{formatted} | {extra_str}"
        
        return formatted


class StructuredLogger:
    """Wrapper for structured logging with extra context."""
    
    def __init__(self, logger: logging.Logger):
        """Initialize structured logger wrapper."""
        self.logger = logger
    
    def debug(self, message: str, **kwargs):
        """Log debug message with extra context."""
        self._log(logging.DEBUG, message, kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message with extra context."""
        self._log(logging.INFO, message, kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with extra context."""
        self._log(logging.WARNING, message, kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with extra context."""
        self._log(logging.ERROR, message, kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with extra context."""
        self._log(logging.CRITICAL, message, kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback and extra context."""
        # Handle exc_info properly
        exc_info = kwargs.pop('exc_info', True)
        if exc_info is True:
            import sys
            exc_info = sys.exc_info()
        self._log(logging.ERROR, message, kwargs, exc_info=exc_info)
    
    def _log(self, level: int, message: str, extra_data: Dict[str, Any], exc_info=None):
        """Internal method to log with extra data."""
        # Filter out None values and convert objects to strings
        filtered_extra = {
            k: str(v) for k, v in extra_data.items() 
            if v is not None and k != 'exc_info'
        }
        
        # Create log record with extra data
        record = self.logger.makeRecord(
            self.logger.name, level, '', 0, message, (), 
            exc_info=exc_info
        )
        
        if filtered_extra:
            record.extra_data = filtered_extra
        
        self.logger.handle(record)


class LoggingManager:
    """Centralized logging configuration and management."""
    
    def __init__(self, config: Config):
        """Initialize logging manager with configuration."""
        self.config = config
        self.log_dir = Path(__file__).parent.parent / 'logs'
        self.log_dir.mkdir(exist_ok=True)
        
        # Configure root logger
        self._configure_root_logger()
        
        # Configure application loggers
        self._configure_app_loggers()
        
        # Set up log rotation
        self._setup_log_rotation()
    
    def _configure_root_logger(self):
        """Configure the root logger with basic settings."""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.LOG_LEVEL.upper()))
        
        # Clear any existing handlers
        root_logger.handlers.clear()
        
        # Console handler with colors
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = CustomFormatter(use_colors=True)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler for all logs
        log_file = self.log_dir / 'application.log'
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = CustomFormatter(use_colors=False)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    def _configure_app_loggers(self):
        """Configure specific loggers for different application components."""
        # Component-specific log files
        components = {
            'services.instagram_client': 'instagram_client.log',
            'services.monitoring_service': 'monitoring.log',
            'services.scheduler': 'scheduler.log',
            'database': 'database.log',
            'web': 'web.log',
            'services.authentication': 'auth.log',
        }
        
        for logger_name, log_file in components.items():
            logger = logging.getLogger(logger_name)
            
            # Create rotating file handler for component
            file_path = self.log_dir / log_file
            handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=5 * 1024 * 1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            handler.setLevel(logging.DEBUG)
            
            # Use structured formatter
            formatter = CustomFormatter(use_colors=False, include_extra=True)
            handler.setFormatter(formatter)
            
            logger.addHandler(handler)
            logger.setLevel(logging.DEBUG)
            
            # Prevent duplicate logs in root logger
            logger.propagate = True
    
    def _setup_log_rotation(self):
        """Set up log rotation and retention policies."""
        # Error log for critical issues
        error_log = self.log_dir / 'errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_formatter = CustomFormatter(use_colors=False, include_extra=True)
        error_handler.setFormatter(error_formatter)
        
        # Add error handler to root logger
        logging.getLogger().addHandler(error_handler)
    
    def get_structured_logger(self, name: str) -> StructuredLogger:
        """Get a structured logger for the given name."""
        logger = logging.getLogger(name)
        return StructuredLogger(logger)
    
    def cleanup_old_logs(self, days: int = 30):
        """Clean up log files older than specified days."""
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            
            for log_file in self.log_dir.glob('*.log*'):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    logging.info(f"Cleaned up old log file: {log_file}")
        
        except Exception as e:
            logging.error(f"Error cleaning up old logs: {e}")


# Global logging manager instance
_logging_manager: Optional[LoggingManager] = None


def setup_logging(config: Config = None) -> LoggingManager:
    """
    Set up application logging configuration.
    
    Args:
        config: Configuration object. If None, creates a new Config instance.
        
    Returns:
        LoggingManager instance.
    """
    global _logging_manager
    
    if config is None:
        config = Config()
    
    if _logging_manager is None:
        _logging_manager = LoggingManager(config)
    
    return _logging_manager


def get_logger(name: str) -> StructuredLogger:
    """
    Get a structured logger for the given name.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        StructuredLogger instance.
    """
    if _logging_manager is None:
        setup_logging()
    
    return _logging_manager.get_structured_logger(name)


def log_function_call(func):
    """Decorator to log function calls with parameters and execution time."""
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = time.time()
        
        # Log function entry
        logger.debug(
            f"Entering {func.__name__}",
            function=func.__name__,
            module=func.__module__,
            args_count=len(args),
            kwargs_keys=list(kwargs.keys())
        )
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Log successful completion
            logger.debug(
                f"Completed {func.__name__}",
                function=func.__name__,
                execution_time_ms=round(execution_time * 1000, 2),
                success=True
            )
            
            return result
        
        except Exception as e:
            execution_time = time.time() - start_time
            
            # Log exception
            logger.error(
                f"Exception in {func.__name__}: {str(e)}",
                function=func.__name__,
                execution_time_ms=round(execution_time * 1000, 2),
                exception_type=type(e).__name__,
                success=False
            )
            raise
    
    return wrapper


def log_performance(operation: str):
    """Context manager to log performance metrics for operations."""
    import time
    from contextlib import contextmanager
    
    @contextmanager
    def performance_logger():
        logger = get_logger(__name__)
        start_time = time.time()
        
        logger.debug(f"Starting {operation}", operation=operation)
        
        try:
            yield
            execution_time = time.time() - start_time
            logger.info(
                f"Completed {operation}",
                operation=operation,
                execution_time_ms=round(execution_time * 1000, 2),
                success=True
            )
        
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"Failed {operation}: {str(e)}",
                operation=operation,
                execution_time_ms=round(execution_time * 1000, 2),
                exception_type=type(e).__name__,
                success=False
            )
            raise
    
    return performance_logger()


class CriticalErrorNotifier:
    """Handles notifications for critical system errors."""
    
    def __init__(self):
        """Initialize critical error notifier."""
        self.notification_handlers = []
        self.error_history = deque(maxlen=100)
        self.notification_cooldown = {}  # Track cooldowns to prevent spam
        
    def add_notification_handler(self, handler):
        """Add a notification handler function."""
        self.notification_handlers.append(handler)
    
    def notify_critical_error(self, error: Exception, component: str, context: Dict[str, Any] = None):
        """Send notifications for critical errors."""
        error_key = f"{component}:{type(error).__name__}"
        current_time = time.time()
        
        # Check cooldown (don't spam notifications for same error type)
        if error_key in self.notification_cooldown:
            if current_time - self.notification_cooldown[error_key] < 300:  # 5 minute cooldown
                return
        
        self.notification_cooldown[error_key] = current_time
        
        # Record error
        error_record = {
            'timestamp': datetime.now().isoformat(),
            'component': component,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context or {}
        }
        self.error_history.append(error_record)
        
        # Send notifications
        for handler in self.notification_handlers:
            try:
                handler(error_record)
            except Exception as e:
                # Don't let notification failures break the application
                logger = get_logger(__name__)
                logger.error(f"Notification handler failed: {e}")
    
    def get_recent_critical_errors(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get recent critical errors."""
        cutoff = datetime.now() - timedelta(hours=hours)
        return [
            error for error in self.error_history
            if datetime.fromisoformat(error['timestamp']) >= cutoff
        ]


# Global critical error notifier
_critical_notifier = CriticalErrorNotifier()


def get_critical_notifier() -> CriticalErrorNotifier:
    """Get the global critical error notifier."""
    return _critical_notifier


def setup_default_notifications():
    """Set up default notification handlers."""
    notifier = get_critical_notifier()
    
    def log_critical_notification(error_record: Dict[str, Any]):
        """Log critical errors with special formatting."""
        logger = get_logger('critical_errors')
        logger.critical(
            f"CRITICAL ERROR in {error_record['component']}: {error_record['error_message']}",
            **error_record['context']
        )
    
    def file_critical_notification(error_record: Dict[str, Any]):
        """Write critical errors to dedicated file."""
        critical_log_path = Path(__file__).parent.parent / 'logs' / 'critical_errors.log'
        try:
            with open(critical_log_path, 'a', encoding='utf-8') as f:
                f.write(f"{error_record['timestamp']} | {error_record['component']} | "
                       f"{error_record['error_type']} | {error_record['error_message']}\n")
        except Exception:
            pass  # Don't fail if we can't write to file
    
    notifier.add_notification_handler(log_critical_notification)
    notifier.add_notification_handler(file_critical_notification)