"""
Scheduling service for periodic Instagram follower monitoring.

This module provides job scheduling functionality using APScheduler with SQLite
persistence, retry logic, and comprehensive error handling.
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta, timezone
import traceback
import time

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_MISSED
from apscheduler.job import Job

from config import Config

logger = logging.getLogger(__name__)

# Global registry for monitoring callbacks
_monitoring_callbacks = {}
_job_stats = {}


def execute_monitoring_job(service_id: str, max_retries: int = 3) -> None:
    """Execute monitoring job with retry logic and error handling.
    
    This is a standalone function that can be serialized by APScheduler.
    
    Args:
        service_id: Unique identifier for the scheduler service
        max_retries: Maximum number of retry attempts
    """
    job_start_time = datetime.now(timezone.utc)
    
    try:
        logger.info("Starting scheduled monitoring job")
        
        # Get callback from global registry
        callback = _monitoring_callbacks.get(service_id)
        if not callback:
            raise Exception(f"No monitoring callback configured for service {service_id}")
        
        # Get or initialize stats for this service
        if service_id not in _job_stats:
            _job_stats[service_id] = {
                'total_executions': 0,
                'successful_executions': 0,
                'failed_executions': 0,
                'missed_executions': 0,
                'last_execution': None,
                'last_error': None
            }
        
        stats = _job_stats[service_id]
        
        # Execute monitoring with retry logic
        result = _execute_with_retry(callback, max_retries=max_retries, retry_delay=60)
        
        # Update statistics
        stats['total_executions'] += 1
        stats['last_execution'] = job_start_time.isoformat()
        
        if result and result.get('success', False):
            stats['successful_executions'] += 1
            logger.info(f"Monitoring job completed successfully: {result.get('message', 'No message')}")
        else:
            stats['failed_executions'] += 1
            error_msg = result.get('error', 'Unknown error') if result else 'No result returned'
            stats['last_error'] = error_msg
            logger.error(f"Monitoring job failed: {error_msg}")
        
    except Exception as e:
        # Update stats for this service
        if service_id in _job_stats:
            stats = _job_stats[service_id]
            stats['total_executions'] += 1
            stats['failed_executions'] += 1
            stats['last_error'] = str(e)
            stats['last_execution'] = job_start_time.isoformat()
        
        logger.error(f"Monitoring job execution failed: {e}")
        logger.debug(f"Monitoring job traceback: {traceback.format_exc()}")


def _execute_with_retry(func: Callable, max_retries: int = 3, retry_delay: int = 60) -> Any:
    """Execute function with retry logic.
    
    Args:
        func: Function to execute
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
        
    Returns:
        Any: Function result
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return func()
            
        except Exception as e:
            last_exception = e
            
            if attempt < max_retries:
                logger.warning(f"Monitoring attempt {attempt + 1} failed: {e}. Retrying in {retry_delay}s...")
                time.sleep(retry_delay)
            else:
                logger.error(f"All {max_retries + 1} monitoring attempts failed")
    
    # If we get here, all attempts failed
    raise last_exception


class SchedulerService:
    """Service for managing scheduled monitoring tasks."""
    
    def __init__(self, config: Config):
        """Initialize scheduler service.
        
        Args:
            config: Application configuration instance
        """
        self.config = config
        self._scheduler = None
        self._monitoring_callback = None
        self._is_running = False
        self._service_id = f"scheduler_{id(self)}"  # Unique ID for this service instance
        
        # Configure job store and executors
        self._setup_scheduler()
    
    def _setup_scheduler(self) -> None:
        """Set up APScheduler with SQLite job store."""
        try:
            # Configure job store with SQLite database
            # Use a separate database file for scheduler jobs to avoid conflicts
            scheduler_db_path = self.config.get_database_path().parent / "scheduler_jobs.db"
            jobstore_url = f"sqlite:///{scheduler_db_path}"
            jobstores = {
                'default': SQLAlchemyJobStore(url=jobstore_url, tablename='scheduler_jobs')
            }
            
            # Configure executors
            executors = {
                'default': ThreadPoolExecutor(max_workers=2)
            }
            
            # Job defaults
            job_defaults = {
                'coalesce': True,  # Combine multiple pending executions into one
                'max_instances': 1,  # Only one instance of each job at a time
                'misfire_grace_time': 300  # 5 minutes grace time for missed jobs
            }
            
            # Create scheduler
            self._scheduler = BackgroundScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone='UTC'
            )
            
            # Add event listeners
            self._scheduler.add_listener(
                self._job_executed_listener,
                EVENT_JOB_EXECUTED | EVENT_JOB_ERROR | EVENT_JOB_MISSED
            )
            
            logger.info("Scheduler configured with SQLite job store")
            
        except Exception as e:
            logger.error(f"Failed to setup scheduler: {e}")
            raise
    
    def start(self) -> bool:
        """Start the scheduler.
        
        Returns:
            bool: True if started successfully
        """
        try:
            if self._is_running:
                logger.warning("Scheduler is already running")
                return True
            
            self._scheduler.start()
            self._is_running = True
            
            logger.info("Scheduler started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            return False
    
    def stop(self, wait: bool = True) -> bool:
        """Stop the scheduler.
        
        Args:
            wait: Whether to wait for running jobs to complete
            
        Returns:
            bool: True if stopped successfully
        """
        try:
            if not self._is_running:
                logger.warning("Scheduler is not running")
                return True
            
            self._scheduler.shutdown(wait=wait)
            self._is_running = False
            
            # Clean up global registry
            if self._service_id in _monitoring_callbacks:
                del _monitoring_callbacks[self._service_id]
            
            # Force cleanup of scheduler resources
            self._scheduler = None
            
            logger.info("Scheduler stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {e}")
            return False
    
    def is_running(self) -> bool:
        """Check if scheduler is running.
        
        Returns:
            bool: True if scheduler is running
        """
        return self._is_running and self._scheduler.running
    
    def set_monitoring_callback(self, callback: Callable[[], Dict[str, Any]]) -> None:
        """Set the callback function for monitoring jobs.
        
        Args:
            callback: Function to call for monitoring (should return result dict)
        """
        self._monitoring_callback = callback
        # Register callback in global registry
        _monitoring_callbacks[self._service_id] = callback
        logger.info("Monitoring callback set")
    
    def schedule_monitoring_job(self, 
                              job_id: str = 'periodic_monitoring',
                              interval_hours: Optional[int] = None,
                              replace_existing: bool = True) -> bool:
        """Schedule periodic monitoring job.
        
        Args:
            job_id: Unique identifier for the job
            interval_hours: Monitoring interval in hours (uses config default if None)
            replace_existing: Whether to replace existing job with same ID
            
        Returns:
            bool: True if job scheduled successfully
        """
        try:
            if not self._monitoring_callback:
                logger.error("No monitoring callback set - cannot schedule job")
                return False
            
            if not self._is_running:
                logger.error("Scheduler is not running - cannot schedule job")
                return False
            
            # Use config default if interval not specified
            if interval_hours is None:
                interval_hours = self.config.MONITORING_INTERVAL_HOURS
            
            # Remove existing job if it exists and replace_existing is True
            if replace_existing and self.job_exists(job_id):
                self.remove_job(job_id)
            
            # Schedule the job
            job = self._scheduler.add_job(
                func=execute_monitoring_job,
                args=[self._service_id, self.config.MAX_RETRIES],
                trigger='interval',
                hours=interval_hours,
                id=job_id,
                name=f'Periodic Monitoring (every {interval_hours}h)',
                replace_existing=replace_existing,
                next_run_time=datetime.now(timezone.utc) + timedelta(minutes=1)  # Start in 1 minute
            )
            
            logger.info(f"Scheduled monitoring job '{job_id}' with {interval_hours}h interval")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule monitoring job: {e}")
            return False
    
    def schedule_one_time_job(self, 
                            job_id: str,
                            run_time: datetime,
                            replace_existing: bool = True) -> bool:
        """Schedule a one-time monitoring job.
        
        Args:
            job_id: Unique identifier for the job
            run_time: When to run the job
            replace_existing: Whether to replace existing job with same ID
            
        Returns:
            bool: True if job scheduled successfully
        """
        try:
            if not self._monitoring_callback:
                logger.error("No monitoring callback set - cannot schedule job")
                return False
            
            if not self._is_running:
                logger.error("Scheduler is not running - cannot schedule job")
                return False
            
            # Remove existing job if it exists and replace_existing is True
            if replace_existing and self.job_exists(job_id):
                self.remove_job(job_id)
            
            # Schedule the job
            job = self._scheduler.add_job(
                func=execute_monitoring_job,
                args=[self._service_id, self.config.MAX_RETRIES],
                trigger='date',
                run_date=run_time,
                id=job_id,
                name=f'One-time Monitoring at {run_time}',
                replace_existing=replace_existing
            )
            
            logger.info(f"Scheduled one-time job '{job_id}' for {run_time}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule one-time job: {e}")
            return False
    
    def remove_job(self, job_id: str) -> bool:
        """Remove a scheduled job.
        
        Args:
            job_id: ID of the job to remove
            
        Returns:
            bool: True if job removed successfully
        """
        try:
            if not self._is_running:
                logger.error("Scheduler is not running")
                return False
            
            self._scheduler.remove_job(job_id)
            logger.info(f"Removed job '{job_id}'")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove job '{job_id}': {e}")
            return False
    
    def job_exists(self, job_id: str) -> bool:
        """Check if a job exists.
        
        Args:
            job_id: ID of the job to check
            
        Returns:
            bool: True if job exists
        """
        try:
            if not self._is_running:
                return False
            
            job = self._scheduler.get_job(job_id)
            return job is not None
            
        except Exception:
            return False
    
    def get_job_info(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific job.
        
        Args:
            job_id: ID of the job
            
        Returns:
            Optional[Dict[str, Any]]: Job information or None if not found
        """
        try:
            if not self._is_running:
                return None
            
            job = self._scheduler.get_job(job_id)
            if not job:
                return None
            
            return {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger),
                'func_name': job.func.__name__ if job.func else None,
                'args': job.args,
                'kwargs': job.kwargs
            }
            
        except Exception as e:
            logger.error(f"Failed to get job info for '{job_id}': {e}")
            return None
    
    def get_all_jobs(self) -> List[Dict[str, Any]]:
        """Get information about all scheduled jobs.
        
        Returns:
            List[Dict[str, Any]]: List of job information
        """
        try:
            if not self._is_running:
                return []
            
            jobs = self._scheduler.get_jobs()
            job_list = []
            
            for job in jobs:
                job_info = {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger),
                    'func_name': job.func.__name__ if job.func else None,
                    'args': job.args,
                    'kwargs': job.kwargs
                }
                job_list.append(job_info)
            
            return job_list
            
        except Exception as e:
            logger.error(f"Failed to get all jobs: {e}")
            return []
    
    def pause_job(self, job_id: str) -> bool:
        """Pause a scheduled job.
        
        Args:
            job_id: ID of the job to pause
            
        Returns:
            bool: True if job paused successfully
        """
        try:
            if not self._is_running:
                logger.error("Scheduler is not running")
                return False
            
            self._scheduler.pause_job(job_id)
            logger.info(f"Paused job '{job_id}'")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause job '{job_id}': {e}")
            return False
    
    def resume_job(self, job_id: str) -> bool:
        """Resume a paused job.
        
        Args:
            job_id: ID of the job to resume
            
        Returns:
            bool: True if job resumed successfully
        """
        try:
            if not self._is_running:
                logger.error("Scheduler is not running")
                return False
            
            self._scheduler.resume_job(job_id)
            logger.info(f"Resumed job '{job_id}'")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume job '{job_id}': {e}")
            return False
    
    def modify_job_interval(self, job_id: str, interval_hours: int) -> bool:
        """Modify the interval of an existing job.
        
        Args:
            job_id: ID of the job to modify
            interval_hours: New interval in hours
            
        Returns:
            bool: True if job modified successfully
        """
        try:
            if not self._is_running:
                logger.error("Scheduler is not running")
                return False
            
            from apscheduler.triggers.interval import IntervalTrigger
            
            # Create new interval trigger
            new_trigger = IntervalTrigger(hours=interval_hours)
            
            self._scheduler.modify_job(job_id, trigger=new_trigger)
            logger.info(f"Modified job '{job_id}' interval to {interval_hours}h")
            return True
            
        except Exception as e:
            logger.error(f"Failed to modify job '{job_id}': {e}")
            return False
    
    def run_job_now(self, job_id: str) -> bool:
        """Run a job immediately (in addition to its scheduled runs).
        
        Args:
            job_id: ID of the job to run
            
        Returns:
            bool: True if job execution started successfully
        """
        try:
            if not self._is_running:
                logger.error("Scheduler is not running")
                return False
            
            job = self._scheduler.get_job(job_id)
            if not job:
                logger.error(f"Job '{job_id}' not found")
                return False
            
            # Execute the job function directly
            self._scheduler.add_job(
                func=job.func,
                args=job.args,
                kwargs=job.kwargs,
                id=f"{job_id}_manual_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                name=f"Manual execution of {job.name}",
                next_run_time=datetime.utcnow()
            )
            
            logger.info(f"Started manual execution of job '{job_id}'")
            return True
            
        except Exception as e:
            logger.error(f"Failed to run job '{job_id}' now: {e}")
            return False
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """Get scheduler status and statistics.
        
        Returns:
            Dict[str, Any]: Scheduler status information
        """
        try:
            # Get stats for this service instance
            stats = _job_stats.get(self._service_id, {
                'total_executions': 0,
                'successful_executions': 0,
                'failed_executions': 0,
                'missed_executions': 0,
                'last_execution': None,
                'last_error': None
            })
            
            status = {
                'running': self.is_running(),
                'job_count': len(self.get_all_jobs()) if self._is_running else 0,
                'statistics': stats.copy()
            }
            
            if self._is_running:
                status['scheduler_state'] = str(self._scheduler.state)
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get scheduler status: {e}")
            return {
                'running': False,
                'job_count': 0,
                'statistics': {},
                'error': str(e)
            }
    

    
    def _job_executed_listener(self, event) -> None:
        """Handle job execution events.
        
        Args:
            event: APScheduler event object
        """
        try:
            if event.code == EVENT_JOB_EXECUTED:
                logger.debug(f"Job {event.job_id} executed successfully")
                
            elif event.code == EVENT_JOB_ERROR:
                logger.error(f"Job {event.job_id} failed with exception: {event.exception}")
                
            elif event.code == EVENT_JOB_MISSED:
                # Update missed executions for this service
                if self._service_id in _job_stats:
                    _job_stats[self._service_id]['missed_executions'] += 1
                logger.warning(f"Job {event.job_id} missed its scheduled run time")
                
        except Exception as e:
            logger.error(f"Error in job event listener: {e}")