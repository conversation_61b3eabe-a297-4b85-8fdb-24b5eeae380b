# Instagram Follower Monitor Configuration

# Application Settings
DEBUG=false
SECRET_KEY=your-secret-key-here
HOST=127.0.0.1
PORT=5000

# Database Settings
DATABASE_URL=sqlite:///instagram_monitor.db

# Instagram Credentials (optional - can be set via web interface)
INSTAGRAM_USERNAME=
INSTAGRAM_PASSWORD=

# Monitoring Settings
MONITORING_INTERVAL_HOURS=2
DATA_RETENTION_DAYS=365

# Security Settings
ENCRYPTION_KEY=your-encryption-key-here

# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=instagram_monitor.log

# Rate Limiting Settings
MIN_REQUEST_DELAY=1.0
MAX_REQUEST_DELAY=3.0
MAX_RETRIES=3