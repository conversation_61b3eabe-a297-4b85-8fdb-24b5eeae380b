"""
Instagram Client Wrapper for Follower Monitor

This module provides a wrapper around Instaloader with rate limiting,
authentication management, and error handling for Instagram data access.
"""

import time
import random
import logging
from typing import List, Optional, Set, Dict, Any
from datetime import datetime, timedelta
import instaloader
from instaloader.exceptions import (
    LoginRequiredException, 
    BadCredentialsException,
    TwoFactorAuthRequiredException,
    ConnectionException,
    QueryReturnedBadRequestException
)

from config import Config
from services.authentication import AuthenticationManager
from models.data_models import ProfileInfo
from services.logging_config import get_logger, log_function_call, log_performance
from services.exceptions import ExternalServiceError, RateLimitError, AuthenticationError


logger = get_logger(__name__)


class RateLimiter:
    """Handles rate limiting and anti-bot measures for Instagram requests."""
    
    def __init__(self, config: Config):
        """Initialize rate limiter with configuration.
        
        Args:
            config: Application configuration instance
        """
        self.config = config
        self.last_request_time = 0.0
        self.request_count = 0
        self.backoff_until = 0.0
        self.consecutive_errors = 0
        
        # Request pattern monitoring
        self.request_history = []  # Store timestamps of recent requests
        self.request_types = {}  # Track different types of requests
        self.session_start_time = time.time()
        
        # Detection avoidance strategies
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        self.current_user_agent_index = 0
        
    def wait_if_needed(self, request_type: str = 'general') -> None:
        """Wait if rate limiting is needed before making a request.
        
        Args:
            request_type: Type of request being made for pattern monitoring
        """
        current_time = time.time()
        
        # Check if we're in backoff period
        if current_time < self.backoff_until:
            wait_time = self.backoff_until - current_time
            logger.info(f"Rate limiter: waiting {wait_time:.2f}s due to backoff")
            time.sleep(wait_time)
            current_time = time.time()
        
        # Update request pattern monitoring
        self._update_request_patterns(request_type, current_time)
        
        # Calculate time since last request
        time_since_last = current_time - self.last_request_time
        
        # Add random delay between min and max request delay
        min_delay = self.config.MIN_REQUEST_DELAY
        max_delay = self.config.MAX_REQUEST_DELAY
        
        # Apply adaptive delays based on request patterns
        adaptive_delay = self._calculate_adaptive_delay(request_type)
        required_delay = random.uniform(min_delay, max_delay) + adaptive_delay
        
        if time_since_last < required_delay:
            wait_time = required_delay - time_since_last
            logger.debug(f"Rate limiter: waiting {wait_time:.2f}s for request spacing (type: {request_type})")
            time.sleep(wait_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def handle_rate_limit_error(self) -> None:
        """Handle rate limit error with exponential backoff."""
        self.consecutive_errors += 1
        
        # Exponential backoff: 2^errors * 60 seconds, max 1 hour
        backoff_seconds = min(60 * (2 ** self.consecutive_errors), 3600)
        self.backoff_until = time.time() + backoff_seconds
        
        logger.warning(f"Rate limit hit. Backing off for {backoff_seconds}s")
    
    def reset_error_count(self) -> None:
        """Reset consecutive error count after successful request."""
        self.consecutive_errors = 0
    
    def _update_request_patterns(self, request_type: str, timestamp: float) -> None:
        """Update request pattern monitoring data.
        
        Args:
            request_type: Type of request being made
            timestamp: Current timestamp
        """
        # Clean old request history (keep last hour)
        cutoff_time = timestamp - 3600  # 1 hour ago
        self.request_history = [t for t in self.request_history if t > cutoff_time]
        
        # Add current request
        self.request_history.append(timestamp)
        
        # Track request types
        if request_type not in self.request_types:
            self.request_types[request_type] = []
        
        # Clean old request type history
        self.request_types[request_type] = [
            t for t in self.request_types[request_type] if t > cutoff_time
        ]
        self.request_types[request_type].append(timestamp)
    
    def _calculate_adaptive_delay(self, request_type: str) -> float:
        """Calculate adaptive delay based on request patterns.
        
        Args:
            request_type: Type of request being made
            
        Returns:
            float: Additional delay in seconds
        """
        current_time = time.time()
        adaptive_delay = 0.0
        
        # Check request frequency in last 10 minutes
        recent_cutoff = current_time - 600  # 10 minutes ago
        recent_requests = [t for t in self.request_history if t > recent_cutoff]
        
        # If making too many requests recently, add extra delay
        if len(recent_requests) > 20:  # More than 20 requests in 10 minutes
            adaptive_delay += 2.0
            logger.debug("Adding adaptive delay due to high request frequency")
        
        # Check for repetitive request patterns
        if request_type in self.request_types:
            recent_type_requests = [
                t for t in self.request_types[request_type] if t > recent_cutoff
            ]
            
            # If making too many of the same type of request, add delay
            if len(recent_type_requests) > 10:  # More than 10 of same type in 10 minutes
                adaptive_delay += 1.5
                logger.debug(f"Adding adaptive delay due to repetitive {request_type} requests")
        
        # Add extra delay during peak hours (simulate human behavior)
        hour = time.localtime(current_time).tm_hour
        if 9 <= hour <= 17:  # Business hours
            adaptive_delay += random.uniform(0.5, 1.0)
        
        return adaptive_delay
    
    def get_next_user_agent(self) -> str:
        """Get next user agent for rotation.
        
        Returns:
            str: User agent string
        """
        user_agent = self.user_agents[self.current_user_agent_index]
        self.current_user_agent_index = (self.current_user_agent_index + 1) % len(self.user_agents)
        return user_agent
    
    def should_rotate_session(self) -> bool:
        """Check if session should be rotated to avoid detection.
        
        Returns:
            bool: True if session should be rotated
        """
        current_time = time.time()
        session_duration = current_time - self.session_start_time
        
        # Rotate session after 2 hours or after many requests
        if session_duration > 7200 or self.request_count > 500:
            return True
        
        # Rotate if too many consecutive errors
        if self.consecutive_errors > 3:
            return True
        
        return False
    
    def reset_session(self) -> None:
        """Reset session tracking for rotation."""
        self.session_start_time = time.time()
        self.request_count = 0
        self.consecutive_errors = 0
        self.request_history = []
        self.request_types = {}
        logger.info("Session reset for detection avoidance")
    
    def get_request_stats(self) -> Dict[str, Any]:
        """Get current request statistics.
        
        Returns:
            Dict[str, Any]: Request statistics
        """
        current_time = time.time()
        recent_cutoff = current_time - 600  # 10 minutes ago
        recent_requests = [t for t in self.request_history if t > recent_cutoff]
        
        return {
            'total_requests': self.request_count,
            'recent_requests_10min': len(recent_requests),
            'consecutive_errors': self.consecutive_errors,
            'session_duration': current_time - self.session_start_time,
            'backoff_until': self.backoff_until,
            'request_types': {
                req_type: len([t for t in timestamps if t > recent_cutoff])
                for req_type, timestamps in self.request_types.items()
            }
        }


class InstagramClient:
    """Main Instagram client wrapper with authentication and rate limiting."""
    
    def __init__(self, config: Config):
        """Initialize Instagram client.
        
        Args:
            config: Application configuration instance
        """
        self.config = config
        self.auth_manager = AuthenticationManager(config)
        self.rate_limiter = RateLimiter(config)
        self.loader = instaloader.Instaloader(
            quiet=True,
            user_agent=self.rate_limiter.get_next_user_agent(),
            request_timeout=30.0,
            max_connection_attempts=3
        )
        self._authenticated = False
        self._current_username = None
        
    @log_function_call
    def authenticate(self, username: Optional[str] = None, password: Optional[str] = None, 
                    interactive: bool = False) -> bool:
        """Authenticate with Instagram.
        
        Args:
            username: Instagram username (optional if stored)
            password: Instagram password (optional if stored)
            interactive: Whether to allow interactive login prompts
            
        Returns:
            bool: True if authentication was successful
        """
        try:
            # Use provided credentials or retrieve stored ones
            if username and password:
                if not self.auth_manager.validate_credentials(username, password):
                    logger.error("Invalid credential format", username=username)
                    raise AuthenticationError("Invalid credential format")
                creds = {'username': username, 'password': password}
            else:
                creds = self.auth_manager.retrieve_credentials()
                if not creds:
                    logger.error("No credentials provided or stored")
                    raise AuthenticationError("No credentials available")
            
            # Apply rate limiting
            self.rate_limiter.wait_if_needed('authentication')
            
            # Attempt login
            logger.info("Attempting Instagram authentication", username=creds['username'])
            
            try:
                with log_performance("instagram_login"):
                    self.loader.login(creds['username'], creds['password'])
                
                self._authenticated = True
                self._current_username = creds['username']
                
                # Store credentials if they were provided directly
                if username and password:
                    self.auth_manager.store_credentials(username, password)
                
                # Reset error count on successful authentication
                self.rate_limiter.reset_error_count()
                
                logger.info("Instagram authentication successful", 
                          username=creds['username'],
                          session_active=True)
                return True
                
            except TwoFactorAuthRequiredException:
                logger.info("Two-factor authentication required", username=creds['username'])
                if interactive:
                    return self._handle_two_factor_auth(creds['username'])
                else:
                    logger.error("Two-factor authentication required but not in interactive mode")
                    raise AuthenticationError("Two-factor authentication required")
                    
            except BadCredentialsException:
                logger.error("Invalid Instagram credentials", username=creds['username'])
                raise AuthenticationError("Invalid Instagram credentials")
                
            except ConnectionException as e:
                logger.error("Connection error during Instagram authentication", 
                           username=creds['username'], error=str(e))
                self.rate_limiter.handle_rate_limit_error()
                raise ExternalServiceError("Instagram", f"Connection error: {str(e)}")
                
        except (AuthenticationError, ExternalServiceError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            logger.exception("Unexpected error during Instagram authentication", 
                           username=username or "unknown")
            raise ExternalServiceError("Instagram", f"Authentication error: {str(e)}")
    
    def _handle_two_factor_auth(self, username: str) -> bool:
        """Handle two-factor authentication interactively.
        
        Args:
            username: Instagram username
            
        Returns:
            bool: True if 2FA was successful
        """
        try:
            # This would typically involve prompting the user for 2FA code
            # For now, we'll log that 2FA is required and return False
            logger.warning(f"Two-factor authentication required for {username}")
            logger.info("Please implement interactive 2FA handling in production")
            return False
            
        except Exception as e:
            logger.error(f"Two-factor authentication failed: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if client is currently authenticated.
        
        Returns:
            bool: True if authenticated
        """
        return self._authenticated and self.loader.context.is_logged_in
    
    def get_current_username(self) -> Optional[str]:
        """Get the currently authenticated username.
        
        Returns:
            Optional[str]: Current username or None if not authenticated
        """
        return self._current_username if self.is_authenticated() else None
    
    def get_profile_info(self, username: str) -> Optional[ProfileInfo]:
        """Get profile information for a given username.
        
        Args:
            username: Instagram username to fetch
            
        Returns:
            Optional[ProfileInfo]: Profile information or None if failed
        """
        if not self.is_authenticated():
            logger.error("Not authenticated - cannot fetch profile info")
            return None
        
        try:
            self.rate_limiter.wait_if_needed('profile_info')
            
            profile = instaloader.Profile.from_username(self.loader.context, username)
            
            profile_info = ProfileInfo(
                username=profile.username,
                display_name=profile.full_name or profile.username,
                follower_count=profile.followers,
                following_count=profile.followees,
                is_private=profile.is_private,
                last_updated=datetime.now()
            )
            
            self.rate_limiter.reset_error_count()
            logger.debug(f"Successfully fetched profile info for: {username}")
            return profile_info
            
        except QueryReturnedBadRequestException as e:
            if "rate limited" in str(e).lower():
                self.rate_limiter.handle_rate_limit_error()
            logger.error(f"Failed to fetch profile info for {username}: {e}")
            return None
            
        except Exception as e:
            logger.error(f"Failed to fetch profile info for {username}: {e}")
            return None
    
    def get_followers(self, username: str, max_count: Optional[int] = None) -> Optional[Set[str]]:
        """Get followers list for a profile.
        
        Args:
            username: Instagram username
            max_count: Maximum number of followers to fetch (None for all)
            
        Returns:
            Optional[Set[str]]: Set of follower usernames or None if failed
        """
        if not self.is_authenticated():
            logger.error("Not authenticated - cannot fetch followers")
            return None
        
        try:
            self.rate_limiter.wait_if_needed('followers')
            
            profile = instaloader.Profile.from_username(self.loader.context, username)
            
            if profile.is_private and not profile.followed_by_viewer:
                logger.warning(f"Profile {username} is private and not followed")
                return None
            
            followers = set()
            count = 0
            
            for follower in profile.get_followers():
                if max_count and count >= max_count:
                    break
                    
                followers.add(follower.username)
                count += 1
                
                # Add small delay between follower fetches
                if count % 50 == 0:
                    time.sleep(random.uniform(0.5, 1.5))
            
            self.rate_limiter.reset_error_count()
            logger.info(f"Successfully fetched {len(followers)} followers for: {username}")
            return followers
            
        except QueryReturnedBadRequestException as e:
            if "rate limited" in str(e).lower():
                self.rate_limiter.handle_rate_limit_error()
            logger.error(f"Failed to fetch followers for {username}: {e}")
            return None
            
        except Exception as e:
            logger.error(f"Failed to fetch followers for {username}: {e}")
            return None
    
    def get_following(self, username: str, max_count: Optional[int] = None) -> Optional[Set[str]]:
        """Get following list for a profile.
        
        Args:
            username: Instagram username
            max_count: Maximum number of following to fetch (None for all)
            
        Returns:
            Optional[Set[str]]: Set of following usernames or None if failed
        """
        if not self.is_authenticated():
            logger.error("Not authenticated - cannot fetch following")
            return None
        
        try:
            self.rate_limiter.wait_if_needed('following')
            
            profile = instaloader.Profile.from_username(self.loader.context, username)
            
            if profile.is_private and not profile.followed_by_viewer:
                logger.warning(f"Profile {username} is private and not followed")
                return None
            
            following = set()
            count = 0
            
            for followee in profile.get_followees():
                if max_count and count >= max_count:
                    break
                    
                following.add(followee.username)
                count += 1
                
                # Add small delay between following fetches
                if count % 50 == 0:
                    time.sleep(random.uniform(0.5, 1.5))
            
            self.rate_limiter.reset_error_count()
            logger.info(f"Successfully fetched {len(following)} following for: {username}")
            return following
            
        except QueryReturnedBadRequestException as e:
            if "rate limited" in str(e).lower():
                self.rate_limiter.handle_rate_limit_error()
            logger.error(f"Failed to fetch following for {username}: {e}")
            return None
            
        except Exception as e:
            logger.error(f"Failed to fetch following for {username}: {e}")
            return None
    
    def logout(self) -> None:
        """Logout and clear authentication state."""
        try:
            self._authenticated = False
            self._current_username = None
            logger.info("Logged out successfully")
            
        except Exception as e:
            logger.error(f"Error during logout: {e}")
    
    def rotate_session_if_needed(self) -> bool:
        """Rotate session if needed for detection avoidance.
        
        Returns:
            bool: True if session was rotated
        """
        if self.rate_limiter.should_rotate_session():
            logger.info("Rotating session for detection avoidance")
            
            # Store current credentials for re-authentication
            current_username = self._current_username
            
            # Logout current session
            self.logout()
            
            # Reset rate limiter
            self.rate_limiter.reset_session()
            
            # Create new loader with rotated user agent
            self.loader = instaloader.Instaloader(
                quiet=True,
                user_agent=self.rate_limiter.get_next_user_agent(),
                request_timeout=30.0,
                max_connection_attempts=3
            )
            
            # Re-authenticate if we had a username
            if current_username:
                success = self.authenticate()
                if not success:
                    logger.error("Failed to re-authenticate after session rotation")
                    return False
            
            return True
        
        return False
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information.
        
        Returns:
            Dict[str, Any]: Session information dictionary
        """
        base_info = {
            'authenticated': self.is_authenticated(),
            'username': self.get_current_username(),
            'request_count': self.rate_limiter.request_count,
            'consecutive_errors': self.rate_limiter.consecutive_errors,
            'backoff_until': self.rate_limiter.backoff_until
        }
        
        # Add rate limiter stats
        rate_stats = self.rate_limiter.get_request_stats()
        base_info.update(rate_stats)
        
        return base_info