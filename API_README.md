# Instagram Follower Monitor REST API

This document provides comprehensive documentation for the Instagram Follower Monitor REST API v1.

## Overview

The REST API provides programmatic access to all Instagram follower monitoring functionality, including:

- Profile management (create, read, update, delete)
- Follower/following change data retrieval with advanced filtering
- Real-time monitoring status and job information
- Dashboard summary data for frontend integration
- System settings and configuration management

## Base URL

```
/api/v1
```

## Authentication

### API Key Authentication

Write operations (POST, PUT, DELETE) require API key authentication. Include the API key in the request header:

```http
X-API-Key: your-api-key-here
```

### Generating an API Key

1. Access the web dashboard settings page
2. Navigate to the "API Configuration" section
3. Click "Generate New API Key"
4. Save the generated key securely (it won't be shown again)

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Read operations**: 100 requests per hour
- **Write operations**: 20 requests per hour  
- **Search operations**: 50 requests per hour

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Requests remaining in current window
- `X-RateLimit-Reset`: Time when rate limit resets

## Response Format

All API responses use JSON format with consistent structure:

### Success Response
```json
{
  "data": { ... },
  "pagination": { ... },  // For paginated endpoints
  "message": "Success message"  // For write operations
}
```

### Error Response
```json
{
  "error": "Error type",
  "message": "Detailed error message"
}
```

## Endpoints

### Health Check

#### GET /health
Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Profile Management

#### GET /profiles
Get all profiles with optional filtering and pagination.

**Parameters:**
- `enabled_only` (boolean): Filter to enabled profiles only
- `include_stats` (boolean): Include profile statistics
- `page` (integer): Page number (default: 1)
- `per_page` (integer): Items per page (default: 20, max: 100)

**Example:**
```bash
curl "http://localhost:5000/api/v1/profiles?enabled_only=true&include_stats=true&page=1&per_page=10"
```

**Response:**
```json
{
  "profiles": [
    {
      "id": 1,
      "username": "example_user",
      "display_name": "Example User",
      "is_private": false,
      "monitoring_enabled": true,
      "last_scan": "2024-01-01T10:00:00Z",
      "created_at": "2024-01-01T08:00:00Z",
      "is_due_for_scan": false,
      "stats": {
        "current_followers": 1500,
        "current_following": 300,
        "followers_gained_30d": 25,
        "followers_lost_30d": 10,
        "following_started_30d": 5,
        "following_stopped_30d": 2
      }
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 10,
    "total_count": 1,
    "total_pages": 1,
    "has_next": false,
    "has_prev": false
  }
}
```

#### GET /profiles/{username}
Get detailed information for a specific profile.

**Example:**
```bash
curl "http://localhost:5000/api/v1/profiles/example_user"
```

#### POST /profiles
Create a new profile for monitoring. **Requires API key.**

**Request Body:**
```json
{
  "username": "new_user",
  "display_name": "New User",
  "is_private": false,
  "enabled": true,
  "interval_hours": 2
}
```

**Example:**
```bash
curl -X POST "http://localhost:5000/api/v1/profiles" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"username": "new_user", "display_name": "New User"}'
```

#### PUT /profiles/{username}
Update profile configuration. **Requires API key.**

**Request Body:**
```json
{
  "display_name": "Updated Name",
  "enabled": false,
  "interval_hours": 4
}
```

#### DELETE /profiles/{username}
Delete profile and all associated data. **Requires API key.**

**Example:**
```bash
curl -X DELETE "http://localhost:5000/api/v1/profiles/example_user" \
  -H "X-API-Key: your-api-key"
```

### Change Data

#### GET /changes
Get follower/following changes with advanced filtering and pagination.

**Parameters:**
- `profile` (string): Filter by profile username
- `type` (string): Filter by change type (`gained`, `lost`, `started_following`, `stopped_following`)
- `search` (string): Search in affected usernames
- `days` (integer): Number of days to look back (default: 7)
- `page` (integer): Page number (default: 1)
- `per_page` (integer): Items per page (default: 20, max: 100)
- `sort_by` (string): Sort field (`timestamp`, `username`, `profile`)
- `sort_order` (string): Sort order (`asc`, `desc`)

**Example:**
```bash
curl "http://localhost:5000/api/v1/changes?profile=example_user&type=gained&days=30&page=1&per_page=20"
```

**Response:**
```json
{
  "changes": [
    {
      "id": "example_user_new_follower_2024-01-01T12:00:00Z",
      "profile_username": "example_user",
      "affected_username": "new_follower",
      "change_type": "gained",
      "timestamp": "2024-01-01T12:00:00Z",
      "is_follower_change": true,
      "is_following_change": false,
      "display_text": "@new_follower started following @example_user"
    }
  ],
  "pagination": { ... },
  "filters": {
    "profile": "example_user",
    "type": "gained",
    "search": "",
    "days": 30,
    "sort_by": "timestamp",
    "sort_order": "desc"
  }
}
```

#### GET /changes/statistics
Get change statistics for specified period and profile.

**Parameters:**
- `profile` (string): Filter by profile username
- `days` (integer): Number of days for statistics (default: 30)

**Example:**
```bash
curl "http://localhost:5000/api/v1/changes/statistics?profile=example_user&days=30"
```

**Response:**
```json
{
  "period_days": 30,
  "profile": "example_user",
  "statistics": {
    "followers_gained": 25,
    "followers_lost": 10,
    "following_started": 5,
    "following_stopped": 2
  },
  "generated_at": "2024-01-01T12:00:00Z"
}
```

### Monitoring Status

#### GET /monitoring/status
Get comprehensive monitoring system status.

**Response:**
```json
{
  "system_status": {
    "total_profiles": 5,
    "enabled_profiles": 4,
    "profiles_due_for_scan": 2,
    "profiles_with_recent_scans": 3,
    "authentication_configured": true
  },
  "profile_status": {
    "example_user": {
      "last_scan": "2024-01-01T10:00:00Z",
      "hours_ago": 2.5,
      "is_due": false
    }
  },
  "generated_at": "2024-01-01T12:30:00Z"
}
```

#### GET /monitoring/jobs
Get current monitoring job status.

**Response:**
```json
{
  "scheduler_running": true,
  "jobs": [
    {
      "id": "monitoring_job_1",
      "name": "Profile Monitoring",
      "next_run_time": "2024-01-01T14:00:00Z",
      "trigger": "interval[0:02:00]",
      "func_name": "monitor_profiles"
    }
  ],
  "job_count": 1
}
```

### Dashboard Data

#### GET /dashboard/summary
Get dashboard summary data for real-time updates.

**Response:**
```json
{
  "summary": {
    "total_profiles": 5,
    "enabled_profiles": 4,
    "total_followers": 7500,
    "total_following": 1200,
    "changes_today": 15
  },
  "recent_changes": [
    {
      "id": "example_user_new_follower_2024-01-01T12:00:00Z",
      "profile_username": "example_user",
      "affected_username": "new_follower",
      "change_type": "gained",
      "timestamp": "2024-01-01T12:00:00Z",
      "display_text": "@new_follower started following @example_user"
    }
  ],
  "last_updated": "2024-01-01T12:30:00Z"
}
```

### Follower/Following Lists

#### GET /profiles/{username}/followers
Get current followers list for a profile.

**Parameters:**
- `page` (integer): Page number (default: 1)
- `per_page` (integer): Items per page (default: 50, max: 200)

**Response:**
```json
{
  "profile": "example_user",
  "followers": ["follower1", "follower2", "follower3"],
  "pagination": { ... }
}
```

#### GET /profiles/{username}/following
Get current following list for a profile.

**Parameters:**
- `page` (integer): Page number (default: 1)
- `per_page` (integer): Items per page (default: 50, max: 200)

### Settings Management

#### GET /settings
Get current system settings. **Requires API key.**

**Response:**
```json
{
  "monitoring_settings": {
    "monitoring_interval_hours": 2,
    "data_retention_days": 365,
    "min_request_delay": 1.0,
    "max_request_delay": 3.0,
    "max_retries": 3,
    "profile_processing_delay": 5.0
  },
  "retrieved_at": "2024-01-01T12:00:00Z"
}
```

#### PUT /settings
Update system settings. **Requires API key.**

**Request Body:**
```json
{
  "monitoring_interval_hours": 3,
  "data_retention_days": 180,
  "min_request_delay": 1.5,
  "max_request_delay": 4.0
}
```

### Documentation

#### GET /docs
Get comprehensive API documentation including schemas and examples.

#### GET /docs/openapi
Get OpenAPI 3.0 specification for the API.

## Error Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 201  | Created |
| 400  | Bad Request - Invalid input |
| 401  | Unauthorized - API key required or invalid |
| 404  | Not Found - Resource doesn't exist |
| 409  | Conflict - Resource already exists |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error |

## Testing

Use the provided test script to verify API functionality:

```bash
# Test without authentication
python test_api.py --url http://localhost:5000/api/v1

# Test with API key
python test_api.py --url http://localhost:5000/api/v1 --api-key your-api-key

# Test specific endpoint
python test_api.py --test health_check
```

## Integration Examples

### JavaScript/Node.js

```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:5000/api/v1',
  headers: {
    'X-API-Key': 'your-api-key-here'
  }
});

// Get profiles
const profiles = await api.get('/profiles?include_stats=true');
console.log(profiles.data);

// Create profile
const newProfile = await api.post('/profiles', {
  username: 'new_user',
  display_name: 'New User'
});
```

### Python

```python
import requests

headers = {'X-API-Key': 'your-api-key-here'}
base_url = 'http://localhost:5000/api/v1'

# Get changes
response = requests.get(f'{base_url}/changes?days=7', headers=headers)
changes = response.json()

# Update profile
profile_data = {'enabled': False}
response = requests.put(f'{base_url}/profiles/example_user', 
                       json=profile_data, headers=headers)
```

### cURL

```bash
# Get monitoring status
curl "http://localhost:5000/api/v1/monitoring/status"

# Create profile with authentication
curl -X POST "http://localhost:5000/api/v1/profiles" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"username": "new_user", "enabled": true}'
```

## Security Considerations

1. **API Key Security**: Store API keys securely and never expose them in client-side code
2. **HTTPS**: Use HTTPS in production to encrypt API communications
3. **Rate Limiting**: Respect rate limits to avoid being blocked
4. **Input Validation**: The API validates all inputs, but clients should also validate data
5. **Error Handling**: Implement proper error handling for all API calls

## Support

For API support and questions:
1. Check the web dashboard logs for detailed error information
2. Use the test script to verify API functionality
3. Review the OpenAPI specification at `/api/v1/docs/openapi`
4. Check system status at `/api/v1/monitoring/status`