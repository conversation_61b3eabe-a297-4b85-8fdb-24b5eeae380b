#!/usr/bin/env python3
"""
Run the dashboard for testing.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.app import create_app
from config import Config


def run_dashboard():
    """Run the dashboard application."""
    config = Config()
    app = create_app(config)
    
    print("Starting Instagram Follower Monitor Dashboard...")
    print(f"Dashboard will be available at: http://{config.HOST}:{config.PORT}")
    print("Press Ctrl+C to stop the server")
    
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.DEBUG
    )


if __name__ == '__main__':
    run_dashboard()