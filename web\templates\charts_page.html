{% extends "base.html" %}

{% block title %}Data Visualization - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="h2 mb-0">
            <i class="bi bi-bar-chart"></i> Data Visualization
        </h1>
        <p class="text-muted">Interactive charts and analytics for your Instagram monitoring data</p>
    </div>
</div>

<!-- Chart Controls -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-sliders"></i> Chart Controls
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="profileSelect" class="form-label">Profile Filter</label>
                        <select class="form-select" id="profileSelect" onchange="updateChartsForProfile()">
                            <option value="">All Profiles</option>
                            {% for profile in profiles %}
                            <option value="{{ profile.profile_username }}">@{{ profile.profile_username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="timeRangeSelect" class="form-label">Time Range</label>
                        <select class="form-select" id="timeRangeSelect" onchange="updateChartsTimeRange()">
                            <option value="7">Last 7 Days</option>
                            <option value="30" selected>Last 30 Days</option>
                            <option value="90">Last 90 Days</option>
                            <option value="365">Last Year</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Export Options</label>
                        <div class="btn-group w-100">
                            <button type="button" class="btn btn-outline-primary" onclick="exportAllCharts()">
                                <i class="bi bi-download"></i> Export Images
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="exportAllChartsData()">
                                <i class="bi bi-file-earmark-spreadsheet"></i> Export Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Grid -->
<div class="row">
    <div class="col-lg-8">
        <!-- Follower Trend Chart -->
        <div class="chart-container mb-4" id="followerTrendContainer">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up"></i> Follower/Following Trends
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary" onclick="updateTrendChart(7)">7D</button>
                        <button type="button" class="btn btn-outline-secondary active" onclick="updateTrendChart(30)">30D</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="updateTrendChart(90)">90D</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportChart('followerTrend', 'follower_trend.png')">
                            <i class="bi bi-download"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-wrapper" style="position: relative; height: 400px;">
                        <canvas id="followerTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Profile Comparison Chart -->
        <div class="chart-container mb-4" id="profileComparisonContainer">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart"></i> Profile Comparison
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleComparisonView('followers')">Followers</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleComparisonView('following')">Following</button>
                        <button type="button" class="btn btn-outline-secondary active" onclick="toggleComparisonView('both')">Both</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportChart('profileComparison', 'profile_comparison.png')">
                            <i class="bi bi-download"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-wrapper" style="position: relative; height: 400px;">
                        <canvas id="profileComparisonChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Change Distribution Chart -->
        <div class="chart-container mb-4" id="changeDistributionContainer">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-pie-chart"></i> Change Distribution
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary" onclick="updateChangeDistribution(7)">7D</button>
                        <button type="button" class="btn btn-outline-secondary active" onclick="updateChangeDistribution(30)">30D</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="updateChangeDistribution(90)">90D</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportChart('changeDistribution', 'change_distribution.png')">
                            <i class="bi bi-download"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-wrapper" style="position: relative; height: 300px;">
                        <canvas id="changeDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistics Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calculator"></i> Statistics Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="fw-bold text-primary fs-4" id="totalFollowers">0</div>
                        <small class="text-muted">Total Followers</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="fw-bold text-info fs-4" id="totalFollowing">0</div>
                        <small class="text-muted">Total Following</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="fw-bold text-success fs-4" id="totalGained">0</div>
                        <small class="text-muted">Followers Gained</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="fw-bold text-danger fs-4" id="totalLost">0</div>
                        <small class="text-muted">Followers Lost</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <div class="fw-bold text-warning fs-5" id="netChange">0</div>
                    <small class="text-muted">Net Change (30 days)</small>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshAllCharts()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh All Charts
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetChartZoom()">
                        <i class="bi bi-zoom-out"></i> Reset Zoom
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="toggleChartAnimations()">
                        <i class="bi bi-play-circle"></i> Toggle Animations
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentProfile = '';
let currentTimeRange = 30;
let animationsEnabled = true;

// Update charts for selected profile
function updateChartsForProfile() {
    const profileSelect = document.getElementById('profileSelect');
    currentProfile = profileSelect.value;
    
    // Update all charts with new profile filter
    if (window.chartUtils) {
        window.chartUtils.loadFollowerTrendData(currentProfile, currentTimeRange);
        window.chartUtils.loadChangeDistributionData(currentProfile, currentTimeRange);
        
        // Profile comparison doesn't need profile filter as it shows all profiles
        if (!currentProfile) {
            window.chartUtils.loadProfileComparisonData();
        }
    }
    
    updateStatistics();
}

// Update charts time range
function updateChartsTimeRange() {
    const timeRangeSelect = document.getElementById('timeRangeSelect');
    currentTimeRange = parseInt(timeRangeSelect.value);
    
    // Update all charts with new time range
    if (window.chartUtils) {
        window.chartUtils.loadFollowerTrendData(currentProfile, currentTimeRange);
        window.chartUtils.loadChangeDistributionData(currentProfile, currentTimeRange);
    }
    
    updateStatistics();
}

// Update statistics summary
async function updateStatistics() {
    try {
        const url = currentProfile ? 
            `/api/profile/${currentProfile}/change-distribution?days=${currentTimeRange}` :
            `/api/dashboard/change-distribution?days=${currentTimeRange}`;
            
        const response = await fetch(url);
        if (!response.ok) return;
        
        const data = await response.json();
        
        // Update statistics display
        document.getElementById('totalGained').textContent = data.gained || 0;
        document.getElementById('totalLost').textContent = data.lost || 0;
        
        const netChange = (data.gained || 0) - (data.lost || 0);
        const netChangeElement = document.getElementById('netChange');
        netChangeElement.textContent = netChange > 0 ? `+${netChange}` : netChange;
        netChangeElement.className = `fw-bold fs-5 ${netChange > 0 ? 'text-success' : netChange < 0 ? 'text-danger' : 'text-warning'}`;
        
        // Get total followers/following if no profile filter
        if (!currentProfile) {
            const statsResponse = await fetch('/api/dashboard/stats');
            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                document.getElementById('totalFollowers').textContent = stats.total_followers.toLocaleString();
                document.getElementById('totalFollowing').textContent = stats.total_following.toLocaleString();
            }
        } else {
            const profileResponse = await fetch(`/api/profiles/${currentProfile}`);
            if (profileResponse.ok) {
                const profile = await profileResponse.json();
                document.getElementById('totalFollowers').textContent = (profile.stats.current_followers || 0).toLocaleString();
                document.getElementById('totalFollowing').textContent = (profile.stats.current_following || 0).toLocaleString();
            }
        }
        
    } catch (error) {
        console.error('Error updating statistics:', error);
    }
}

// Export all charts as images
function exportAllCharts() {
    const charts = ['followerTrend', 'changeDistribution', 'profileComparison'];
    charts.forEach((chartName, index) => {
        if (window.chartUtils && window.chartInstances && window.chartInstances[chartName]) {
            setTimeout(() => {
                window.chartUtils.exportChart(chartName, `${chartName}_chart.png`);
            }, index * 200); // Stagger exports
        }
    });
}

// Export all chart data as CSV
function exportAllChartsData() {
    const charts = ['followerTrend', 'changeDistribution', 'profileComparison'];
    charts.forEach((chartName, index) => {
        if (window.chartUtils && window.chartInstances && window.chartInstances[chartName]) {
            setTimeout(() => {
                window.chartUtils.exportChartData(chartName, `${chartName}_data.csv`);
            }, index * 200); // Stagger exports
        }
    });
}

// Refresh all charts
function refreshAllCharts() {
    if (window.chartUtils) {
        window.chartUtils.loadFollowerTrendData(currentProfile, currentTimeRange);
        window.chartUtils.loadChangeDistributionData(currentProfile, currentTimeRange);
        window.chartUtils.loadProfileComparisonData();
    }
    updateStatistics();
}

// Reset chart zoom
function resetChartZoom() {
    if (window.chartInstances) {
        Object.values(window.chartInstances).forEach(chart => {
            if (chart && typeof chart.resetZoom === 'function') {
                chart.resetZoom();
            }
        });
    }
}

// Toggle chart animations
function toggleChartAnimations() {
    animationsEnabled = !animationsEnabled;
    
    if (window.chartInstances) {
        Object.values(window.chartInstances).forEach(chart => {
            if (chart && chart.options) {
                chart.options.animation = animationsEnabled ? { duration: 1000 } : false;
                chart.update('none');
            }
        });
    }
    
    const button = event.target.closest('button');
    button.innerHTML = animationsEnabled ? 
        '<i class="bi bi-pause-circle"></i> Disable Animations' :
        '<i class="bi bi-play-circle"></i> Enable Animations';
}

// Page-specific refresh function
async function pageRefresh() {
    refreshAllCharts();
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize statistics
    updateStatistics();
    
    // Auto-refresh every 5 minutes
    setInterval(refreshAllCharts, 5 * 60 * 1000);
});
</script>
{% endblock %}
</content>
</file>