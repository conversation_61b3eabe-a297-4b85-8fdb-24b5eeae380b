{% extends "base.html" %}

{% block title %}Bad Gateway - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plug me-2"></i>
                        Bad Gateway (502)
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-unlink fa-5x text-danger"></i>
                    </div>
                    
                    <h5 class="card-title">External Service Unavailable</h5>
                    <p class="card-text text-muted">
                        We're having trouble connecting to external services (like Instagram). This is usually temporary.
                    </p>
                    
                    <div class="mt-4">
                        <h6>What's happening:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-instagram me-2"></i>Instagram's servers might be experiencing issues</li>
                            <li><i class="fas fa-network-wired me-2"></i>There could be network connectivity problems</li>
                            <li><i class="fas fa-clock me-2"></i>The service might be temporarily overloaded</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <button onclick="location.reload()" class="btn btn-primary">
                            <i class="fas fa-refresh me-2"></i>
                            Try Again
                        </button>
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}