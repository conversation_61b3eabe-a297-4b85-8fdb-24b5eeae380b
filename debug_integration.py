#!/usr/bin/env python3
"""Debug integration test issues."""

import tempfile
import os
from database.connection import DatabaseManager
from database.repositories import ProfileRepository

def test_basic_repository():
    """Test basic repository creation."""
    print("Testing basic repository creation...")
    
    # Create temporary database
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_file.close()
    temp_db_path = temp_file.name
    
    try:
        # Initialize database
        db_manager = DatabaseManager(temp_db_path)
        db_manager.initialize_schema()
        print("✓ Database initialized")
        
        # Create repository
        profile_repo = ProfileRepository()
        print("✓ ProfileRepository created")
        
        # Test basic operation
        profiles = profile_repo.get_all_profiles()
        print(f"✓ Got profiles: {len(profiles)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if os.path.exists(temp_db_path):
            os.unlink(temp_db_path)

if __name__ == '__main__':
    test_basic_repository()