/**
 * Search functionality JavaScript
 * Handles live search, filtering, and result display
 */

// Global variables
let searchTimeout;
let currentRequest;

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
    setupSearchEventListeners();
});

/**
 * Initialize search components
 */
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput && searchInput.value.trim()) {
        performSearch();
    }
}

/**
 * Set up event listeners for search functionality
 */
function setupSearchEventListeners() {
    // Live search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearchInput);
        searchInput.addEventListener('keydown', handleSearchKeydown);
    }
    
    // Filter selects
    document.querySelectorAll('.search-filter').forEach(filter => {
        filter.addEventListener('change', handleFilterChange);
    });
    
    // Sort buttons
    document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.addEventListener('click', handleSortChange);
    });
    
    // Clear search button
    const clearBtn = document.getElementById('clearSearch');
    if (clearBtn) {
        clearBtn.addEventListener('click', handleClearSearch);
    }
    
    // Advanced search toggle
    const advancedToggle = document.getElementById('advancedSearchToggle');
    if (advancedToggle) {
        advancedToggle.addEventListener('click', toggleAdvancedSearch);
    }
}

/**
 * Handle search input changes with debouncing
 */
function handleSearchInput(event) {
    const query = event.target.value.trim();
    
    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    // Cancel previous request
    if (currentRequest) {
        currentRequest.abort();
    }
    
    // Show live search indicator
    showLiveSearchIndicator(true);
    
    // Debounce search
    searchTimeout = setTimeout(() => {
        if (query.length >= 2 || query.length === 0) {
            performSearch();
        }
    }, 300);
}

/**
 * Handle search input keydown events
 */
function handleSearchKeydown(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        performSearch();
    } else if (event.key === 'Escape') {
        handleClearSearch();
    }
}

/**
 * Handle filter changes
 */
function handleFilterChange(event) {
    updateURL();
    performSearch();
}

/**
 * Handle sort button clicks
 */
function handleSortChange(event) {
    const btn = event.target.closest('.sort-btn');
    const sortBy = btn.dataset.sortBy;
    const currentOrder = btn.dataset.sortOrder || 'desc';
    const newOrder = currentOrder === 'desc' ? 'asc' : 'desc';
    
    // Update button state
    document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
    btn.classList.add('active');
    btn.dataset.sortOrder = newOrder;
    
    // Update sort icon
    const icon = btn.querySelector('i');
    if (icon) {
        icon.className = `bi bi-sort-${newOrder === 'desc' ? 'down' : 'up'}`;
    }
    
    updateURL();
    performSearch();
}

/**
 * Handle clear search
 */
function handleClearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
    }
    
    // Reset filters
    document.querySelectorAll('.search-filter').forEach(filter => {
        filter.selectedIndex = 0;
    });
    
    // Clear results
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.innerHTML = '<p class="text-muted">Enter a search term to find users and changes.</p>';
    }
    
    // Update URL
    const url = new URL(window.location);
    url.search = '';
    window.history.replaceState({}, '', url);
    
    showLiveSearchIndicator(false);
}

/**
 * Toggle advanced search options
 */
function toggleAdvancedSearch() {
    const advancedOptions = document.getElementById('advancedSearchOptions');
    const toggleBtn = document.getElementById('advancedSearchToggle');
    
    if (advancedOptions && toggleBtn) {
        const isVisible = advancedOptions.style.display !== 'none';
        advancedOptions.style.display = isVisible ? 'none' : 'block';
        
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            icon.className = isVisible ? 'bi bi-chevron-down' : 'bi bi-chevron-up';
        }
        
        toggleBtn.textContent = isVisible ? 'Show Advanced Options' : 'Hide Advanced Options';
    }
}

/**
 * Perform search with current parameters
 */
async function performSearch() {
    const searchParams = getSearchParameters();
    
    try {
        showLoadingState(true);
        
        // Create AbortController for request cancellation
        const controller = new AbortController();
        currentRequest = controller;
        
        const url = new URL('/api/search', window.location.origin);
        Object.entries(searchParams).forEach(([key, value]) => {
            if (value) url.searchParams.set(key, value);
        });
        
        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                'Accept': 'application/json'
            }
        });
        
        if (!response.ok) throw new Error('Search request failed');
        
        const results = await response.json();
        displaySearchResults(results);
        updateURL();
        
    } catch (error) {
        if (error.name !== 'AbortError') {
            console.error('Search error:', error);
            showSearchError('Failed to perform search. Please try again.');
        }
    } finally {
        showLoadingState(false);
        showLiveSearchIndicator(false);
        currentRequest = null;
    }
}

/**
 * Get current search parameters
 */
function getSearchParameters() {
    const searchInput = document.getElementById('searchInput');
    const profileFilter = document.getElementById('profileFilter');
    const typeFilter = document.getElementById('typeFilter');
    const daysFilter = document.getElementById('daysFilter');
    const sortBtn = document.querySelector('.sort-btn.active');
    
    return {
        q: searchInput ? searchInput.value.trim() : '',
        profile: profileFilter ? profileFilter.value : '',
        type: typeFilter ? typeFilter.value : '',
        days: daysFilter ? daysFilter.value : '7',
        sort_by: sortBtn ? sortBtn.dataset.sortBy : 'timestamp',
        sort_order: sortBtn ? sortBtn.dataset.sortOrder : 'desc'
    };
}

/**
 * Display search results
 */
function displaySearchResults(results) {
    const container = document.getElementById('searchResults');
    if (!container) return;
    
    if (!results.changes || results.changes.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-search display-4 text-muted"></i>
                <p class="text-muted mt-2">No results found for your search.</p>
                <button class="btn btn-outline-primary" onclick="handleClearSearch()">
                    Clear Search
                </button>
            </div>
        `;
        return;
    }
    
    const html = `
        <div class="search-results-header mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5>Search Results (${results.total_count} found)</h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="exportResults()">
                        <i class="bi bi-download"></i> Export
                    </button>
                </div>
            </div>
        </div>
        
        <div class="search-results-list">
            ${results.changes.map(change => createChangeResultHTML(change)).join('')}
        </div>
        
        ${createPaginationHTML(results.pagination)}
    `;
    
    container.innerHTML = html;
    
    // Add fade-in animation
    container.querySelectorAll('.search-result-item').forEach((item, index) => {
        setTimeout(() => {
            item.classList.add('fade-in');
        }, index * 50);
    });
}

/**
 * Create HTML for a single change result
 */
function createChangeResultHTML(change) {
    const changeTypeClass = `change-${change.change_type}`;
    const badgeColor = window.dashboardUtils ? 
        window.dashboardUtils.getChangeTypeBadgeColor(change.change_type) : 'secondary';
    const timeAgoText = window.dashboardUtils ? 
        window.dashboardUtils.timeAgo(change.timestamp) : new Date(change.timestamp).toLocaleString();
    
    return `
        <div class="search-result-item card mb-2 ${changeTypeClass}">
            <div class="card-body py-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <strong class="me-2">@${change.profile_username}</strong>
                            <span class="badge bg-${badgeColor} me-2">
                                ${formatChangeType(change.change_type)}
                            </span>
                            <small class="text-muted">
                                <i class="bi bi-clock"></i> ${timeAgoText}
                            </small>
                        </div>
                        <div class="text-muted">
                            ${change.display_text}
                        </div>
                    </div>
                    <div class="ms-2">
                        <a href="/profile/${change.profile_username}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i> View Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Create pagination HTML
 */
function createPaginationHTML(pagination) {
    if (!pagination || pagination.total_pages <= 1) {
        return '';
    }
    
    const currentPage = pagination.page;
    const totalPages = pagination.total_pages;
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    let html = '<nav aria-label="Search results pagination"><ul class="pagination justify-content-center">';
    
    // Previous button
    if (currentPage > 1) {
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="goToPage(${currentPage - 1})">Previous</a>
        </li>`;
    }
    
    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        html += `<li class="page-item ${activeClass}">
            <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
        </li>`;
    }
    
    // Next button
    if (currentPage < totalPages) {
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="goToPage(${currentPage + 1})">Next</a>
        </li>`;
    }
    
    html += '</ul></nav>';
    return html;
}

/**
 * Go to specific page
 */
function goToPage(page) {
    const url = new URL(window.location);
    url.searchParams.set('page', page);
    window.location.href = url.toString();
}

/**
 * Update URL with current search parameters
 */
function updateURL() {
    const params = getSearchParameters();
    const url = new URL(window.location);
    
    // Clear existing search params
    url.search = '';
    
    // Add non-empty parameters
    Object.entries(params).forEach(([key, value]) => {
        if (value && value !== '7' && key !== 'days') { // Don't add default days value
            url.searchParams.set(key, value);
        } else if (key === 'days' && value !== '7') {
            url.searchParams.set(key, value);
        }
    });
    
    // Update URL without page reload
    window.history.replaceState({}, '', url);
}

/**
 * Show/hide loading state
 */
function showLoadingState(show) {
    const container = document.getElementById('searchResults');
    if (!container) return;
    
    if (show) {
        container.classList.add('results-loading');
        container.style.position = 'relative';
    } else {
        container.classList.remove('results-loading');
        container.style.position = '';
    }
}

/**
 * Show/hide live search indicator
 */
function showLiveSearchIndicator(show) {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;
    
    if (show) {
        searchInput.classList.add('live-search-active');
    } else {
        searchInput.classList.remove('live-search-active');
    }
}

/**
 * Show search error message
 */
function showSearchError(message) {
    const container = document.getElementById('searchResults');
    if (!container) return;
    
    container.innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle"></i>
            ${message}
        </div>
    `;
}

/**
 * Export search results
 */
function exportResults() {
    const params = getSearchParameters();
    const url = new URL('/api/search/export', window.location.origin);
    
    Object.entries(params).forEach(([key, value]) => {
        if (value) url.searchParams.set(key, value);
    });
    
    // Create temporary link and click it
    const link = document.createElement('a');
    link.href = url.toString();
    link.download = `search_results_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * Format change type for display
 */
function formatChangeType(changeType) {
    const formats = {
        'gained': 'New Follower',
        'lost': 'Lost Follower',
        'started_following': 'Started Following',
        'stopped_following': 'Stopped Following'
    };
    return formats[changeType] || changeType;
}

// Export functions for global use
window.searchUtils = {
    performSearch,
    handleClearSearch,
    goToPage,
    exportResults
};