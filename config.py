"""
Configuration Management for Instagram Follower Monitor

This module handles all configuration settings for the application,
including environment variables, default values, and validation.
"""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv


class Config:
    """Application configuration class."""
    
    def __init__(self):
        """Initialize configuration from environment variables and defaults."""
        # Load environment variables from .env file if it exists
        env_file = Path(__file__).parent / '.env'
        if env_file.exists():
            load_dotenv(env_file)
        
        # Application settings
        self.DEBUG = self._get_bool('DEBUG', False)
        self.SECRET_KEY = self._get_required('SECRET_KEY', self._generate_secret_key())
        self.HOST = os.getenv('HOST', '127.0.0.1')
        self.PORT = int(os.getenv('PORT', '5000'))
        
        # Database settings
        self.DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///instagram_monitor.db')
        self.DATABASE_PATH = Path(__file__).parent / 'instagram_monitor.db'
        
        # Instagram settings
        self.INSTAGRAM_USERNAME = os.getenv('INSTAGRAM_USERNAME', '')
        self.INSTAGRAM_PASSWORD = os.getenv('INSTAGRAM_PASSWORD', '')
        
        # Monitoring settings
        self.MONITORING_INTERVAL_HOURS = int(os.getenv('MONITORING_INTERVAL_HOURS', '2'))
        self.DATA_RETENTION_DAYS = int(os.getenv('DATA_RETENTION_DAYS', '365'))
        
        # Security settings
        self.ENCRYPTION_KEY = self._get_required('ENCRYPTION_KEY', self._generate_encryption_key())
        
        # Logging settings
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.LOG_FILE = os.getenv('LOG_FILE', 'instagram_monitor.log')
        
        # Rate limiting settings
        self.MIN_REQUEST_DELAY = float(os.getenv('MIN_REQUEST_DELAY', '1.0'))
        self.MAX_REQUEST_DELAY = float(os.getenv('MAX_REQUEST_DELAY', '3.0'))
        self.MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
        self.PROFILE_PROCESSING_DELAY = float(os.getenv('PROFILE_PROCESSING_DELAY', '5.0'))
        
        # Validate configuration
        self._validate_config()
    
    def _get_bool(self, key: str, default: bool) -> bool:
        """Get boolean value from environment variable."""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def _get_required(self, key: str, default: Optional[str] = None) -> str:
        """Get required environment variable or use default."""
        value = os.getenv(key, default)
        if not value:
            if default is None:
                raise ValueError(f"Required environment variable {key} is not set")
            return default
        return value
    
    def _generate_secret_key(self) -> str:
        """Generate a random secret key for Flask sessions."""
        import secrets
        return secrets.token_hex(32)
    
    def _generate_encryption_key(self) -> str:
        """Generate a random encryption key for credential storage."""
        from cryptography.fernet import Fernet
        return Fernet.generate_key().decode()
    
    def _validate_config(self) -> None:
        """Validate configuration settings."""
        if self.MONITORING_INTERVAL_HOURS < 1:
            raise ValueError("MONITORING_INTERVAL_HOURS must be at least 1")
        
        if self.DATA_RETENTION_DAYS < 1:
            raise ValueError("DATA_RETENTION_DAYS must be at least 1")
        
        if self.MIN_REQUEST_DELAY < 0:
            raise ValueError("MIN_REQUEST_DELAY must be non-negative")
        
        if self.MAX_REQUEST_DELAY < self.MIN_REQUEST_DELAY:
            raise ValueError("MAX_REQUEST_DELAY must be >= MIN_REQUEST_DELAY")
        
        if self.MAX_RETRIES < 0:
            raise ValueError("MAX_RETRIES must be non-negative")
    
    def get_database_path(self) -> Path:
        """Get the database file path."""
        return self.DATABASE_PATH
    
    def get_log_path(self) -> Path:
        """Get the log file path."""
        return Path(__file__).parent / self.LOG_FILE
    
    def to_dict(self) -> dict:
        """Convert configuration to dictionary (excluding sensitive data)."""
        return {
            'DEBUG': self.DEBUG,
            'HOST': self.HOST,
            'PORT': self.PORT,
            'DATABASE_URL': self.DATABASE_URL,
            'MONITORING_INTERVAL_HOURS': self.MONITORING_INTERVAL_HOURS,
            'DATA_RETENTION_DAYS': self.DATA_RETENTION_DAYS,
            'LOG_LEVEL': self.LOG_LEVEL,
            'LOG_FILE': self.LOG_FILE,
            'MIN_REQUEST_DELAY': self.MIN_REQUEST_DELAY,
            'MAX_REQUEST_DELAY': self.MAX_REQUEST_DELAY,
            'MAX_RETRIES': self.MAX_RETRIES,
            'PROFILE_PROCESSING_DELAY': self.PROFILE_PROCESSING_DELAY,
        }