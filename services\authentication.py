"""
Authentication Manager for Instagram Follower Monitor

This module handles secure credential storage, session management,
and authentication flows for Instagram access.
"""

import os
import json
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from config import Config


logger = logging.getLogger(__name__)


class AuthenticationManager:
    """Manages secure storage and retrieval of Instagram credentials."""
    
    def __init__(self, config: Config):
        """Initialize the authentication manager.
        
        Args:
            config: Application configuration instance
        """
        self.config = config
        self._fernet = Fernet(config.ENCRYPTION_KEY.encode())
        self._credentials_file = Path(__file__).parent.parent / '.credentials.enc'
        self._session_file = Path(__file__).parent.parent / '.session.enc'
        
    def store_credentials(self, username: str, password: str) -> bool:
        """Store Instagram credentials securely.
        
        Args:
            username: Instagram username
            password: Instagram password
            
        Returns:
            bool: True if credentials were stored successfully
        """
        try:
            credentials = {
                'username': username,
                'password': password,
                'stored_at': str(Path(__file__).parent.parent)
            }
            
            # Encrypt credentials
            encrypted_data = self._fernet.encrypt(
                json.dumps(credentials).encode()
            )
            
            # Store encrypted credentials
            with open(self._credentials_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Set restrictive permissions
            os.chmod(self._credentials_file, 0o600)
            
            logger.info(f"Credentials stored successfully for user: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store credentials: {e}")
            return False
    
    def retrieve_credentials(self) -> Optional[Dict[str, str]]:
        """Retrieve stored Instagram credentials.
        
        Returns:
            Optional[Dict[str, str]]: Dictionary with username and password,
                                    or None if no credentials are stored
        """
        try:
            if not self._credentials_file.exists():
                logger.debug("No credentials file found")
                return None
            
            # Read encrypted credentials
            with open(self._credentials_file, 'rb') as f:
                encrypted_data = f.read()
            
            # Decrypt credentials
            decrypted_data = self._fernet.decrypt(encrypted_data)
            credentials = json.loads(decrypted_data.decode())
            
            # Return only username and password
            return {
                'username': credentials['username'],
                'password': credentials['password']
            }
            
        except Exception as e:
            logger.error(f"Failed to retrieve credentials: {e}")
            return None
    
    def has_stored_credentials(self) -> bool:
        """Check if credentials are stored.
        
        Returns:
            bool: True if credentials are available
        """
        return self._credentials_file.exists()
    
    def clear_credentials(self) -> bool:
        """Clear stored credentials.
        
        Returns:
            bool: True if credentials were cleared successfully
        """
        try:
            if self._credentials_file.exists():
                self._credentials_file.unlink()
                logger.info("Credentials cleared successfully")
            
            if self._session_file.exists():
                self._session_file.unlink()
                logger.info("Session data cleared successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear credentials: {e}")
            return False
    
    def store_session_data(self, session_data: Dict[str, Any]) -> bool:
        """Store session data securely.
        
        Args:
            session_data: Dictionary containing session information
            
        Returns:
            bool: True if session data was stored successfully
        """
        try:
            # Encrypt session data
            encrypted_data = self._fernet.encrypt(
                json.dumps(session_data).encode()
            )
            
            # Store encrypted session data
            with open(self._session_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Set restrictive permissions
            os.chmod(self._session_file, 0o600)
            
            logger.debug("Session data stored successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store session data: {e}")
            return False
    
    def retrieve_session_data(self) -> Optional[Dict[str, Any]]:
        """Retrieve stored session data.
        
        Returns:
            Optional[Dict[str, Any]]: Session data dictionary or None
        """
        try:
            if not self._session_file.exists():
                logger.debug("No session file found")
                return None
            
            # Read encrypted session data
            with open(self._session_file, 'rb') as f:
                encrypted_data = f.read()
            
            # Decrypt session data
            decrypted_data = self._fernet.decrypt(encrypted_data)
            session_data = json.loads(decrypted_data.decode())
            
            return session_data
            
        except Exception as e:
            logger.error(f"Failed to retrieve session data: {e}")
            return None
    
    def validate_credentials(self, username: str, password: str) -> bool:
        """Validate credential format and basic requirements.
        
        Args:
            username: Instagram username to validate
            password: Instagram password to validate
            
        Returns:
            bool: True if credentials meet basic requirements
        """
        if not username or not password:
            logger.warning("Username or password is empty")
            return False
        
        if len(username) < 1 or len(username) > 30:
            logger.warning("Username length is invalid")
            return False
        
        if len(password) < 6:
            logger.warning("Password is too short")
            return False
        
        # Check for valid username characters (Instagram allows letters, numbers, periods, underscores)
        valid_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789._')
        if not all(c in valid_chars for c in username):
            logger.warning("Username contains invalid characters")
            return False
        
        return True