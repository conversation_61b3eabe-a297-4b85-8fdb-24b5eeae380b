"""Integration tests for repository operations with real database scenarios."""

import pytest
import os
import tempfile
from datetime import datetime, timedelta
from typing import List, Set
import time

from models.data_models import (
    ProfileInfo, FollowerChange, MonitoringConfig, ChangeType
)
from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, FollowerRepository, ChangeRepository, DataRetentionManager
)


@pytest.fixture
def integration_db():
    """Create a persistent test database for integration testing."""
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_file.close()
    
    db_manager = DatabaseManager(temp_file.name)
    db_manager.migrate_schema()
    
    yield db_manager
    
    # Cleanup
    os.unlink(temp_file.name)


@pytest.fixture
def repositories(integration_db, monkeypatch):
    """Create all repository instances with shared database."""
    # Patch the global db_manager to use our test database
    monkeypatch.setattr('database.repositories.db_manager', integration_db)
    
    return {
        'profile': ProfileRepository(),
        'follower': FollowerRepository(),
        'change': ChangeRepository(),
        'retention': DataRetentionManager(retention_days=30)
    }


class TestRepositoryIntegration:
    """Integration tests for repository operations."""
    
    def test_complete_monitoring_workflow(self, repositories):
        """Test complete workflow from profile creation to change tracking."""
        profile_repo = repositories['profile']
        follower_repo = repositories['follower']
        change_repo = repositories['change']
        
        # Step 1: Create a profile
        config = MonitoringConfig(
            profile_username="testuser",
            display_name="Test User",
            enabled=True
        )
        profile_id = profile_repo.create_profile(config)
        assert profile_id is not None
        
        # Step 2: Store initial followers/following
        initial_followers = {"follower1", "follower2", "follower3"}
        initial_following = {"following1", "following2"}
        
        follower_repo.store_current_followers(profile_id, initial_followers)
        follower_repo.store_current_following(profile_id, initial_following)
        
        # Verify initial data
        stored_followers = follower_repo.get_current_followers(profile_id)
        stored_following = follower_repo.get_current_following(profile_id)
        
        assert stored_followers == initial_followers
        assert stored_following == initial_following
        
        # Step 3: Simulate changes
        new_followers = {"follower1", "follower2", "follower4"}  # follower3 lost, follower4 gained
        new_following = {"following1", "following3"}  # following2 stopped, following3 started
        
        # Calculate changes
        gained_followers = new_followers - initial_followers
        lost_followers = initial_followers - new_followers
        started_following = new_following - initial_following
        stopped_following = initial_following - new_following
        
        # Create change records
        changes = []
        for username in gained_followers:
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=username,
                change_type=ChangeType.GAINED,
                profile_id=profile_id
            ))
        
        for username in lost_followers:
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=username,
                change_type=ChangeType.LOST,
                profile_id=profile_id
            ))
        
        for username in started_following:
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=username,
                change_type=ChangeType.STARTED_FOLLOWING,
                profile_id=profile_id
            ))
        
        for username in stopped_following:
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=username,
                change_type=ChangeType.STOPPED_FOLLOWING,
                profile_id=profile_id
            ))
        
        # Store changes
        change_repo.store_follower_changes(changes)
        
        # Update current lists
        follower_repo.store_current_followers(profile_id, new_followers)
        follower_repo.store_current_following(profile_id, new_following)
        
        # Step 4: Verify changes were recorded
        recent_changes = change_repo.get_recent_changes(profile_id=profile_id)
        
        assert len(recent_changes) == 4
        
        change_types = {change.change_type for change in recent_changes}
        expected_types = {ChangeType.GAINED, ChangeType.LOST, ChangeType.STARTED_FOLLOWING, ChangeType.STOPPED_FOLLOWING}
        assert change_types == expected_types
        
        # Step 5: Verify current data is updated
        current_followers = follower_repo.get_current_followers(profile_id)
        current_following = follower_repo.get_current_following(profile_id)
        
        assert current_followers == new_followers
        assert current_following == new_following
    
    def test_bulk_operations_performance(self, repositories):
        """Test bulk operations with larger datasets."""
        profile_repo = repositories['profile']
        follower_repo = repositories['follower']
        
        # Create multiple profiles
        profiles = []
        for i in range(5):
            config = MonitoringConfig(
                profile_username=f"user{i}",
                display_name=f"User {i}",
                enabled=True
            )
            profile_id = profile_repo.create_profile(config)
            profiles.append((profile_id, f"user{i}"))
        
        # Prepare bulk data
        profile_followers_map = {}
        profile_following_map = {}
        
        for profile_id, username in profiles:
            # Generate large follower/following lists
            followers = {f"follower_{username}_{j}" for j in range(100)}
            following = {f"following_{username}_{j}" for j in range(50)}
            
            profile_followers_map[profile_id] = followers
            profile_following_map[profile_id] = following
        
        # Test bulk operations
        start_time = time.time()
        follower_repo.bulk_store_followers(profile_followers_map)
        follower_repo.bulk_store_following(profile_following_map)
        bulk_time = time.time() - start_time
        
        # Verify data was stored correctly
        for profile_id, expected_followers in profile_followers_map.items():
            stored_followers = follower_repo.get_current_followers(profile_id)
            assert stored_followers == expected_followers
        
        for profile_id, expected_following in profile_following_map.items():
            stored_following = follower_repo.get_current_following(profile_id)
            assert stored_following == expected_following
        
        # Bulk operations should be reasonably fast
        assert bulk_time < 5.0  # Should complete within 5 seconds
    
    def test_data_retention_lifecycle(self, repositories):
        """Test complete data retention lifecycle."""
        profile_repo = repositories['profile']
        change_repo = repositories['change']
        retention_manager = repositories['retention']
        
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Create changes with different ages
        now = datetime.now()
        changes = []
        
        # Recent changes (should be kept)
        for i in range(5):
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=f"recent_user_{i}",
                change_type=ChangeType.GAINED,
                timestamp=now - timedelta(days=i),
                profile_id=profile_id
            ))
        
        # Old changes (should be deleted)
        for i in range(5):
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=f"old_user_{i}",
                change_type=ChangeType.GAINED,
                timestamp=now - timedelta(days=35 + i),  # Older than 30-day retention
                profile_id=profile_id
            ))
        
        # Store all changes
        change_repo.store_follower_changes(changes)
        
        # Verify all changes are stored
        all_changes = change_repo.get_recent_changes(profile_id=profile_id, limit=20)
        assert len(all_changes) == 10
        
        # Run cleanup
        cleanup_stats = retention_manager.cleanup_old_data()
        
        # Verify old data was removed
        remaining_changes = change_repo.get_recent_changes(profile_id=profile_id, limit=20)
        assert len(remaining_changes) == 5
        
        # Verify only recent changes remain
        for change in remaining_changes:
            assert "recent_user" in change.affected_username
        
        # Verify cleanup stats
        assert cleanup_stats['follower_changes_deleted'] == 5
    
    def test_search_and_filtering_performance(self, repositories):
        """Test search and filtering operations with realistic data."""
        profile_repo = repositories['profile']
        change_repo = repositories['change']
        
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Create many changes with searchable usernames
        changes = []
        now = datetime.now()
        
        # Create changes with specific patterns
        patterns = ["john", "jane", "mike", "sarah", "alex"]
        for i, pattern in enumerate(patterns):
            for j in range(20):
                changes.append(FollowerChange(
                    profile_username="testuser",
                    affected_username=f"{pattern}_{j}",
                    change_type=ChangeType.GAINED if j % 2 == 0 else ChangeType.LOST,
                    timestamp=now - timedelta(hours=i * 24 + j),
                    profile_id=profile_id
                ))
        
        change_repo.store_follower_changes(changes)
        
        # Test search functionality
        john_changes = change_repo.search_changes("john", profile_id=profile_id)
        assert len(john_changes) == 20
        assert all("john" in change.affected_username for change in john_changes)
        
        # Test date range filtering
        start_date = now - timedelta(days=2)
        end_date = now
        recent_changes = change_repo.get_changes_by_date_range(start_date, end_date, profile_id)
        
        # Should have changes from first 2 days only
        assert len(recent_changes) > 0
        assert all(change.timestamp >= start_date for change in recent_changes)
        assert all(change.timestamp <= end_date for change in recent_changes)
        
        # Test statistics
        stats = change_repo.get_change_statistics(profile_id=profile_id, days=7)
        assert stats['followers_gained'] > 0
        assert stats['followers_lost'] > 0
    
    def test_profile_statistics_accuracy(self, repositories):
        """Test profile statistics calculation accuracy."""
        profile_repo = repositories['profile']
        follower_repo = repositories['follower']
        change_repo = repositories['change']
        
        # Create profile
        config = MonitoringConfig(profile_username="testuser")
        profile_id = profile_repo.create_profile(config)
        
        # Set up current followers/following
        current_followers = {f"follower_{i}" for i in range(100)}
        current_following = {f"following_{i}" for i in range(50)}
        
        follower_repo.store_current_followers(profile_id, current_followers)
        follower_repo.store_current_following(profile_id, current_following)
        
        # Create changes in last 30 days
        now = datetime.now()
        changes = []
        
        # 10 gained, 5 lost followers
        for i in range(10):
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=f"gained_{i}",
                change_type=ChangeType.GAINED,
                timestamp=now - timedelta(days=i),
                profile_id=profile_id
            ))
        
        for i in range(5):
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=f"lost_{i}",
                change_type=ChangeType.LOST,
                timestamp=now - timedelta(days=i),
                profile_id=profile_id
            ))
        
        # 3 started following, 2 stopped following
        for i in range(3):
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=f"started_{i}",
                change_type=ChangeType.STARTED_FOLLOWING,
                timestamp=now - timedelta(days=i),
                profile_id=profile_id
            ))
        
        for i in range(2):
            changes.append(FollowerChange(
                profile_username="testuser",
                affected_username=f"stopped_{i}",
                change_type=ChangeType.STOPPED_FOLLOWING,
                timestamp=now - timedelta(days=i),
                profile_id=profile_id
            ))
        
        change_repo.store_follower_changes(changes)
        
        # Get profile statistics
        stats = profile_repo.get_profile_stats(profile_id)
        
        # Verify statistics
        assert stats['current_followers'] == 100
        assert stats['current_following'] == 50
        assert stats['followers_gained_30d'] == 10
        assert stats['followers_lost_30d'] == 5
        assert stats['following_started_30d'] == 3
        assert stats['following_stopped_30d'] == 2
    
    def test_database_optimization(self, repositories):
        """Test database optimization operations."""
        retention_manager = repositories['retention']
        
        # Get initial stats
        initial_stats = retention_manager.get_data_size_stats()
        
        # Run optimization
        optimization_stats = retention_manager.optimize_database()
        
        # Verify optimization completed
        assert 'database_size_bytes' in optimization_stats
        assert optimization_stats['database_size_bytes'] > 0
        
        # Verify table row counts are included
        expected_tables = ['profiles', 'current_followers', 'current_following', 
                          'follower_changes', 'following_changes', 'settings']
        
        for table in expected_tables:
            assert f'{table}_rows' in optimization_stats
            assert isinstance(optimization_stats[f'{table}_rows'], int)
    
    def test_concurrent_operations(self, repositories):
        """Test repository operations under concurrent access patterns."""
        profile_repo = repositories['profile']
        follower_repo = repositories['follower']
        
        # Create multiple profiles
        profile_ids = []
        for i in range(3):
            config = MonitoringConfig(profile_username=f"concurrent_user_{i}")
            profile_id = profile_repo.create_profile(config)
            profile_ids.append(profile_id)
        
        # Simulate concurrent updates
        for profile_id in profile_ids:
            followers = {f"follower_{profile_id}_{j}" for j in range(10)}
            following = {f"following_{profile_id}_{j}" for j in range(5)}
            
            follower_repo.store_current_followers(profile_id, followers)
            follower_repo.store_current_following(profile_id, following)
        
        # Verify all data was stored correctly
        for profile_id in profile_ids:
            stored_followers = follower_repo.get_current_followers(profile_id)
            stored_following = follower_repo.get_current_following(profile_id)
            
            assert len(stored_followers) == 10
            assert len(stored_following) == 5
            
            # Verify data integrity
            for follower in stored_followers:
                assert follower.startswith(f"follower_{profile_id}_")
            
            for following in stored_following:
                assert following.startswith(f"following_{profile_id}_")
    
    def test_error_handling_and_recovery(self, repositories):
        """Test error handling and data consistency."""
        profile_repo = repositories['profile']
        
        # Test duplicate profile creation
        config = MonitoringConfig(profile_username="duplicate_test")
        profile_id1 = profile_repo.create_profile(config)
        
        # Attempting to create duplicate should handle gracefully
        with pytest.raises(Exception):  # Should raise constraint violation
            profile_repo.create_profile(config)
        
        # Original profile should still exist
        retrieved = profile_repo.get_profile_by_username("duplicate_test")
        assert retrieved is not None
        assert retrieved.profile_id == profile_id1
        
        # Test invalid username handling
        with pytest.raises(ValueError):
            profile_repo.get_profile_by_username("")
        
        # Test update non-existent profile
        invalid_config = MonitoringConfig(profile_username="nonexistent", profile_id=99999)
        result = profile_repo.update_profile(invalid_config)
        assert result is False