"""
Profile scanner for retrieving Instagram profile data.

This module handles the scanning of individual Instagram profiles to retrieve
current follower and following lists along with profile information.
"""

import logging
from typing import Dict, Any, Set, Optional
from datetime import datetime

from models.data_models import ProfileInfo, UserList, validate_username
from services.instagram_client import InstagramClient
from config import Config

logger = logging.getLogger(__name__)


class ProfileScanner:
    """Handles scanning of individual Instagram profiles."""
    
    def __init__(self, instagram_client: InstagramClient, config: Config):
        """Initialize profile scanner.
        
        Args:
            instagram_client: Instagram client instance
            config: Application configuration
        """
        self.instagram_client = instagram_client
        self.config = config
        self._scan_stats = {
            'total_scans': 0,
            'successful_scans': 0,
            'failed_scans': 0,
            'profiles_scanned': set(),
            'last_scan_time': None,
            'average_scan_duration': 0.0
        }
    
    def scan_profile(self, username: str, include_followers: bool = True, 
                    include_following: bool = True, max_followers: Optional[int] = None,
                    max_following: Optional[int] = None) -> Dict[str, Any]:
        """Scan a profile and retrieve current data.
        
        Args:
            username: Instagram username to scan
            include_followers: Whether to fetch followers list
            include_following: Whether to fetch following list
            max_followers: Maximum number of followers to fetch (None for all)
            max_following: Maximum number of following to fetch (None for all)
            
        Returns:
            Dict[str, Any]: Scan results containing profile info and lists
        """
        start_time = datetime.now()
        
        try:
            # Validate and clean username
            username = validate_username(username)
            
            logger.info(f"Starting profile scan for: {username}")
            
            # Check if client is authenticated
            if not self.instagram_client.is_authenticated():
                return {
                    'success': False,
                    'error': 'Instagram client not authenticated',
                    'username': username
                }
            
            # Get profile information first
            profile_info = self.instagram_client.get_profile_info(username)
            if not profile_info:
                return {
                    'success': False,
                    'error': f'Could not retrieve profile information for {username}',
                    'username': username
                }
            
            logger.debug(f"Retrieved profile info for {username}: {profile_info.follower_count} followers, {profile_info.following_count} following")
            
            # Initialize result data
            scan_data = {
                'profile_info': profile_info,
                'followers': set(),
                'following': set(),
                'scan_timestamp': start_time
            }
            
            # Fetch followers if requested
            if include_followers:
                followers_result = self._fetch_followers(username, max_followers)
                if followers_result['success']:
                    scan_data['followers'] = followers_result['followers']
                    logger.info(f"Retrieved {len(scan_data['followers'])} followers for {username}")
                else:
                    logger.warning(f"Failed to fetch followers for {username}: {followers_result.get('error', 'Unknown error')}")
                    # Continue with scan even if followers fetch fails
            
            # Fetch following if requested
            if include_following:
                following_result = self._fetch_following(username, max_following)
                if following_result['success']:
                    scan_data['following'] = following_result['following']
                    logger.info(f"Retrieved {len(scan_data['following'])} following for {username}")
                else:
                    logger.warning(f"Failed to fetch following for {username}: {following_result.get('error', 'Unknown error')}")
                    # Continue with scan even if following fetch fails
            
            # Update scan statistics
            self._update_scan_stats(username, start_time, True)
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"Profile scan completed for {username} in {duration:.2f}s")
            
            return {
                'success': True,
                'username': username,
                'data': scan_data,
                'duration': duration,
                'followers_count': len(scan_data['followers']),
                'following_count': len(scan_data['following'])
            }
            
        except Exception as e:
            logger.error(f"Profile scan failed for {username}: {e}")
            self._update_scan_stats(username, start_time, False)
            
            return {
                'success': False,
                'error': str(e),
                'username': username,
                'duration': (datetime.now() - start_time).total_seconds()
            }
    
    def _fetch_followers(self, username: str, max_count: Optional[int] = None) -> Dict[str, Any]:
        """Fetch followers list for a profile.
        
        Args:
            username: Instagram username
            max_count: Maximum number of followers to fetch
            
        Returns:
            Dict[str, Any]: Fetch results
        """
        try:
            logger.debug(f"Fetching followers for {username} (max: {max_count})")
            
            followers = self.instagram_client.get_followers(username, max_count)
            
            if followers is None:
                return {
                    'success': False,
                    'error': 'Failed to fetch followers from Instagram',
                    'followers': set()
                }
            
            # Validate and clean follower usernames
            clean_followers = set()
            for follower in followers:
                try:
                    clean_follower = validate_username(follower)
                    clean_followers.add(clean_follower)
                except ValueError as e:
                    logger.warning(f"Invalid follower username '{follower}' for {username}: {e}")
                    continue
            
            logger.debug(f"Successfully fetched {len(clean_followers)} followers for {username}")
            
            return {
                'success': True,
                'followers': clean_followers,
                'count': len(clean_followers)
            }
            
        except Exception as e:
            logger.error(f"Error fetching followers for {username}: {e}")
            return {
                'success': False,
                'error': str(e),
                'followers': set()
            }
    
    def _fetch_following(self, username: str, max_count: Optional[int] = None) -> Dict[str, Any]:
        """Fetch following list for a profile.
        
        Args:
            username: Instagram username
            max_count: Maximum number of following to fetch
            
        Returns:
            Dict[str, Any]: Fetch results
        """
        try:
            logger.debug(f"Fetching following for {username} (max: {max_count})")
            
            following = self.instagram_client.get_following(username, max_count)
            
            if following is None:
                return {
                    'success': False,
                    'error': 'Failed to fetch following from Instagram',
                    'following': set()
                }
            
            # Validate and clean following usernames
            clean_following = set()
            for followee in following:
                try:
                    clean_followee = validate_username(followee)
                    clean_following.add(clean_followee)
                except ValueError as e:
                    logger.warning(f"Invalid following username '{followee}' for {username}: {e}")
                    continue
            
            logger.debug(f"Successfully fetched {len(clean_following)} following for {username}")
            
            return {
                'success': True,
                'following': clean_following,
                'count': len(clean_following)
            }
            
        except Exception as e:
            logger.error(f"Error fetching following for {username}: {e}")
            return {
                'success': False,
                'error': str(e),
                'following': set()
            }
    
    def scan_profile_basic_info(self, username: str) -> Dict[str, Any]:
        """Scan only basic profile information without followers/following lists.
        
        Args:
            username: Instagram username to scan
            
        Returns:
            Dict[str, Any]: Basic profile information
        """
        try:
            username = validate_username(username)
            
            if not self.instagram_client.is_authenticated():
                return {
                    'success': False,
                    'error': 'Instagram client not authenticated',
                    'username': username
                }
            
            profile_info = self.instagram_client.get_profile_info(username)
            if not profile_info:
                return {
                    'success': False,
                    'error': f'Could not retrieve profile information for {username}',
                    'username': username
                }
            
            return {
                'success': True,
                'username': username,
                'profile_info': profile_info
            }
            
        except Exception as e:
            logger.error(f"Basic profile scan failed for {username}: {e}")
            return {
                'success': False,
                'error': str(e),
                'username': username
            }
    
    def validate_profile_access(self, username: str) -> Dict[str, Any]:
        """Validate that a profile can be accessed and monitored.
        
        Args:
            username: Instagram username to validate
            
        Returns:
            Dict[str, Any]: Validation results
        """
        try:
            username = validate_username(username)
            
            if not self.instagram_client.is_authenticated():
                return {
                    'success': False,
                    'accessible': False,
                    'error': 'Instagram client not authenticated',
                    'username': username
                }
            
            # Try to get basic profile info
            profile_info = self.instagram_client.get_profile_info(username)
            if not profile_info:
                return {
                    'success': False,
                    'accessible': False,
                    'error': f'Profile {username} not found or not accessible',
                    'username': username
                }
            
            # Check if profile is private and we can access it
            accessible = True
            access_level = 'public'
            
            if profile_info.is_private:
                # For private profiles, we need to check if we can access followers
                # This is a lightweight check - just try to get a small sample
                followers = self.instagram_client.get_followers(username, max_count=1)
                if followers is None:
                    accessible = False
                    access_level = 'private_no_access'
                else:
                    access_level = 'private_accessible'
            
            return {
                'success': True,
                'accessible': accessible,
                'username': username,
                'profile_info': profile_info,
                'access_level': access_level,
                'is_private': profile_info.is_private,
                'follower_count': profile_info.follower_count,
                'following_count': profile_info.following_count
            }
            
        except Exception as e:
            logger.error(f"Profile validation failed for {username}: {e}")
            return {
                'success': False,
                'accessible': False,
                'error': str(e),
                'username': username
            }
    
    def _update_scan_stats(self, username: str, start_time: datetime, success: bool) -> None:
        """Update scanning statistics.
        
        Args:
            username: Username that was scanned
            start_time: When the scan started
            success: Whether the scan was successful
        """
        try:
            self._scan_stats['total_scans'] += 1
            self._scan_stats['profiles_scanned'].add(username)
            self._scan_stats['last_scan_time'] = datetime.now()
            
            if success:
                self._scan_stats['successful_scans'] += 1
            else:
                self._scan_stats['failed_scans'] += 1
            
            # Update average scan duration
            duration = (datetime.now() - start_time).total_seconds()
            current_avg = self._scan_stats['average_scan_duration']
            total_scans = self._scan_stats['total_scans']
            
            # Calculate running average
            self._scan_stats['average_scan_duration'] = (
                (current_avg * (total_scans - 1) + duration) / total_scans
            )
            
        except Exception as e:
            logger.error(f"Error updating scan stats: {e}")
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """Get current scanning statistics.
        
        Returns:
            Dict[str, Any]: Scanning statistics
        """
        stats = self._scan_stats.copy()
        
        # Convert set to list for JSON serialization
        stats['profiles_scanned'] = list(stats['profiles_scanned'])
        
        # Add success rate
        if stats['total_scans'] > 0:
            stats['success_rate'] = stats['successful_scans'] / stats['total_scans']
        else:
            stats['success_rate'] = 0.0
        
        # Format last scan time
        if stats['last_scan_time']:
            stats['last_scan_time'] = stats['last_scan_time'].isoformat()
        
        return stats
    
    def reset_statistics(self) -> None:
        """Reset scanning statistics."""
        self._scan_stats = {
            'total_scans': 0,
            'successful_scans': 0,
            'failed_scans': 0,
            'profiles_scanned': set(),
            'last_scan_time': None,
            'average_scan_duration': 0.0
        }
        logger.info("Profile scanner statistics reset")
    
    def create_user_list(self, profile_username: str, usernames: Set[str], 
                        list_type: str) -> UserList:
        """Create a UserList object from scan results.
        
        Args:
            profile_username: Username of the profile being scanned
            usernames: Set of usernames in the list
            list_type: Type of list ('followers' or 'following')
            
        Returns:
            UserList: Formatted user list object
        """
        return UserList(
            profile_username=profile_username,
            usernames=list(usernames),
            list_type=list_type,
            retrieved_at=datetime.now()
        )