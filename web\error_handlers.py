"""
Flask Error Handlers and Middleware

This module provides comprehensive error handling for the Flask application,
including custom error pages, API error responses, and error logging.
"""

import traceback
from typing import <PERSON><PERSON>, Dict, Any, Optional
from flask import Flask, request, jsonify, render_template, current_app
from werkzeug.exceptions import HTTPException
from werkzeug.http import HTTP_STATUS_CODES

from services.logging_config import get_logger, get_critical_notifier
from services.system_monitor import get_system_monitor, AlertLevel
from services.exceptions import (
    ApplicationError, ValidationError, AuthenticationError, AuthorizationError,
    ResourceNotFoundError, RateLimitError, ExternalServiceError
)


logger = get_logger(__name__)


def is_api_request() -> bool:
    """Check if the current request is an API request."""
    return (
        request.path.startswith('/api/') or
        request.headers.get('Content-Type', '').startswith('application/json') or
        'application/json' in request.headers.get('Accept', '')
    )


def log_error(error: Exception, request_info: Dict[str, Any] = None):
    """Log error with request context and traceback."""
    if request_info is None:
        request_info = {}
    
    # Gather request information
    try:
        request_data = {
            'method': request.method,
            'url': request.url,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'referrer': request.headers.get('Referer', ''),
            **request_info
        }
    except RuntimeError:
        # Outside request context
        request_data = request_info
    
    # Record error in system monitor
    monitor = get_system_monitor()
    monitor.record_error('web_application', error, request_data)
    
    # Log error with context
    if isinstance(error, ApplicationError):
        logger.error(
            f"Application error: {error.message}",
            error_code=error.error_code,
            status_code=error.status_code,
            details=error.details,
            **request_data
        )
        
        # Notify critical errors
        if error.status_code >= 500 or isinstance(error, (ExternalServiceError, AuthenticationError)):
            notifier = get_critical_notifier()
            notifier.notify_critical_error(error, 'web_application', request_data)
    else:
        logger.exception(
            f"Unhandled exception: {str(error)}",
            exception_type=type(error).__name__,
            **request_data
        )
        
        # All unhandled exceptions are critical
        notifier = get_critical_notifier()
        notifier.notify_critical_error(error, 'web_application', request_data)


def create_error_response(error: Exception, status_code: int = None) -> Tuple[Dict[str, Any], int]:
    """
    Create standardized error response.
    
    Args:
        error: Exception instance
        status_code: HTTP status code (overrides error's status code)
        
    Returns:
        Tuple of (response_dict, status_code)
    """
    if isinstance(error, ApplicationError):
        response = {
            'error': {
                'code': error.error_code,
                'message': error.message,
                'details': error.details
            }
        }
        code = status_code or error.status_code
    else:
        # Generic error response for unexpected exceptions
        response = {
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': 'An internal error occurred',
                'details': {}
            }
        }
        code = status_code or 500
        
        # Include exception details in debug mode
        if current_app.debug:
            response['error']['details'] = {
                'exception_type': type(error).__name__,
                'exception_message': str(error),
                'traceback': traceback.format_exc()
            }
    
    return response, code


def register_error_handlers(app: Flask):
    """Register error handlers with the Flask application."""
    
    @app.errorhandler(ApplicationError)
    def handle_application_error(error: ApplicationError):
        """Handle custom application errors."""
        log_error(error)
        
        if is_api_request():
            response, status_code = create_error_response(error)
            return jsonify(response), status_code
        else:
            return render_template(
                'errors/error.html',
                error_code=error.status_code,
                error_message=error.message,
                error_details=error.details
            ), error.status_code
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(error: ValidationError):
        """Handle validation errors."""
        log_error(error)
        
        if is_api_request():
            response, status_code = create_error_response(error)
            return jsonify(response), status_code
        else:
            return render_template(
                'errors/validation_error.html',
                error_message=error.message,
                field=error.details.get('field'),
                value=error.details.get('value')
            ), error.status_code
    
    @app.errorhandler(404)
    def handle_not_found(error):
        """Handle 404 Not Found errors."""
        log_error(error, {'status_code': 404})
        
        if is_api_request():
            return jsonify({
                'error': {
                    'code': 'NOT_FOUND',
                    'message': 'The requested resource was not found',
                    'details': {'path': request.path}
                }
            }), 404
        else:
            return render_template('errors/404.html'), 404
    
    @app.errorhandler(403)
    def handle_forbidden(error):
        """Handle 403 Forbidden errors."""
        log_error(error, {'status_code': 403})
        
        if is_api_request():
            return jsonify({
                'error': {
                    'code': 'FORBIDDEN',
                    'message': 'Access to this resource is forbidden',
                    'details': {}
                }
            }), 403
        else:
            return render_template('errors/403.html'), 403
    
    @app.errorhandler(405)
    def handle_method_not_allowed(error):
        """Handle 405 Method Not Allowed errors."""
        log_error(error, {'status_code': 405})
        
        if is_api_request():
            return jsonify({
                'error': {
                    'code': 'METHOD_NOT_ALLOWED',
                    'message': f'Method {request.method} not allowed for this endpoint',
                    'details': {'method': request.method, 'path': request.path}
                }
            }), 405
        else:
            return render_template('errors/405.html', method=request.method), 405
    
    @app.errorhandler(429)
    def handle_rate_limit(error):
        """Handle 429 Rate Limit Exceeded errors."""
        log_error(error, {'status_code': 429})
        
        retry_after = getattr(error, 'retry_after', None)
        
        if is_api_request():
            response = {
                'error': {
                    'code': 'RATE_LIMIT_EXCEEDED',
                    'message': 'Rate limit exceeded. Please try again later.',
                    'details': {'retry_after': retry_after}
                }
            }
            
            resp = jsonify(response)
            if retry_after:
                resp.headers['Retry-After'] = str(retry_after)
            return resp, 429
        else:
            return render_template(
                'errors/429.html',
                retry_after=retry_after
            ), 429
    
    @app.errorhandler(500)
    def handle_internal_error(error):
        """Handle 500 Internal Server Error."""
        log_error(error, {'status_code': 500})
        
        if is_api_request():
            response, status_code = create_error_response(error, 500)
            return jsonify(response), status_code
        else:
            return render_template('errors/500.html'), 500
    
    @app.errorhandler(502)
    def handle_bad_gateway(error):
        """Handle 502 Bad Gateway errors."""
        log_error(error, {'status_code': 502})
        
        if is_api_request():
            return jsonify({
                'error': {
                    'code': 'BAD_GATEWAY',
                    'message': 'External service unavailable',
                    'details': {}
                }
            }), 502
        else:
            return render_template('errors/502.html'), 502
    
    @app.errorhandler(503)
    def handle_service_unavailable(error):
        """Handle 503 Service Unavailable errors."""
        log_error(error, {'status_code': 503})
        
        if is_api_request():
            return jsonify({
                'error': {
                    'code': 'SERVICE_UNAVAILABLE',
                    'message': 'Service temporarily unavailable',
                    'details': {}
                }
            }), 503
        else:
            return render_template('errors/503.html'), 503
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error: HTTPException):
        """Handle generic HTTP exceptions."""
        log_error(error, {'status_code': error.code})
        
        if is_api_request():
            return jsonify({
                'error': {
                    'code': 'HTTP_ERROR',
                    'message': error.description or HTTP_STATUS_CODES.get(error.code, 'Unknown error'),
                    'details': {'status_code': error.code}
                }
            }), error.code
        else:
            return render_template(
                'errors/generic.html',
                error_code=error.code,
                error_message=error.description or HTTP_STATUS_CODES.get(error.code, 'Unknown error')
            ), error.code
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(error: Exception):
        """Handle unexpected exceptions."""
        log_error(error, {'status_code': 500})
        
        if is_api_request():
            response, status_code = create_error_response(error, 500)
            return jsonify(response), status_code
        else:
            return render_template('errors/500.html'), 500


def setup_request_logging(app: Flask):
    """Set up request/response logging middleware."""
    
    @app.before_request
    def log_request():
        """Log incoming requests."""
        # Skip logging for static files and health checks
        if request.path.startswith('/static/') or request.path == '/health':
            return
        
        logger.info(
            f"Request: {request.method} {request.path}",
            method=request.method,
            path=request.path,
            remote_addr=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            content_type=request.headers.get('Content-Type', ''),
            content_length=request.headers.get('Content-Length', 0)
        )
    
    @app.after_request
    def log_response(response):
        """Log outgoing responses."""
        # Skip logging for static files and health checks
        if request.path.startswith('/static/') or request.path == '/health':
            return response
        
        logger.info(
            f"Response: {response.status_code} for {request.method} {request.path}",
            method=request.method,
            path=request.path,
            status_code=response.status_code,
            content_type=response.headers.get('Content-Type', ''),
            content_length=response.headers.get('Content-Length', 0)
        )
        
        return response


class ErrorMonitor:
    """Monitor and track application errors for alerting."""
    
    def __init__(self):
        """Initialize error monitor."""
        self.error_counts = {}
        self.critical_errors = []
    
    def record_error(self, error: Exception, severity: str = 'error'):
        """Record an error occurrence."""
        error_key = f"{type(error).__name__}:{str(error)}"
        
        if error_key not in self.error_counts:
            self.error_counts[error_key] = 0
        
        self.error_counts[error_key] += 1
        
        # Track critical errors
        if severity == 'critical' or isinstance(error, (
            ExternalServiceError,
            AuthenticationError
        )):
            self.critical_errors.append({
                'error': error_key,
                'timestamp': logger.logger.makeRecord(
                    '', 0, '', 0, '', (), None
                ).created,
                'count': self.error_counts[error_key]
            })
            
            # Alert on critical errors
            self._alert_critical_error(error, self.error_counts[error_key])
    
    def _alert_critical_error(self, error: Exception, count: int):
        """Alert on critical errors (placeholder for notification system)."""
        logger.critical(
            f"Critical error alert: {type(error).__name__}",
            error_message=str(error),
            occurrence_count=count,
            alert_type='critical_error'
        )
        
        # TODO: Implement actual alerting mechanism (email, Slack, etc.)
        # This could integrate with external monitoring services
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors."""
        return {
            'total_errors': len(self.error_counts),
            'error_counts': dict(sorted(
                self.error_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]),  # Top 10 errors
            'critical_errors': len(self.critical_errors),
            'recent_critical': self.critical_errors[-5:] if self.critical_errors else []
        }


# Global error monitor instance
error_monitor = ErrorMonitor()