# Design Document

## Overview

The Instagram Follower Monitor is a Python-based application that tracks changes in Instagram followers and following lists for specified profiles. The system consists of a backend monitoring service built with Instaloader for Instagram data access, a Flask web application for the dashboard interface, and a SQLite database for persistent storage. The application handles Instagram's anti-bot measures through rate limiting and session management while providing a user-friendly web interface for configuration and data visualization.

## Architecture

The application follows a modular architecture with clear separation of concerns:

```mermaid
graph TB
    A[Web Dashboard - Flask] --> B[Backend Service]
    B --> C[Instagram API - Instaloader]
    B --> D[Database - SQLite]
    B --> E[Scheduler - APScheduler]
    F[Configuration Manager] --> B
    G[Authentication Manager] --> C
    H[Rate Limiter] --> C
```

### Core Components

1. **Web Dashboard (Flask)**: User interface for viewing data and configuration
2. **Backend Service**: Core monitoring logic and data processing
3. **Database Layer**: SQLite for data persistence with 1-year retention
4. **Instagram Interface**: Instaloader wrapper with rate limiting and authentication
5. **Scheduler**: APScheduler for periodic monitoring tasks
6. **Configuration Manager**: Settings and profile management
7. **Authentication Manager**: Secure credential storage and session handling

## Components and Interfaces

### 1. Instagram Interface Layer

**Purpose**: Handles all Instagram interactions with proper rate limiting and authentication.

**Key Classes**:
- `InstagramClient`: Main interface wrapping Instaloader functionality
- `RateLimiter`: Manages request timing and anti-bot measures
- `AuthenticationManager`: Handles login, session management, and credential storage

**Interface**:
```python
class InstagramClient:
    def authenticate(self, username: str, password: str) -> bool
    def get_profile_info(self, username: str) -> ProfileInfo
    def get_followers(self, username: str) -> List[str]
    def get_following(self, username: str) -> List[str]
    def is_authenticated(self) -> bool
```

**Rate Limiting Strategy**:
- Implement exponential backoff for 429 errors
- Use Instaloader's built-in rate controller
- Add random delays between requests (1-3 seconds)
- Monitor request patterns to avoid detection

### 2. Database Layer

**Purpose**: Persistent storage for follower data, changes, and configuration.

**Schema Design**:
```sql
-- Profile tracking configuration
CREATE TABLE profiles (
    id INTEGER PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    display_name TEXT,
    is_private BOOLEAN DEFAULT FALSE,
    monitoring_enabled BOOLEAN DEFAULT TRUE,
    last_scan TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Current follower/following snapshots
CREATE TABLE current_followers (
    profile_id INTEGER,
    follower_username TEXT,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id),
    PRIMARY KEY (profile_id, follower_username)
);

CREATE TABLE current_following (
    profile_id INTEGER,
    following_username TEXT,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id),
    PRIMARY KEY (profile_id, following_username)
);

-- Change history
CREATE TABLE follower_changes (
    id INTEGER PRIMARY KEY,
    profile_id INTEGER,
    username TEXT NOT NULL,
    change_type TEXT CHECK(change_type IN ('gained', 'lost')),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id)
);

CREATE TABLE following_changes (
    id INTEGER PRIMARY KEY,
    profile_id INTEGER,
    username TEXT NOT NULL,
    change_type TEXT CHECK(change_type IN ('started_following', 'stopped_following')),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id)
);

-- System configuration
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Classes**:
- `DatabaseManager`: Main database interface
- `ProfileRepository`: Profile-specific data operations
- `ChangeRepository`: Change tracking operations
- `DataRetentionManager`: Handles 1-year data cleanup

### 3. Backend Monitoring Service

**Purpose**: Core business logic for monitoring and change detection.

**Key Classes**:
- `MonitoringService`: Main orchestrator
- `ChangeDetector`: Compares current vs previous data
- `DataProcessor`: Processes and stores changes
- `ProfileScanner`: Handles individual profile scanning

**Monitoring Workflow**:
1. Retrieve list of enabled profiles from database
2. For each profile:
   - Authenticate if needed
   - Fetch current followers/following lists
   - Compare with stored baseline
   - Detect and categorize changes
   - Update database with new data and changes
   - Apply rate limiting between profiles
3. Schedule next monitoring cycle

**Change Detection Algorithm**:
```python
def detect_changes(current_followers: Set[str], previous_followers: Set[str]) -> Dict:
    return {
        'new_followers': current_followers - previous_followers,
        'lost_followers': previous_followers - current_followers,
        'unchanged': current_followers & previous_followers
    }
```

### 4. Web Dashboard (Flask)

**Purpose**: User interface for data visualization and system configuration.

**Key Routes**:
- `/`: Dashboard overview with current stats
- `/profile/<username>`: Detailed profile view
- `/changes`: Recent changes across all profiles
- `/settings`: Configuration panel
- `/api/*`: REST API endpoints for frontend

**Dashboard Features**:
- Real-time follower/following counts
- Recent changes timeline with timestamps
- Search and filter capabilities for user lists
- Profile management (add/remove/configure)
- Monitoring interval configuration
- Authentication status and credential management

**Frontend Technology**:
- HTML5/CSS3 with responsive design
- JavaScript for dynamic interactions
- Chart.js for data visualization
- Bootstrap for UI components

### 5. Scheduler Component

**Purpose**: Manages periodic monitoring tasks.

**Implementation**: APScheduler with SQLite job store for persistence

**Configuration**:
- Default interval: 2 hours (configurable)
- Retry logic for failed jobs
- Job persistence across application restarts
- Monitoring job status and error handling

## Data Models

### Core Data Structures

```python
@dataclass
class ProfileInfo:
    username: str
    display_name: str
    follower_count: int
    following_count: int
    is_private: bool
    last_updated: datetime

@dataclass
class FollowerChange:
    profile_username: str
    affected_username: str
    change_type: str  # 'gained', 'lost', 'started_following', 'stopped_following'
    timestamp: datetime

@dataclass
class MonitoringConfig:
    profile_username: str
    enabled: bool
    interval_hours: int
    last_scan: Optional[datetime]
```

## Error Handling

### Instagram API Errors

1. **Rate Limiting (429 errors)**:
   - Implement exponential backoff
   - Log rate limit encounters
   - Adjust monitoring frequency if needed

2. **Authentication Failures**:
   - Retry with stored credentials
   - Prompt for credential update via dashboard
   - Disable monitoring for affected profiles

3. **Profile Access Errors**:
   - Handle private profiles requiring authentication
   - Log access denied errors
   - Provide user feedback through dashboard

### Application Errors

1. **Database Errors**:
   - Connection retry logic
   - Data integrity validation
   - Backup and recovery procedures

2. **Network Errors**:
   - Connection timeout handling
   - Retry mechanisms with backoff
   - Graceful degradation of service

### Error Logging Strategy

- Structured logging with different levels (DEBUG, INFO, WARNING, ERROR)
- Separate log files for different components
- Log rotation and retention policies
- Error notification system for critical failures

## Testing Strategy

### Unit Testing

**Components to Test**:
- Instagram client wrapper functions
- Change detection algorithms
- Database operations
- Authentication management
- Rate limiting logic

**Testing Framework**: pytest with fixtures for database and mock Instagram responses

### Integration Testing

**Test Scenarios**:
- End-to-end monitoring workflow
- Database schema migrations
- Flask application routes and API endpoints
- Scheduler job execution
- Error handling and recovery

### Mock Testing

**Mock Components**:
- Instagram API responses using responses library
- Database operations for isolated testing
- Time-dependent functions for scheduler testing

### Performance Testing

**Areas to Test**:
- Database query performance with large datasets
- Memory usage during large follower list processing
- Response times for web dashboard
- Concurrent user handling

## Security Considerations

### Credential Management

1. **Encryption**: Store Instagram credentials using Fernet symmetric encryption
2. **Key Management**: Store encryption keys separately from credentials
3. **Access Control**: Limit credential access to authentication manager only
4. **Session Security**: Secure session token storage and rotation

### Web Application Security

1. **CSRF Protection**: Implement CSRF tokens for all forms
2. **Input Validation**: Sanitize all user inputs
3. **SQL Injection Prevention**: Use parameterized queries
4. **XSS Prevention**: Escape output and use Content Security Policy

### Data Protection

1. **Data Minimization**: Store only necessary user data
2. **Data Retention**: Automatic cleanup after 1 year
3. **Access Logging**: Log all data access attempts
4. **Backup Security**: Encrypt database backups

## Deployment Architecture

### Development Environment

- Local SQLite database
- Flask development server
- File-based logging
- Manual credential management

### Production Environment

- SQLite database with regular backups
- WSGI server (Gunicorn) with reverse proxy (Nginx)
- Centralized logging with log rotation
- Environment variable configuration
- Process monitoring and auto-restart

### Configuration Management

**Environment Variables**:
- `INSTAGRAM_USERNAME`: Default Instagram username
- `DATABASE_URL`: Database connection string
- `SECRET_KEY`: Flask secret key for sessions
- `ENCRYPTION_KEY`: Key for credential encryption
- `LOG_LEVEL`: Application logging level
- `MONITORING_INTERVAL`: Default monitoring interval in hours

## Performance Optimization

### Database Optimization

1. **Indexing Strategy**:
   - Index on profile_id and timestamp columns
   - Composite indexes for common query patterns
   - Regular index maintenance and analysis

2. **Query Optimization**:
   - Use prepared statements
   - Implement query result caching
   - Optimize JOIN operations

### Memory Management

1. **Data Processing**:
   - Process large follower lists in chunks
   - Implement pagination for web interface
   - Use generators for large dataset iteration

2. **Caching Strategy**:
   - Cache frequently accessed profile data
   - Implement Redis for session storage in production
   - Cache dashboard statistics

### Rate Limiting Optimization

1. **Intelligent Scheduling**:
   - Distribute monitoring tasks across time windows
   - Prioritize active profiles
   - Adjust intervals based on change frequency

2. **Request Optimization**:
   - Batch similar requests when possible
   - Implement request deduplication
   - Use conditional requests when supported