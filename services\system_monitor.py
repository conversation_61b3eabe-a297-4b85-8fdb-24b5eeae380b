"""
System Monitoring and Alerting Service

This module provides system monitoring capabilities including error tracking,
performance monitoring, and alerting for critical system failures.
"""

import time
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum

from services.logging_config import get_logger
from config import Config


logger = get_logger(__name__)


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class SystemAlert:
    """Represents a system alert."""
    level: AlertLevel
    message: str
    component: str
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)
    count: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary."""
        return {
            'level': self.level.value,
            'message': self.message,
            'component': self.component,
            'timestamp': self.timestamp.isoformat(),
            'details': self.details,
            'count': self.count
        }


@dataclass
class PerformanceMetric:
    """Represents a performance metric."""
    name: str
    value: float
    unit: str
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metric to dictionary."""
        return {
            'name': self.name,
            'value': self.value,
            'unit': self.unit,
            'timestamp': self.timestamp.isoformat(),
            'tags': self.tags
        }


class SystemMonitor:
    """System monitoring and alerting service."""
    
    def __init__(self, config: Config):
        """Initialize system monitor."""
        self.config = config
        self.alerts: deque = deque(maxlen=1000)  # Keep last 1000 alerts
        self.metrics: deque = deque(maxlen=5000)  # Keep last 5000 metrics
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.component_status: Dict[str, Dict[str, Any]] = {}
        self.alert_handlers: List[Callable[[SystemAlert], None]] = []
        
        # Performance tracking
        self.request_times: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.error_rates: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Thresholds
        self.error_rate_threshold = 0.1  # 10% error rate
        self.response_time_threshold = 5.0  # 5 seconds
        self.consecutive_error_threshold = 5
        
        # Monitoring thread
        self._monitoring_active = False
        self._monitoring_thread: Optional[threading.Thread] = None
        
        logger.info("System monitor initialized", 
                   alert_buffer_size=len(self.alerts),
                   metric_buffer_size=len(self.metrics))
    
    def start_monitoring(self):
        """Start background monitoring thread."""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        
        logger.info("System monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring thread."""
        self._monitoring_active = False
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5.0)
        
        logger.info("System monitoring stopped")
    
    def add_alert_handler(self, handler: Callable[[SystemAlert], None]):
        """Add an alert handler function."""
        self.alert_handlers.append(handler)
        logger.debug("Alert handler added", handler_count=len(self.alert_handlers))
    
    def record_error(self, component: str, error: Exception, details: Dict[str, Any] = None):
        """Record an error occurrence."""
        error_key = f"{component}:{type(error).__name__}"
        self.error_counts[error_key] += 1
        
        # Determine alert level based on error type and frequency
        if self.error_counts[error_key] >= self.consecutive_error_threshold:
            level = AlertLevel.CRITICAL
        elif isinstance(error, (ConnectionError, TimeoutError)):
            level = AlertLevel.ERROR
        else:
            level = AlertLevel.WARNING
        
        # Create alert
        alert = SystemAlert(
            level=level,
            message=f"Error in {component}: {str(error)}",
            component=component,
            details={
                'error_type': type(error).__name__,
                'error_count': self.error_counts[error_key],
                **(details or {})
            }
        )
        
        self._add_alert(alert)
        
        # Update component status
        self._update_component_status(component, 'error', {
            'last_error': str(error),
            'error_count': self.error_counts[error_key],
            'timestamp': datetime.now().isoformat()
        })
        
        logger.error("System error recorded", 
                    component=component,
                    error_type=type(error).__name__,
                    error_count=self.error_counts[error_key])
    
    def record_performance_metric(self, name: str, value: float, unit: str, 
                                 component: str = None, **tags):
        """Record a performance metric."""
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            tags={'component': component, **tags} if component else tags
        )
        
        self.metrics.append(metric)
        
        # Check for performance alerts
        self._check_performance_thresholds(metric)
        
        logger.debug("Performance metric recorded", 
                    name=name, value=value, unit=unit, component=component)
    
    def record_request_time(self, component: str, operation: str, duration: float):
        """Record request/operation timing."""
        self.request_times[f"{component}:{operation}"].append({
            'duration': duration,
            'timestamp': time.time()
        })
        
        # Record as metric
        self.record_performance_metric(
            name="request_duration",
            value=duration,
            unit="seconds",
            component=component,
            operation=operation
        )
        
        # Check for slow requests
        if duration > self.response_time_threshold:
            self._add_alert(SystemAlert(
                level=AlertLevel.WARNING,
                message=f"Slow request in {component}: {operation} took {duration:.2f}s",
                component=component,
                details={
                    'operation': operation,
                    'duration': duration,
                    'threshold': self.response_time_threshold
                }
            ))
    
    def update_component_health(self, component: str, status: str, details: Dict[str, Any] = None):
        """Update component health status."""
        self._update_component_status(component, status, details or {})
        
        if status in ['error', 'critical']:
            level = AlertLevel.CRITICAL if status == 'critical' else AlertLevel.ERROR
            self._add_alert(SystemAlert(
                level=level,
                message=f"Component {component} status: {status}",
                component=component,
                details=details or {}
            ))
        
        logger.info("Component health updated", 
                   component=component, status=status, details=details)
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health summary."""
        now = datetime.now()
        recent_alerts = [
            alert for alert in self.alerts 
            if (now - alert.timestamp) < timedelta(hours=1)
        ]
        
        critical_alerts = [alert for alert in recent_alerts if alert.level == AlertLevel.CRITICAL]
        error_alerts = [alert for alert in recent_alerts if alert.level == AlertLevel.ERROR]
        
        # Calculate error rates
        error_rates = {}
        for component_op, times in self.request_times.items():
            recent_times = [t for t in times if (now.timestamp() - t['timestamp']) < 3600]
            if recent_times:
                error_count = sum(1 for t in recent_times if t.get('error', False))
                error_rates[component_op] = error_count / len(recent_times)
        
        return {
            'status': self._calculate_overall_status(critical_alerts, error_alerts),
            'timestamp': now.isoformat(),
            'alerts': {
                'total': len(recent_alerts),
                'critical': len(critical_alerts),
                'error': len(error_alerts),
                'warning': len([a for a in recent_alerts if a.level == AlertLevel.WARNING])
            },
            'components': dict(self.component_status),
            'error_rates': error_rates,
            'metrics_count': len(self.metrics),
            'monitoring_active': self._monitoring_active
        }
    
    def get_recent_alerts(self, hours: int = 24, level: AlertLevel = None) -> List[Dict[str, Any]]:
        """Get recent alerts within specified time window."""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        filtered_alerts = [
            alert for alert in self.alerts
            if alert.timestamp >= cutoff and (level is None or alert.level == level)
        ]
        
        return [alert.to_dict() for alert in filtered_alerts]
    
    def get_performance_metrics(self, component: str = None, hours: int = 24) -> List[Dict[str, Any]]:
        """Get performance metrics within specified time window."""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        filtered_metrics = [
            metric for metric in self.metrics
            if metric.timestamp >= cutoff and (
                component is None or metric.tags.get('component') == component
            )
        ]
        
        return [metric.to_dict() for metric in filtered_metrics]
    
    def _add_alert(self, alert: SystemAlert):
        """Add alert and notify handlers."""
        # Check for duplicate recent alerts
        for existing_alert in reversed(list(self.alerts)[-10:]):  # Check last 10 alerts
            if (existing_alert.component == alert.component and 
                existing_alert.message == alert.message and
                (alert.timestamp - existing_alert.timestamp) < timedelta(minutes=5)):
                # Update existing alert count instead of creating new one
                existing_alert.count += 1
                existing_alert.timestamp = alert.timestamp
                return
        
        self.alerts.append(alert)
        
        # Notify handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error("Error in alert handler", handler=str(handler), error=str(e))
    
    def _update_component_status(self, component: str, status: str, details: Dict[str, Any]):
        """Update component status tracking."""
        if component not in self.component_status:
            self.component_status[component] = {}
        
        self.component_status[component].update({
            'status': status,
            'last_updated': datetime.now().isoformat(),
            **details
        })
    
    def _check_performance_thresholds(self, metric: PerformanceMetric):
        """Check if metric exceeds performance thresholds."""
        if metric.name == "request_duration" and metric.value > self.response_time_threshold:
            self._add_alert(SystemAlert(
                level=AlertLevel.WARNING,
                message=f"High response time: {metric.value:.2f}s",
                component=metric.tags.get('component', 'unknown'),
                details={
                    'metric_name': metric.name,
                    'value': metric.value,
                    'threshold': self.response_time_threshold,
                    'tags': metric.tags
                }
            ))
    
    def _calculate_overall_status(self, critical_alerts: List, error_alerts: List) -> str:
        """Calculate overall system status."""
        if critical_alerts:
            return 'critical'
        elif error_alerts:
            return 'degraded'
        elif any(status.get('status') == 'error' for status in self.component_status.values()):
            return 'warning'
        else:
            return 'healthy'
    
    def _monitoring_loop(self):
        """Background monitoring loop."""
        while self._monitoring_active:
            try:
                # Clean up old data
                self._cleanup_old_data()
                
                # Check component health
                self._check_component_health()
                
                # Sleep for monitoring interval
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.exception("Error in monitoring loop")
                time.sleep(10)  # Short sleep on error
    
    def _cleanup_old_data(self):
        """Clean up old monitoring data."""
        cutoff_time = time.time() - 86400  # 24 hours
        
        # Clean up request times
        for key, times in self.request_times.items():
            while times and times[0]['timestamp'] < cutoff_time:
                times.popleft()
    
    def _check_component_health(self):
        """Check health of all monitored components."""
        now = datetime.now()
        
        for component, status in self.component_status.items():
            last_updated = datetime.fromisoformat(status['last_updated'])
            
            # Check if component hasn't reported in a while
            if (now - last_updated) > timedelta(minutes=10):
                if status.get('status') != 'stale':
                    self._add_alert(SystemAlert(
                        level=AlertLevel.WARNING,
                        message=f"Component {component} hasn't reported status recently",
                        component=component,
                        details={
                            'last_updated': status['last_updated'],
                            'minutes_since_update': int((now - last_updated).total_seconds() / 60)
                        }
                    ))
                    status['status'] = 'stale'


# Global system monitor instance
_system_monitor: Optional[SystemMonitor] = None


def get_system_monitor(config: Config = None) -> SystemMonitor:
    """Get or create global system monitor instance."""
    global _system_monitor
    
    if _system_monitor is None:
        if config is None:
            config = Config()
        _system_monitor = SystemMonitor(config)
    
    return _system_monitor


def setup_default_alert_handlers():
    """Set up default alert handlers."""
    monitor = get_system_monitor()
    
    def log_alert_handler(alert: SystemAlert):
        """Log alerts to the logging system."""
        log_method = {
            AlertLevel.INFO: logger.info,
            AlertLevel.WARNING: logger.warning,
            AlertLevel.ERROR: logger.error,
            AlertLevel.CRITICAL: logger.critical
        }.get(alert.level, logger.info)
        
        log_method(
            f"System Alert: {alert.message}",
            component=alert.component,
            level=alert.level.value,
            count=alert.count,
            **alert.details
        )
    
    monitor.add_alert_handler(log_alert_handler)
    logger.info("Default alert handlers configured")