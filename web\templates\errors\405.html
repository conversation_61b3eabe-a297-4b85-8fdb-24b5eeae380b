{% extends "base.html" %}

{% block title %}Method Not Allowed - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-times-circle me-2"></i>
                        Method Not Allowed (405)
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-hand-paper fa-5x text-warning"></i>
                    </div>
                    
                    <h5 class="card-title">HTTP Method Not Allowed</h5>
                    <p class="card-text text-muted">
                        The HTTP method <code>{{ method }}</code> is not allowed for this endpoint.
                    </p>
                    
                    <div class="mt-4">
                        <h6>What this means:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-info-circle me-2"></i>The endpoint exists but doesn't support this HTTP method</li>
                            <li><i class="fas fa-code me-2"></i>You might need to use GET, POST, PUT, or DELETE instead</li>
                            <li><i class="fas fa-bug me-2"></i>This could be a programming error in the client application</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>
                            Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}