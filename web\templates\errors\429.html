{% extends "base.html" %}

{% block title %}Rate Limit Exceeded - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Rate Limit Exceeded (429)
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-hourglass-half fa-5x text-info"></i>
                    </div>
                    
                    <h5 class="card-title">Too Many Requests</h5>
                    <p class="card-text text-muted">
                        You've made too many requests in a short period. Please wait before trying again.
                    </p>
                    
                    {% if retry_after %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Please wait <strong>{{ retry_after }} seconds</strong> before making another request.
                    </div>
                    {% endif %}
                    
                    <div class="mt-4">
                        <h6>Why this happens:</h6>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-shield-alt me-2"></i>Rate limiting protects the system from overload</li>
                            <li><i class="fas fa-balance-scale me-2"></i>It ensures fair usage for all users</li>
                            <li><i class="fas fa-instagram me-2"></i>It helps comply with Instagram's usage policies</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        {% if retry_after %}
                        <button id="retryButton" class="btn btn-primary" disabled>
                            <i class="fas fa-refresh me-2"></i>
                            <span id="retryText">Retry in {{ retry_after }}s</span>
                        </button>
                        {% else %}
                        <button onclick="location.reload()" class="btn btn-primary">
                            <i class="fas fa-refresh me-2"></i>
                            Try Again
                        </button>
                        {% endif %}
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if retry_after %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let retryAfter = {{ retry_after }};
    const retryButton = document.getElementById('retryButton');
    const retryText = document.getElementById('retryText');
    
    const countdown = setInterval(function() {
        retryAfter--;
        retryText.textContent = `Retry in ${retryAfter}s`;
        
        if (retryAfter <= 0) {
            clearInterval(countdown);
            retryButton.disabled = false;
            retryText.textContent = 'Try Again';
            retryButton.onclick = function() { location.reload(); };
        }
    }, 1000);
});
</script>
{% endif %}
{% endblock %}