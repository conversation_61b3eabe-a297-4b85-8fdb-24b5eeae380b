"""
REST API endpoints for Instagram Follower Monitor.

This module provides comprehensive REST API endpoints for frontend integration,
including profile management, change data retrieval, monitoring status, and
real-time updates with proper authentication and rate limiting.
"""

from flask import Blueprint, jsonify, request, current_app
from functools import wraps
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
import logging
import time
import hashlib
import hmac

from database.repositories import ProfileRepository, ChangeRepository, SettingsRepository, FollowerRepository
from models.data_models import FollowerChange, MonitoringConfig, validate_username, ChangeType
from web.security import validate_api_input, InputValidator, sanitize_output, SQLInjectionPrevention

logger = logging.getLogger(__name__)

# Create API blueprint
api_v1 = Blueprint('api_v1', __name__)

# Initialize repositories
profile_repo = ProfileRepository()
change_repo = ChangeRepository()
settings_repo = SettingsRepository()
follower_repo = FollowerRepository()

# Rate limiting storage (in production, use Redis)
rate_limit_storage = {}

def rate_limit(max_requests: int = 100, window_seconds: int = 3600):
    """Rate limiting decorator for API endpoints."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get client identifier (IP address)
            client_id = request.environ.get('REMOTE_ADDR', 'unknown')
            current_time = time.time()
            
            # Clean old entries
            cutoff_time = current_time - window_seconds
            if client_id in rate_limit_storage:
                rate_limit_storage[client_id] = [
                    timestamp for timestamp in rate_limit_storage[client_id] 
                    if timestamp > cutoff_time
                ]
            
            # Check rate limit
            if client_id not in rate_limit_storage:
                rate_limit_storage[client_id] = []
            
            if len(rate_limit_storage[client_id]) >= max_requests:
                return jsonify({
                    'error': 'Rate limit exceeded',
                    'message': f'Maximum {max_requests} requests per {window_seconds} seconds'
                }), 429
            
            # Add current request
            rate_limit_storage[client_id].append(current_time)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def api_key_required(f):
    """API key authentication decorator."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from web.api_auth import api_key_manager
        
        api_key = request.headers.get('X-API-Key')
        if not api_key:
            return jsonify({'error': 'API key required'}), 401
        
        if not api_key_manager.validate_api_key(api_key):
            return jsonify({'error': 'Invalid API key'}), 401
        
        return f(*args, **kwargs)
    return decorated_function

def handle_api_error(f):
    """Error handling decorator for API endpoints."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"API validation error in {f.__name__}: {e}")
            return jsonify({'error': 'Invalid input', 'message': str(e)}), 400
        except Exception as e:
            logger.error(f"API error in {f.__name__}: {e}")
            return jsonify({'error': 'Internal server error'}), 500
    return decorated_function

# Profile Management Endpoints

@api_v1.route('/profiles', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_profiles():
    """Get all profiles with optional filtering and pagination."""
    # Parse query parameters
    enabled_only = request.args.get('enabled_only', 'false').lower() == 'true'
    include_stats = request.args.get('include_stats', 'false').lower() == 'true'
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 20)), 100)  # Max 100 per page
    
    # Get profiles
    if enabled_only:
        profiles = profile_repo.get_enabled_profiles()
    else:
        profiles = profile_repo.get_all_profiles()
    
    # Apply pagination
    total_count = len(profiles)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_profiles = profiles[start_idx:end_idx]
    
    # Build response
    profiles_data = []
    for profile in paginated_profiles:
        profile_data = {
            'id': profile.profile_id,
            'username': profile.profile_username,
            'display_name': profile.display_name,
            'is_private': profile.is_private,
            'monitoring_enabled': profile.enabled,
            'last_scan': profile.last_scan.isoformat() if profile.last_scan else None,
            'created_at': profile.created_at.isoformat() if profile.created_at else None,
            'is_due_for_scan': profile.is_due_for_scan if profile.enabled else False
        }
        
        if include_stats and profile.profile_id:
            stats = profile_repo.get_profile_stats(profile.profile_id)
            profile_data['stats'] = stats
        
        profiles_data.append(profile_data)
    
    return jsonify({
        'profiles': profiles_data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': (total_count + per_page - 1) // per_page,
            'has_next': end_idx < total_count,
            'has_prev': page > 1
        }
    })

@api_v1.route('/profiles/<username>', methods=['GET'])
@rate_limit(max_requests=100, window_seconds=3600)
@handle_api_error
def get_profile(username: str):
    """Get detailed information for a specific profile."""
    profile = profile_repo.get_profile_by_username(username)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    # Get profile statistics
    stats = profile_repo.get_profile_stats(profile.profile_id) if profile.profile_id else {}
    
    # Get recent changes
    recent_changes = change_repo.get_recent_changes(profile_id=profile.profile_id, limit=20)
    changes_data = [format_change_for_api(change) for change in recent_changes]
    
    return jsonify({
        'id': profile.profile_id,
        'username': profile.profile_username,
        'display_name': profile.display_name,
        'is_private': profile.is_private,
        'monitoring_enabled': profile.enabled,
        'last_scan': profile.last_scan.isoformat() if profile.last_scan else None,
        'created_at': profile.created_at.isoformat() if profile.created_at else None,
        'is_due_for_scan': profile.is_due_for_scan if profile.enabled else False,
        'stats': stats,
        'recent_changes': changes_data
    })

@api_v1.route('/profiles', methods=['POST'])
@api_key_required
@rate_limit(max_requests=10, window_seconds=3600)
@validate_api_input({
    'username': {'type': 'username', 'required': True},
    'display_name': {'type': 'display_name', 'required': False, 'max_length': 100},
    'is_private': {'type': 'boolean', 'required': False},
    'enabled': {'type': 'boolean', 'required': False},
    'interval_hours': {'type': 'integer', 'required': False, 'min': 1, 'max': 168}
})
@handle_api_error
def create_profile():
    """Create a new profile for monitoring."""
    # Use validated data from security decorator
    validated = request.validated_data
    clean_username = validated['username']
    
    # Check if profile already exists
    existing_profile = profile_repo.get_profile_by_username(clean_username)
    if existing_profile:
        return jsonify({'error': f'Profile @{clean_username} already exists'}), 409
    
    # Create monitoring configuration
    config = MonitoringConfig(
        profile_username=clean_username,
        display_name=validated.get('display_name'),
        is_private=validated.get('is_private', False),
        enabled=validated.get('enabled', True),
        interval_hours=validated.get('interval_hours', 2)
    )
    
    # Save to database
    profile_id = profile_repo.create_profile(config)
    config.profile_id = profile_id
    
    return jsonify({
        'id': profile_id,
        'username': config.profile_username,
        'display_name': config.display_name,
        'is_private': config.is_private,
        'monitoring_enabled': config.enabled,
        'created_at': datetime.now().isoformat(),
        'message': f'Profile @{clean_username} created successfully'
    }), 201

@api_v1.route('/profiles/<username>', methods=['PUT'])
@api_key_required
@rate_limit(max_requests=20, window_seconds=3600)
@validate_api_input({
    'display_name': {'type': 'display_name', 'required': False, 'max_length': 100},
    'is_private': {'type': 'boolean', 'required': False},
    'enabled': {'type': 'boolean', 'required': False},
    'interval_hours': {'type': 'integer', 'required': False, 'min': 1, 'max': 168}
})
@handle_api_error
def update_profile(username: str):
    """Update profile configuration."""
    # Validate username parameter
    try:
        username = InputValidator.validate_username(username)
    except ValueError as e:
        return jsonify({'error': f'Invalid username: {e}'}), 400
    
    profile = profile_repo.get_profile_by_username(username)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    # Use validated data from security decorator
    validated = request.validated_data
    
    # Update profile fields
    if 'display_name' in validated:
        profile.display_name = validated['display_name']
    if 'is_private' in validated:
        profile.is_private = validated['is_private']
    if 'enabled' in validated:
        profile.enabled = validated['enabled']
    if 'interval_hours' in validated:
        profile.interval_hours = validated['interval_hours']
    
    # Save changes
    success = profile_repo.update_profile(profile)
    if not success:
        return jsonify({'error': 'Failed to update profile'}), 500
    
    return jsonify({
        'id': profile.profile_id,
        'username': profile.profile_username,
        'display_name': profile.display_name,
        'is_private': profile.is_private,
        'monitoring_enabled': profile.enabled,
        'message': f'Profile @{username} updated successfully'
    })

@api_v1.route('/profiles/<username>', methods=['DELETE'])
@api_key_required
@rate_limit(max_requests=10, window_seconds=3600)
@handle_api_error
def delete_profile(username: str):
    """Delete profile and all associated data."""
    profile = profile_repo.get_profile_by_username(username)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    success = profile_repo.delete_profile(username)
    if not success:
        return jsonify({'error': 'Failed to delete profile'}), 500
    
    return jsonify({'message': f'Profile @{username} deleted successfully'})

# Change Data Endpoints

@api_v1.route('/changes', methods=['GET'])
@rate_limit(max_requests=100, window_seconds=3600)
@handle_api_error
def get_changes():
    """Get follower/following changes with advanced filtering and pagination."""
    # Parse query parameters
    profile_username = request.args.get('profile')
    change_type = request.args.get('type')
    search_term = request.args.get('search', '').strip()
    days = int(request.args.get('days', 7))
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 20)), 100)
    sort_by = request.args.get('sort_by', 'timestamp')
    sort_order = request.args.get('sort_order', 'desc')
    
    # Get profile ID if specified
    profile_id = None
    if profile_username:
        profile = profile_repo.get_profile_by_username(profile_username)
        if not profile:
            return jsonify({'error': 'Profile not found'}), 404
        profile_id = profile.profile_id
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # Calculate offset for pagination
    offset = (page - 1) * per_page
    
    # Search changes
    if search_term or change_type:
        try:
            result = change_repo.search_changes(
                search_term=search_term,
                profile_id=profile_id,
                change_type=change_type,
                start_date=start_date,
                end_date=end_date,
                limit=per_page,
                offset=offset,
                sort_by=sort_by,
                sort_order=sort_order
            )
            if isinstance(result, dict) and 'changes' in result:
                changes = result['changes']
                total_count = result.get('total_count', len(changes))
            else:
                # Fallback if search_changes returns a list
                changes = result if isinstance(result, list) else []
                total_count = len(changes)
        except (AttributeError, TypeError):
            # Fallback to simple query if search_changes is not available
            all_changes = change_repo.get_changes_by_date_range(start_date, end_date, profile_id)
            if search_term:
                # Simple search filter
                search_lower = search_term.lower()
                all_changes = [c for c in all_changes if search_lower in c.affected_username.lower()]
            if change_type:
                all_changes = [c for c in all_changes if c.change_type.value == change_type]
            total_count = len(all_changes)
            changes = all_changes[offset:offset + per_page]
    else:
        # Use simple date range query
        all_changes = change_repo.get_changes_by_date_range(start_date, end_date, profile_id)
        total_count = len(all_changes)
        changes = all_changes[offset:offset + per_page]
    
    # Format changes for API response
    changes_data = [format_change_for_api(change) for change in changes]
    
    return jsonify({
        'changes': changes_data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': (total_count + per_page - 1) // per_page,
            'has_next': total_count > offset + len(changes),
            'has_prev': page > 1
        },
        'filters': {
            'profile': profile_username,
            'type': change_type,
            'search': search_term,
            'days': days,
            'sort_by': sort_by,
            'sort_order': sort_order
        }
    })

@api_v1.route('/changes/statistics', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_change_statistics():
    """Get change statistics for specified period and profile."""
    profile_username = request.args.get('profile')
    days = int(request.args.get('days', 30))
    
    # Get profile ID if specified
    profile_id = None
    if profile_username:
        profile = profile_repo.get_profile_by_username(profile_username)
        if not profile:
            return jsonify({'error': 'Profile not found'}), 404
        profile_id = profile.profile_id
    
    # Get statistics
    stats = change_repo.get_change_statistics(profile_id=profile_id, days=days)
    
    return jsonify({
        'period_days': days,
        'profile': profile_username,
        'statistics': stats,
        'generated_at': datetime.now().isoformat()
    })

# Monitoring Status Endpoints

@api_v1.route('/monitoring/status', methods=['GET'])
@rate_limit(max_requests=100, window_seconds=3600)
@handle_api_error
def get_monitoring_status():
    """Get comprehensive monitoring system status."""
    profiles = profile_repo.get_all_profiles()
    
    # Calculate status metrics
    total_profiles = len(profiles)
    enabled_profiles = len([p for p in profiles if p.enabled])
    profiles_due_for_scan = len([p for p in profiles if p.enabled and p.is_due_for_scan])
    
    # Get recent scan information
    profiles_with_recent_scans = 0
    last_scan_times = {}
    
    for profile in profiles:
        if profile.last_scan:
            hours_since_scan = (datetime.now() - profile.last_scan).total_seconds() / 3600
            if hours_since_scan < 24:  # Within last 24 hours
                profiles_with_recent_scans += 1
            
            last_scan_times[profile.profile_username] = {
                'last_scan': profile.last_scan.isoformat(),
                'hours_ago': round(hours_since_scan, 2),
                'is_due': profile.is_due_for_scan if profile.enabled else False
            }
        else:
            last_scan_times[profile.profile_username] = {
                'last_scan': None,
                'hours_ago': None,
                'is_due': profile.enabled
            }
    
    # Get authentication status
    try:
        from services.authentication import AuthenticationManager
        from config import Config
        config = Config()
        auth_manager = AuthenticationManager(config)
        has_credentials = auth_manager.has_stored_credentials()
    except Exception:
        has_credentials = False
    
    return jsonify({
        'system_status': {
            'total_profiles': total_profiles,
            'enabled_profiles': enabled_profiles,
            'profiles_due_for_scan': profiles_due_for_scan,
            'profiles_with_recent_scans': profiles_with_recent_scans,
            'authentication_configured': has_credentials
        },
        'profile_status': last_scan_times,
        'generated_at': datetime.now().isoformat()
    })

@api_v1.route('/monitoring/jobs', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_monitoring_jobs():
    """Get current monitoring job status."""
    try:
        from services.scheduler import SchedulerManager
        scheduler_manager = SchedulerManager()
        
        # Get job information
        jobs_info = []
        if scheduler_manager.scheduler and scheduler_manager.scheduler.running:
            jobs = scheduler_manager.scheduler.get_jobs()
            for job in jobs:
                jobs_info.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger),
                    'func_name': job.func.__name__ if job.func else None
                })
        
        return jsonify({
            'scheduler_running': scheduler_manager.scheduler.running if scheduler_manager.scheduler else False,
            'jobs': jobs_info,
            'job_count': len(jobs_info)
        })
    
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        return jsonify({
            'scheduler_running': False,
            'jobs': [],
            'job_count': 0,
            'error': 'Failed to get job status'
        })

# Real-time Data Endpoints

@api_v1.route('/dashboard/summary', methods=['GET'])
@rate_limit(max_requests=100, window_seconds=3600)
@handle_api_error
def get_dashboard_summary():
    """Get dashboard summary data for real-time updates."""
    profiles = profile_repo.get_all_profiles()
    
    # Calculate summary statistics
    total_followers = 0
    total_following = 0
    
    for profile in profiles:
        if profile.profile_id:
            stats = profile_repo.get_profile_stats(profile.profile_id)
            total_followers += stats.get('current_followers', 0)
            total_following += stats.get('current_following', 0)
    
    # Get today's changes
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_changes = change_repo.get_changes_by_date_range(today_start, datetime.now())
    
    # Get recent changes
    recent_changes = change_repo.get_recent_changes(limit=10)
    recent_changes_data = [format_change_for_api(change) for change in recent_changes]
    
    return jsonify({
        'summary': {
            'total_profiles': len(profiles),
            'enabled_profiles': len([p for p in profiles if p.enabled]),
            'total_followers': total_followers,
            'total_following': total_following,
            'changes_today': len(today_changes)
        },
        'recent_changes': recent_changes_data,
        'last_updated': datetime.now().isoformat()
    })

@api_v1.route('/profiles/<username>/followers', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_profile_followers(username: str):
    """Get current followers list for a profile."""
    profile = profile_repo.get_profile_by_username(username)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    if not profile.profile_id:
        return jsonify({'error': 'Profile not initialized'}), 400
    
    # Get current followers
    followers = follower_repo.get_current_followers(profile.profile_id)
    
    # Apply pagination
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 200)
    
    followers_list = sorted(list(followers))
    total_count = len(followers_list)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_followers = followers_list[start_idx:end_idx]
    
    return jsonify({
        'profile': username,
        'followers': paginated_followers,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': (total_count + per_page - 1) // per_page,
            'has_next': end_idx < total_count,
            'has_prev': page > 1
        }
    })

@api_v1.route('/profiles/<username>/following', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_profile_following(username: str):
    """Get current following list for a profile."""
    profile = profile_repo.get_profile_by_username(username)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    if not profile.profile_id:
        return jsonify({'error': 'Profile not initialized'}), 400
    
    # Get current following
    following = follower_repo.get_current_following(profile.profile_id)
    
    # Apply pagination
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 200)
    
    following_list = sorted(list(following))
    total_count = len(following_list)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_following = following_list[start_idx:end_idx]
    
    return jsonify({
        'profile': username,
        'following': paginated_following,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': (total_count + per_page - 1) // per_page,
            'has_next': end_idx < total_count,
            'has_prev': page > 1
        }
    })

# Settings and Configuration Endpoints

@api_v1.route('/settings', methods=['GET'])
@api_key_required
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_settings():
    """Get current system settings."""
    monitoring_settings = settings_repo.get_monitoring_settings()
    
    return jsonify({
        'monitoring_settings': monitoring_settings,
        'retrieved_at': datetime.now().isoformat()
    })

@api_v1.route('/settings', methods=['PUT'])
@api_key_required
@rate_limit(max_requests=10, window_seconds=3600)
@handle_api_error
def update_settings():
    """Update system settings."""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'JSON data required'}), 400
    
    # Validate and update settings
    valid_settings = [
        'monitoring_interval_hours', 'data_retention_days', 'min_request_delay',
        'max_request_delay', 'max_retries', 'profile_processing_delay'
    ]
    
    updated_settings = {}
    for key, value in data.items():
        if key in valid_settings:
            updated_settings[key] = value
    
    if not updated_settings:
        return jsonify({'error': 'No valid settings provided'}), 400
    
    success = settings_repo.update_monitoring_settings(updated_settings)
    if not success:
        return jsonify({'error': 'Failed to update settings'}), 500
    
    return jsonify({
        'message': 'Settings updated successfully',
        'updated_settings': updated_settings
    })

# Utility Functions

def format_change_for_api(change: FollowerChange) -> Dict[str, Any]:
    """Format a FollowerChange object for API response."""
    return {
        'id': f"{change.profile_username}_{change.affected_username}_{change.timestamp.isoformat()}",
        'profile_username': change.profile_username,
        'affected_username': change.affected_username,
        'change_type': change.change_type.value,
        'timestamp': change.timestamp.isoformat(),
        'is_follower_change': change.is_follower_change,
        'is_following_change': change.is_following_change,
        'display_text': format_change_display_text(change)
    }

def format_change_display_text(change: FollowerChange) -> str:
    """Format change for display text."""
    if change.change_type == ChangeType.GAINED:
        return f"@{change.affected_username} started following @{change.profile_username}"
    elif change.change_type == ChangeType.LOST:
        return f"@{change.affected_username} unfollowed @{change.profile_username}"
    elif change.change_type == ChangeType.STARTED_FOLLOWING:
        return f"@{change.profile_username} started following @{change.affected_username}"
    elif change.change_type == ChangeType.STOPPED_FOLLOWING:
        return f"@{change.profile_username} stopped following @{change.affected_username}"
    else:
        return f"Unknown change type for @{change.affected_username}"

# Chart Data Endpoints

@api_v1.route('/profile/<username>/trend-data', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_profile_trend_data(username: str):
    """Get follower/following trend data for charts."""
    profile = profile_repo.get_profile_by_username(username)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    if not profile.profile_id:
        return jsonify({'error': 'Profile not initialized'}), 400
    
    # Get days parameter (default 30 days)
    days = int(request.args.get('days', 30))
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # Get historical data points
    trend_data = change_repo.get_trend_data(profile.profile_id, start_date, end_date)
    
    return jsonify({
        'profile': username,
        'dates': trend_data.get('dates', []),
        'followers': trend_data.get('followers', []),
        'following': trend_data.get('following', []),
        'period_days': days
    })

@api_v1.route('/profile/<username>/change-distribution', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_profile_change_distribution(username: str):
    """Get change distribution data for pie/doughnut charts."""
    profile = profile_repo.get_profile_by_username(username)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    if not profile.profile_id:
        return jsonify({'error': 'Profile not initialized'}), 400
    
    # Get days parameter (default 30 days)
    days = int(request.args.get('days', 30))
    
    # Get change statistics
    stats = change_repo.get_change_statistics(profile_id=profile.profile_id, days=days)
    
    return jsonify({
        'profile': username,
        'gained': stats.get('followers_gained', 0),
        'lost': stats.get('followers_lost', 0),
        'started_following': stats.get('following_started', 0),
        'stopped_following': stats.get('following_stopped', 0),
        'period_days': days
    })

@api_v1.route('/dashboard/change-distribution', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_dashboard_change_distribution():
    """Get overall change distribution data for all profiles."""
    # Get days parameter (default 30 days)
    days = int(request.args.get('days', 30))
    
    # Get change statistics for all profiles
    stats = change_repo.get_change_statistics(profile_id=None, days=days)
    
    return jsonify({
        'gained': stats.get('followers_gained', 0),
        'lost': stats.get('followers_lost', 0),
        'started_following': stats.get('following_started', 0),
        'stopped_following': stats.get('following_stopped', 0),
        'period_days': days
    })

@api_v1.route('/dashboard/profile-comparison', methods=['GET'])
@rate_limit(max_requests=50, window_seconds=3600)
@handle_api_error
def get_profile_comparison():
    """Get profile comparison data for bar charts."""
    profiles = profile_repo.get_enabled_profiles()
    
    profile_names = []
    follower_counts = []
    following_counts = []
    
    for profile in profiles:
        if profile.profile_id:
            stats = profile_repo.get_profile_stats(profile.profile_id)
            profile_names.append(profile.profile_username)
            follower_counts.append(stats.get('current_followers', 0))
            following_counts.append(stats.get('current_following', 0))
    
    return jsonify({
        'profiles': profile_names,
        'followers': follower_counts,
        'following': following_counts
    })

@api_v1.route('/dashboard/stats', methods=['GET'])
@rate_limit(max_requests=100, window_seconds=3600)
@handle_api_error
def get_dashboard_stats():
    """Get dashboard statistics for real-time updates."""
    profiles = profile_repo.get_all_profiles()
    
    # Calculate summary statistics
    total_followers = 0
    total_following = 0
    
    for profile in profiles:
        if profile.profile_id:
            stats = profile_repo.get_profile_stats(profile.profile_id)
            total_followers += stats.get('current_followers', 0)
            total_following += stats.get('current_following', 0)
    
    # Get today's changes
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_changes = change_repo.get_changes_by_date_range(today_start, datetime.now())
    
    return jsonify({
        'total_profiles': len(profiles),
        'enabled_profiles': len([p for p in profiles if p.enabled]),
        'total_followers': total_followers,
        'total_following': total_following,
        'changes_today': len(today_changes)
    })

@api_v1.route('/dashboard/recent-changes', methods=['GET'])
@rate_limit(max_requests=100, window_seconds=3600)
@handle_api_error
def get_dashboard_recent_changes():
    """Get recent changes for dashboard updates."""
    limit = int(request.args.get('limit', 10))
    recent_changes = change_repo.get_recent_changes(limit=limit)
    changes_data = [format_change_for_api(change) for change in recent_changes]
    
    return jsonify(changes_data)

# Documentation Endpoints

@api_v1.route('/docs', methods=['GET'])
def get_api_docs():
    """Get comprehensive API documentation."""
    from web.api_docs import get_api_documentation
    return jsonify(get_api_documentation())

@api_v1.route('/docs/openapi', methods=['GET'])
def get_openapi_spec():
    """Get OpenAPI 3.0 specification."""
    from web.api_docs import get_openapi_spec
    return jsonify(get_openapi_spec())

@api_v1.route('/health', methods=['GET'])
def api_health():
    """API health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat()
    })

# Error handlers for API blueprint

@api_v1.errorhandler(404)
def api_not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@api_v1.errorhandler(405)
def api_method_not_allowed(error):
    return jsonify({'error': 'Method not allowed'}), 405

@api_v1.errorhandler(500)
def api_internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500