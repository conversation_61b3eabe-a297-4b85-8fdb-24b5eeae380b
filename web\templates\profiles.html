{% extends "base.html" %}

{% block title %}Profiles - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Profile Management</h1>
    <a href="{{ url_for('main.add_profile') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Add Profile
    </a>
</div>

<!-- Flash messages -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}
{% endwith %}

{% if profile_stats %}
    <div class="row">
        {% for item in profile_stats %}
            {% set profile = item.profile %}
            {% set stats = item.stats %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card profile-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <span class="status-indicator {{ 'status-active' if profile.enabled else 'status-inactive' }}"></span>
                            <strong>@{{ profile.profile_username }}</strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('main.profile_detail', username=profile.profile_username) }}">
                                        <i class="bi bi-eye"></i> View Details
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('main.edit_profile', username=profile.profile_username) }}">
                                        <i class="bi bi-pencil"></i> Edit Settings
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-{{ 'warning' if profile.enabled else 'success' }}" 
                                            onclick="toggleProfile('{{ profile.profile_username }}')">
                                        <i class="bi bi-{{ 'pause' if profile.enabled else 'play' }}"></i> 
                                        {{ 'Disable' if profile.enabled else 'Enable' }} Monitoring
                                    </button>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-danger" 
                                            onclick="confirmDelete('{{ profile.profile_username }}')">
                                        <i class="bi bi-trash"></i> Delete Profile
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        {% if profile.display_name %}
                            <h6 class="card-subtitle mb-2 text-muted">{{ profile.display_name }}</h6>
                        {% endif %}
                        
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="stat-number">{{ stats.get('current_followers', 0) }}</div>
                                <div class="stat-label">Followers</div>
                            </div>
                            <div class="col-6">
                                <div class="stat-number">{{ stats.get('current_following', 0) }}</div>
                                <div class="stat-label">Following</div>
                            </div>
                        </div>
                        
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="text-success">+{{ stats.get('followers_gained_30d', 0) }}</div>
                                <div class="stat-label">Gained (30d)</div>
                            </div>
                            <div class="col-6">
                                <div class="text-danger">-{{ stats.get('followers_lost_30d', 0) }}</div>
                                <div class="stat-label">Lost (30d)</div>
                            </div>
                        </div>
                        
                        <div class="small text-muted">
                            <div class="d-flex justify-content-between">
                                <span>Status:</span>
                                <span class="badge bg-{{ 'success' if profile.enabled else 'secondary' }}">
                                    {{ 'Active' if profile.enabled else 'Inactive' }}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mt-1">
                                <span>Type:</span>
                                <span class="badge bg-{{ 'warning' if profile.is_private else 'info' }}">
                                    {{ 'Private' if profile.is_private else 'Public' }}
                                </span>
                            </div>
                            {% if profile.last_scan %}
                                <div class="d-flex justify-content-between mt-1">
                                    <span>Last Scan:</span>
                                    <span>{{ profile.last_scan | timeago }}</span>
                                </div>
                            {% else %}
                                <div class="d-flex justify-content-between mt-1">
                                    <span>Last Scan:</span>
                                    <span class="text-warning">Never</span>
                                </div>
                            {% endif %}
                            <div class="d-flex justify-content-between mt-1">
                                <span>Interval:</span>
                                <span>{{ profile.interval_hours }}h</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-people display-1 text-muted"></i>
        <h3 class="mt-3">No Profiles Yet</h3>
        <p class="text-muted">Add your first Instagram profile to start monitoring follower changes.</p>
        <a href="{{ url_for('main.add_profile') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add Your First Profile
        </a>
    </div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete profile <strong id="deleteProfileName"></strong>?</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    This will permanently delete all follower data and change history for this profile.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Delete Profile
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function confirmDelete(username) {
    document.getElementById('deleteProfileName').textContent = '@' + username;
    document.getElementById('deleteForm').action = `/profiles/${username}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function toggleProfile(username) {
    fetch(`/profiles/${username}/toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showAlert(data.message, 'success');
            // Reload page to update UI
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.error || 'Failed to toggle profile', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to toggle profile', 'danger');
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// Page-specific refresh function
function pageRefresh() {
    return new Promise((resolve) => {
        location.reload();
        resolve();
    });
}
</script>
{% endblock %}