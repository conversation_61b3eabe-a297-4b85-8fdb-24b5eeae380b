"""
Web routes for backup and recovery management.

This module provides web interface endpoints for backup operations including
creating backups, restoring from backups, and managing backup files.
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, send_file
from datetime import datetime, timedelta
import os
import json
from pathlib import Path

from services.backup_manager import Backup<PERSON>anager
from config import Config
import logging

logger = logging.getLogger(__name__)

# Create blueprint
backup_bp = Blueprint('backup', __name__, url_prefix='/backup')

# Initialize backup manager
config = Config()
backup_manager = BackupManager(config)


@backup_bp.route('/')
def backup_dashboard():
    """Main backup management dashboard."""
    try:
        # Get list of available backups
        backups = backup_manager.list_available_backups()
        
        # Get backup statistics
        backup_stats = {
            'total_backups': len(backups),
            'total_size': sum(backup.get('file_size', 0) for backup in backups),
            'latest_backup': backups[0] if backups else None,
            'oldest_backup': backups[-1] if backups else None
        }
        
        # Get database integrity status
        integrity_result = backup_manager.validate_database_integrity()
        
        return render_template('backup/dashboard.html', 
                             backups=backups,
                             backup_stats=backup_stats,
                             integrity_status=integrity_result)
    
    except Exception as e:
        logger.error(f"Error loading backup dashboard: {e}")
        flash(f"Error loading backup dashboard: {e}", 'error')
        return render_template('backup/dashboard.html', 
                             backups=[],
                             backup_stats={},
                             integrity_status={'valid': False, 'error': str(e)})


@backup_bp.route('/create', methods=['GET', 'POST'])
def create_backup():
    """Create a new backup."""
    if request.method == 'GET':
        return render_template('backup/create.html')
    
    try:
        backup_type = request.form.get('backup_type', 'full')
        backup_name = request.form.get('backup_name', '').strip()
        
        if backup_type == 'incremental':
            since_date_str = request.form.get('since_date')
            if not since_date_str:
                flash('Since date is required for incremental backups', 'error')
                return render_template('backup/create.html')
            
            since_date = datetime.fromisoformat(since_date_str)
            result = backup_manager.create_incremental_backup(since_date, backup_name)
        else:
            result = backup_manager.create_full_backup(backup_name)
        
        if result['success']:
            flash(f'Backup created successfully: {result["backup_path"]}', 'success')
            return redirect(url_for('backup.backup_dashboard'))
        else:
            flash(f'Backup creation failed: {result["error"]}', 'error')
            return render_template('backup/create.html')
    
    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        flash(f'Error creating backup: {e}', 'error')
        return render_template('backup/create.html')


@backup_bp.route('/restore', methods=['GET', 'POST'])
def restore_backup():
    """Restore from a backup."""
    if request.method == 'GET':
        backups = backup_manager.list_available_backups()
        return render_template('backup/restore.html', backups=backups)
    
    try:
        backup_path = request.form.get('backup_path')
        restore_database = request.form.get('restore_database') == 'on'
        restore_configuration = request.form.get('restore_configuration') == 'on'
        skip_validation = request.form.get('skip_validation') == 'on'
        
        if not backup_path:
            flash('Please select a backup to restore', 'error')
            return redirect(url_for('backup.restore_backup'))
        
        restore_options = {
            'database': restore_database,
            'configuration': restore_configuration,
            'validate_integrity': not skip_validation
        }
        
        # If no specific options selected, restore everything
        if not any([restore_database, restore_configuration]):
            restore_options = {
                'database': True,
                'configuration': True,
                'validate_integrity': not skip_validation
            }
        
        result = backup_manager.restore_from_backup(backup_path, restore_options)
        
        if result['success']:
            flash(f'Restore completed successfully! Restored: {", ".join(result["restored_components"])}', 'success')
            if result.get('errors'):
                for error in result['errors']:
                    flash(f'Warning: {error}', 'warning')
        else:
            flash(f'Restore failed: {result["error"]}', 'error')
        
        return redirect(url_for('backup.backup_dashboard'))
    
    except Exception as e:
        logger.error(f"Error restoring backup: {e}")
        flash(f'Error restoring backup: {e}', 'error')
        return redirect(url_for('backup.restore_backup'))


@backup_bp.route('/validate')
def validate_database():
    """Validate database integrity."""
    try:
        result = backup_manager.validate_database_integrity()
        return render_template('backup/validation.html', validation_result=result)
    
    except Exception as e:
        logger.error(f"Error validating database: {e}")
        flash(f'Error validating database: {e}', 'error')
        return redirect(url_for('backup.backup_dashboard'))


@backup_bp.route('/export/<username>')
def export_profile(username):
    """Export profile data."""
    try:
        result = backup_manager.export_profile_data(username)
        
        if result['success']:
            # Send the exported file for download
            return send_file(
                result['output_path'],
                as_attachment=True,
                download_name=f"profile_export_{username}_{datetime.now().strftime('%Y%m%d')}.json"
            )
        else:
            flash(f'Export failed: {result["error"]}', 'error')
            return redirect(url_for('backup.backup_dashboard'))
    
    except Exception as e:
        logger.error(f"Error exporting profile: {e}")
        flash(f'Error exporting profile: {e}', 'error')
        return redirect(url_for('backup.backup_dashboard'))


@backup_bp.route('/import', methods=['GET', 'POST'])
def import_profile():
    """Import profile data."""
    if request.method == 'GET':
        return render_template('backup/import.html')
    
    try:
        if 'import_file' not in request.files:
            flash('No file selected', 'error')
            return render_template('backup/import.html')
        
        file = request.files['import_file']
        if file.filename == '':
            flash('No file selected', 'error')
            return render_template('backup/import.html')
        
        overwrite_existing = request.form.get('overwrite_existing') == 'on'
        
        # Save uploaded file temporarily
        temp_path = Path('/tmp') / f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        file.save(temp_path)
        
        try:
            result = backup_manager.import_profile_data(str(temp_path), overwrite_existing)
            
            if result['success']:
                flash(f'Profile imported successfully: {result["profile_username"]}', 'success')
                flash(f'Records imported: {result["records_imported"]}', 'info')
            else:
                flash(f'Import failed: {result["error"]}', 'error')
        
        finally:
            # Clean up temporary file
            if temp_path.exists():
                temp_path.unlink()
        
        return redirect(url_for('backup.backup_dashboard'))
    
    except Exception as e:
        logger.error(f"Error importing profile: {e}")
        flash(f'Error importing profile: {e}', 'error')
        return render_template('backup/import.html')


@backup_bp.route('/cleanup', methods=['POST'])
def cleanup_backups():
    """Clean up old backups."""
    try:
        retention_days = int(request.form.get('retention_days', 30))
        
        result = backup_manager.cleanup_old_backups(retention_days)
        
        if result['success']:
            flash(f'Cleanup completed! Deleted {len(result["deleted_files"])} files, freed {result["total_size_freed"]:,} bytes', 'success')
        else:
            flash(f'Cleanup failed: {result["error"]}', 'error')
        
        return redirect(url_for('backup.backup_dashboard'))
    
    except Exception as e:
        logger.error(f"Error cleaning up backups: {e}")
        flash(f'Error cleaning up backups: {e}', 'error')
        return redirect(url_for('backup.backup_dashboard'))


@backup_bp.route('/schedule', methods=['GET', 'POST'])
def schedule_backup():
    """Schedule automated backups."""
    if request.method == 'GET':
        return render_template('backup/schedule.html')
    
    try:
        interval_hours = int(request.form.get('interval_hours', 24))
        
        result = backup_manager.schedule_automated_backup(interval_hours)
        
        if result['success']:
            flash(f'Automated backup scheduled successfully! Interval: {interval_hours} hours', 'success')
        else:
            flash(f'Scheduling failed: {result["error"]}', 'error')
        
        return redirect(url_for('backup.backup_dashboard'))
    
    except Exception as e:
        logger.error(f"Error scheduling backup: {e}")
        flash(f'Error scheduling backup: {e}', 'error')
        return render_template('backup/schedule.html')


@backup_bp.route('/download/<path:backup_name>')
def download_backup(backup_name):
    """Download a backup file."""
    try:
        backup_path = backup_manager.backup_dir / f"{backup_name}.backup"
        
        if not backup_path.exists():
            flash('Backup file not found', 'error')
            return redirect(url_for('backup.backup_dashboard'))
        
        return send_file(
            backup_path,
            as_attachment=True,
            download_name=f"{backup_name}.backup"
        )
    
    except Exception as e:
        logger.error(f"Error downloading backup: {e}")
        flash(f'Error downloading backup: {e}', 'error')
        return redirect(url_for('backup.backup_dashboard'))


@backup_bp.route('/api/status')
def api_backup_status():
    """API endpoint for backup status information."""
    try:
        backups = backup_manager.list_available_backups()
        integrity_result = backup_manager.validate_database_integrity()
        
        return jsonify({
            'success': True,
            'backup_count': len(backups),
            'latest_backup': backups[0] if backups else None,
            'database_integrity': integrity_result['valid'],
            'integrity_errors': integrity_result.get('errors', [])
        })
    
    except Exception as e:
        logger.error(f"Error getting backup status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@backup_bp.route('/api/create', methods=['POST'])
def api_create_backup():
    """API endpoint for creating backups."""
    try:
        data = request.get_json()
        backup_type = data.get('type', 'full')
        backup_name = data.get('name')
        
        if backup_type == 'incremental':
            since_date_str = data.get('since_date')
            if not since_date_str:
                return jsonify({
                    'success': False,
                    'error': 'since_date is required for incremental backups'
                }), 400
            
            since_date = datetime.fromisoformat(since_date_str)
            result = backup_manager.create_incremental_backup(since_date, backup_name)
        else:
            result = backup_manager.create_full_backup(backup_name)
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"Error creating backup via API: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@backup_bp.route('/api/validate')
def api_validate_database():
    """API endpoint for database validation."""
    try:
        result = backup_manager.validate_database_integrity()
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"Error validating database via API: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500