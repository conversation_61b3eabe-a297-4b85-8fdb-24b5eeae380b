"""Database connection management and utilities."""

import sqlite3
import os
from contextlib import contextmanager
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages SQLite database connections and schema initialization."""
    
    def __init__(self, db_path: str = "instagram_monitor.db"):
        """Initialize database manager with database path."""
        self.db_path = db_path
        self._ensure_database_exists()
    
    def _ensure_database_exists(self) -> None:
        """Ensure database file exists and create if necessary."""
        if not os.path.exists(self.db_path):
            logger.info(f"Creating new database at {self.db_path}")
            # Create empty database file
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("SELECT 1")  # Simple query to create file
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable dict-like access to rows
            conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign key constraints
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def initialize_schema(self) -> None:
        """Initialize database schema from schema.sql file."""
        schema_path = os.path.join(os.path.dirname(__file__), "schema.sql")
        
        if not os.path.exists(schema_path):
            raise FileNotFoundError(f"Schema file not found: {schema_path}")
        
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
        
        with self.get_connection() as conn:
            # Execute schema creation
            conn.executescript(schema_sql)
            conn.commit()
            logger.info("Database schema initialized successfully")
    
    def get_schema_version(self) -> Optional[int]:
        """Get current schema version from database."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT value FROM settings WHERE key = 'schema_version'"
                )
                result = cursor.fetchone()
                return int(result['value']) if result else None
        except sqlite3.OperationalError:
            # Settings table doesn't exist yet
            return None
    
    def set_schema_version(self, version: int) -> None:
        """Set schema version in database."""
        with self.get_connection() as conn:
            conn.execute(
                "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
                ("schema_version", str(version))
            )
            conn.commit()
    
    def migrate_schema(self) -> None:
        """Run database migrations if needed."""
        current_version = self.get_schema_version()
        target_version = 1  # Current schema version
        
        if current_version is None:
            # Fresh installation
            self.initialize_schema()
            self.set_schema_version(target_version)
            logger.info(f"Database initialized with schema version {target_version}")
        elif current_version < target_version:
            # Future migrations would go here
            logger.info(f"Schema migration from version {current_version} to {target_version}")
            # For now, just update version
            self.set_schema_version(target_version)
        else:
            logger.info(f"Database schema is up to date (version {current_version})")
    
    def execute_query(self, query: str, params: tuple = ()) -> list:
        """Execute a SELECT query and return results."""
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return affected rows."""
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_many(self, query: str, params_list: list) -> int:
        """Execute query with multiple parameter sets."""
        with self.get_connection() as conn:
            cursor = conn.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount


# Global database manager instance
db_manager = DatabaseManager()