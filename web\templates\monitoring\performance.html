{% extends "base.html" %}

{% block title %}Performance Monitoring - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Performance Monitoring
                </h2>
                <div>
                    <button onclick="location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh
                    </button>
                    <a href="{{ url_for('monitoring.system_status') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-heartbeat me-2"></i>
                        System Status
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Summary -->
    <div class="row mb-4">
        {% for component, metrics in metrics_by_component.items() %}
        {% if metrics %}
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">{{ component.replace('_', ' ').title() }}</h6>
                </div>
                <div class="card-body">
                    {% set request_metrics = metrics|selectattr('name', 'equalto', 'request_duration')|list %}
                    {% if request_metrics %}
                        {% set avg_duration = (request_metrics|sum(attribute='value') / request_metrics|length) %}
                        <div class="text-center">
                            <h4 class="
                                {% if avg_duration < 1 %}text-success
                                {% elif avg_duration < 3 %}text-warning
                                {% else %}text-danger{% endif %}
                            ">
                                {{ "%.2f"|format(avg_duration) }}s
                            </h4>
                            <small class="text-muted">Avg Response Time</small>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                {{ request_metrics|length }} requests (24h)
                            </small>
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <p>No metrics available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        {% endfor %}
    </div>

    <!-- Detailed Performance Metrics -->
    {% for component, metrics in metrics_by_component.items() %}
    {% if metrics %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        {{ component.replace('_', ' ').title() }} Performance
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Request Duration Metrics -->
                    {% set request_metrics = metrics|selectattr('name', 'equalto', 'request_duration')|list %}
                    {% if request_metrics %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6>Request Duration Statistics</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Metric</th>
                                            <th>Value</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% set durations = request_metrics|map(attribute='value')|list %}
                                        {% set avg_duration = durations|sum / durations|length %}
                                        {% set min_duration = durations|min %}
                                        {% set max_duration = durations|max %}
                                        
                                        <tr>
                                            <td>Average Duration</td>
                                            <td>{{ "%.3f"|format(avg_duration) }}s</td>
                                            <td>
                                                {% if avg_duration < 1 %}
                                                    <span class="badge bg-success">Good</span>
                                                {% elif avg_duration < 3 %}
                                                    <span class="badge bg-warning">Acceptable</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Slow</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Minimum Duration</td>
                                            <td>{{ "%.3f"|format(min_duration) }}s</td>
                                            <td>
                                                <span class="badge bg-info">Best</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Maximum Duration</td>
                                            <td>{{ "%.3f"|format(max_duration) }}s</td>
                                            <td>
                                                {% if max_duration < 5 %}
                                                    <span class="badge bg-success">Good</span>
                                                {% elif max_duration < 10 %}
                                                    <span class="badge bg-warning">Concerning</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Critical</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Total Requests</td>
                                            <td>{{ durations|length }}</td>
                                            <td>
                                                <span class="badge bg-secondary">Count</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Recent Performance Data -->
                    <div class="row">
                        <div class="col-12">
                            <h6>Recent Performance Data</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Metric</th>
                                            <th>Value</th>
                                            <th>Unit</th>
                                            <th>Operation</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for metric in metrics|sort(attribute='timestamp', reverse=true)[:20] %}
                                        <tr>
                                            <td>
                                                <small>{{ metric.timestamp }}</small>
                                            </td>
                                            <td>{{ metric.name }}</td>
                                            <td>
                                                <span class="
                                                    {% if metric.name == 'request_duration' %}
                                                        {% if metric.value < 1 %}text-success
                                                        {% elif metric.value < 3 %}text-warning
                                                        {% else %}text-danger{% endif %}
                                                    {% endif %}
                                                ">
                                                    {{ "%.3f"|format(metric.value) }}
                                                </span>
                                            </td>
                                            <td>{{ metric.unit }}</td>
                                            <td>
                                                {% if metric.tags.operation %}
                                                    <code>{{ metric.tags.operation }}</code>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endfor %}

    <!-- No Data Message -->
    {% if not metrics_by_component or not (metrics_by_component.values()|selectattr('length')|list) %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                    <h4>No Performance Data Available</h4>
                    <p class="text-muted">
                        Performance metrics will appear here once the system starts processing requests.
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Auto-refresh every 60 seconds
setTimeout(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}