#!/usr/bin/env python3
"""
Test script for Instagram Follower Monitor REST API endpoints.

This script tests the API endpoints to ensure they work correctly with proper
authentication, rate limiting, and response formatting.
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class APITester:
    """Test class for API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:5000/api/v1", api_key: Optional[str] = None):
        self.base_url = base_url
        self.api_key = api_key
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({'X-API-Key': api_key})
    
    def test_health_check(self) -> bool:
        """Test API health check endpoint."""
        print("Testing API health check...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Health check passed: {data['status']}")
                return True
            else:
                print(f"✗ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Health check error: {e}")
            return False
    
    def test_get_profiles(self) -> bool:
        """Test getting profiles list."""
        print("Testing GET /profiles...")
        try:
            response = self.session.get(f"{self.base_url}/profiles")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Got {len(data['profiles'])} profiles")
                print(f"  Pagination: page {data['pagination']['page']} of {data['pagination']['total_pages']}")
                return True
            else:
                print(f"✗ Get profiles failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Get profiles error: {e}")
            return False
    
    def test_get_changes(self) -> bool:
        """Test getting changes list."""
        print("Testing GET /changes...")
        try:
            response = self.session.get(f"{self.base_url}/changes?days=30&per_page=10")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Got {len(data['changes'])} changes")
                print(f"  Filters: {data['filters']}")
                return True
            else:
                print(f"✗ Get changes failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Get changes error: {e}")
            return False
    
    def test_monitoring_status(self) -> bool:
        """Test monitoring status endpoint."""
        print("Testing GET /monitoring/status...")
        try:
            response = self.session.get(f"{self.base_url}/monitoring/status")
            if response.status_code == 200:
                data = response.json()
                status = data['system_status']
                print(f"✓ System status: {status['total_profiles']} profiles, {status['enabled_profiles']} enabled")
                return True
            else:
                print(f"✗ Monitoring status failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Monitoring status error: {e}")
            return False
    
    def test_dashboard_summary(self) -> bool:
        """Test dashboard summary endpoint."""
        print("Testing GET /dashboard/summary...")
        try:
            response = self.session.get(f"{self.base_url}/dashboard/summary")
            if response.status_code == 200:
                data = response.json()
                summary = data['summary']
                print(f"✓ Dashboard summary: {summary['total_followers']} followers, {summary['changes_today']} changes today")
                return True
            else:
                print(f"✗ Dashboard summary failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Dashboard summary error: {e}")
            return False
    
    def test_create_profile(self, username: str = "test_user") -> bool:
        """Test creating a profile (requires API key)."""
        if not self.api_key:
            print("⚠ Skipping create profile test (no API key)")
            return True
        
        print(f"Testing POST /profiles (create {username})...")
        try:
            profile_data = {
                "username": username,
                "display_name": "Test User",
                "is_private": False,
                "enabled": True,
                "interval_hours": 2
            }
            
            response = self.session.post(
                f"{self.base_url}/profiles",
                json=profile_data
            )
            
            if response.status_code == 201:
                data = response.json()
                print(f"✓ Profile created: {data['username']}")
                return True
            elif response.status_code == 409:
                print(f"⚠ Profile already exists: {username}")
                return True
            else:
                print(f"✗ Create profile failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"✗ Create profile error: {e}")
            return False
    
    def test_update_profile(self, username: str = "test_user") -> bool:
        """Test updating a profile (requires API key)."""
        if not self.api_key:
            print("⚠ Skipping update profile test (no API key)")
            return True
        
        print(f"Testing PUT /profiles/{username}...")
        try:
            update_data = {
                "display_name": "Updated Test User",
                "enabled": False
            }
            
            response = self.session.put(
                f"{self.base_url}/profiles/{username}",
                json=update_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Profile updated: {data['username']}")
                return True
            elif response.status_code == 404:
                print(f"⚠ Profile not found: {username}")
                return True
            else:
                print(f"✗ Update profile failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"✗ Update profile error: {e}")
            return False
    
    def test_rate_limiting(self) -> bool:
        """Test rate limiting functionality."""
        print("Testing rate limiting...")
        try:
            # Make multiple rapid requests
            for i in range(5):
                response = self.session.get(f"{self.base_url}/health")
                if response.status_code == 429:
                    print("✓ Rate limiting is working")
                    return True
                time.sleep(0.1)
            
            print("⚠ Rate limiting not triggered (may need more requests)")
            return True
        except Exception as e:
            print(f"✗ Rate limiting test error: {e}")
            return False
    
    def test_authentication(self) -> bool:
        """Test API key authentication."""
        print("Testing authentication...")
        try:
            # Test without API key
            no_auth_session = requests.Session()
            response = no_auth_session.post(f"{self.base_url}/profiles", json={"username": "test"})
            
            if response.status_code == 401:
                print("✓ Authentication required for protected endpoints")
                return True
            else:
                print(f"⚠ Expected 401, got {response.status_code}")
                return True
        except Exception as e:
            print(f"✗ Authentication test error: {e}")
            return False
    
    def test_api_documentation(self) -> bool:
        """Test API documentation endpoints."""
        print("Testing API documentation...")
        try:
            # Test docs endpoint
            response = self.session.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ API documentation available: {data['documentation']['info']['title']}")
                
                # Test OpenAPI spec
                response = self.session.get(f"{self.base_url}/docs/openapi")
                if response.status_code == 200:
                    spec = response.json()
                    print(f"✓ OpenAPI spec available: version {spec['openapi']}")
                    return True
                else:
                    print(f"✗ OpenAPI spec failed: {response.status_code}")
                    return False
            else:
                print(f"✗ API docs failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ API documentation error: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all API tests."""
        print("=" * 50)
        print("Instagram Follower Monitor API Tests")
        print("=" * 50)
        
        tests = {
            "health_check": self.test_health_check,
            "get_profiles": self.test_get_profiles,
            "get_changes": self.test_get_changes,
            "monitoring_status": self.test_monitoring_status,
            "dashboard_summary": self.test_dashboard_summary,
            "create_profile": self.test_create_profile,
            "update_profile": self.test_update_profile,
            "authentication": self.test_authentication,
            "rate_limiting": self.test_rate_limiting,
            "api_documentation": self.test_api_documentation
        }
        
        results = {}
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests.items():
            print(f"\n--- {test_name.replace('_', ' ').title()} ---")
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
            except Exception as e:
                print(f"✗ Test {test_name} crashed: {e}")
                results[test_name] = False
        
        print("\n" + "=" * 50)
        print(f"Test Results: {passed}/{total} passed")
        print("=" * 50)
        
        for test_name, result in results.items():
            status = "✓ PASS" if result else "✗ FAIL"
            print(f"{status} {test_name.replace('_', ' ').title()}")
        
        return results

def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Instagram Follower Monitor API")
    parser.add_argument("--url", default="http://localhost:5000/api/v1", help="API base URL")
    parser.add_argument("--api-key", help="API key for authenticated requests")
    parser.add_argument("--test", help="Run specific test")
    
    args = parser.parse_args()
    
    tester = APITester(base_url=args.url, api_key=args.api_key)
    
    if args.test:
        # Run specific test
        test_method = getattr(tester, f"test_{args.test}", None)
        if test_method:
            test_method()
        else:
            print(f"Test '{args.test}' not found")
    else:
        # Run all tests
        tester.run_all_tests()

if __name__ == "__main__":
    main()