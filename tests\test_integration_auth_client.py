"""
Integration tests for authentication and Instagram client components.
"""

import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch

from config import Config
from services import AuthenticationManager, InstagramClient


class TestAuthenticationClientIntegration:
    """Integration tests for authentication and client components."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for test files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        config.MIN_REQUEST_DELAY = 1.0
        config.MAX_REQUEST_DELAY = 3.0
        config.MAX_RETRIES = 3
        from cryptography.fernet import Fernet
        config.ENCRYPTION_KEY = Fernet.generate_key().decode()
        return config
    
    @pytest.fixture
    def instagram_client(self, mock_config, temp_dir, monkeypatch):
        """Create an InstagramClient instance for testing."""
        monkeypatch.chdir(temp_dir)
        return InstagramClient(mock_config)
    
    def test_client_uses_auth_manager(self, instagram_client):
        """Test that Instagram client properly uses authentication manager."""
        # Verify that the client has an auth manager
        assert hasattr(instagram_client, 'auth_manager')
        assert isinstance(instagram_client.auth_manager, AuthenticationManager)
    
    def test_credential_storage_and_retrieval_flow(self, instagram_client):
        """Test the complete credential storage and retrieval flow."""
        username = "testuser"
        password = "testpassword123"
        
        # Store credentials through auth manager
        result = instagram_client.auth_manager.store_credentials(username, password)
        assert result is True
        
        # Verify credentials can be retrieved
        retrieved = instagram_client.auth_manager.retrieve_credentials()
        assert retrieved is not None
        assert retrieved['username'] == username
        assert retrieved['password'] == password
        
        # Verify client can check for stored credentials
        assert instagram_client.auth_manager.has_stored_credentials() is True
    
    @patch('services.instagram_client.instaloader.Instaloader')
    def test_authentication_flow_with_stored_credentials(self, mock_instaloader_class, instagram_client):
        """Test authentication flow using stored credentials."""
        # Mock the loader instance
        mock_loader = Mock()
        mock_instaloader_class.return_value = mock_loader
        instagram_client.loader = mock_loader
        mock_loader.login.return_value = None
        
        # Mock the context for authentication check
        mock_loader.context = Mock()
        mock_loader.context.is_logged_in = True
        
        # Store credentials first
        instagram_client.auth_manager.store_credentials("testuser", "testpass")
        
        # Authenticate using stored credentials
        result = instagram_client.authenticate()
        
        assert result is True
        assert instagram_client.is_authenticated() is True
        assert instagram_client.get_current_username() == "testuser"
        mock_loader.login.assert_called_once_with("testuser", "testpass")
    
    def test_credential_validation_integration(self, instagram_client):
        """Test credential validation integration between components."""
        # Test valid credentials
        assert instagram_client.auth_manager.validate_credentials("validuser", "validpass123") is True
        
        # Test invalid credentials
        assert instagram_client.auth_manager.validate_credentials("", "password") is False
        assert instagram_client.auth_manager.validate_credentials("user", "short") is False
        assert instagram_client.auth_manager.validate_credentials("user@invalid", "password123") is False
    
    def test_session_management_integration(self, instagram_client):
        """Test session management between auth manager and client."""
        session_data = {
            "session_id": "test123",
            "user_agent": "test_agent",
            "expires_at": "2024-01-01T00:00:00Z"
        }
        
        # Store session data
        result = instagram_client.auth_manager.store_session_data(session_data)
        assert result is True
        
        # Retrieve session data
        retrieved = instagram_client.auth_manager.retrieve_session_data()
        assert retrieved is not None
        assert retrieved['session_id'] == session_data['session_id']
        
        # Get session info from client
        client_session_info = instagram_client.get_session_info()
        assert 'authenticated' in client_session_info
        assert 'username' in client_session_info
        assert 'request_count' in client_session_info
    
    def test_cleanup_integration(self, instagram_client):
        """Test cleanup operations across components."""
        # Store some data
        instagram_client.auth_manager.store_credentials("testuser", "testpass")
        instagram_client.auth_manager.store_session_data({"session_id": "test"})
        
        # Verify data exists
        assert instagram_client.auth_manager.has_stored_credentials() is True
        assert instagram_client.auth_manager.retrieve_session_data() is not None
        
        # Clear all data
        result = instagram_client.auth_manager.clear_credentials()
        assert result is True
        
        # Verify data is cleared
        assert instagram_client.auth_manager.has_stored_credentials() is False
        assert instagram_client.auth_manager.retrieve_session_data() is None
        
        # Logout from client
        instagram_client.logout()
        assert instagram_client.is_authenticated() is False
        assert instagram_client.get_current_username() is None