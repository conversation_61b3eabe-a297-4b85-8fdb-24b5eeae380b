"""
Core monitoring service for Instagram follower tracking.

This module orchestrates the monitoring process by coordinating profile scanning,
change detection, and data processing components.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import time

from models.data_models import MonitoringConfig, ProfileInfo, FollowerChange
from database.repositories import ProfileRepository, FollowerRepository, ChangeRepository
from services.instagram_client import InstagramClient
from services.profile_scanner import ProfileScanner
from services.change_detector import ChangeDetector
from services.data_processor import DataProcessor
from config import Config

logger = logging.getLogger(__name__)


class MonitoringService:
    """Main service for orchestrating Instagram follower monitoring."""
    
    def __init__(self, config: Config):
        """Initialize monitoring service with dependencies.
        
        Args:
            config: Application configuration instance
        """
        self.config = config
        self.instagram_client = InstagramClient(config)
        self.profile_repo = ProfileRepository()
        self.follower_repo = FollowerRepository()
        self.change_repo = ChangeRepository()
        
        # Initialize service components
        self.profile_scanner = ProfileScanner(self.instagram_client, config)
        self.change_detector = ChangeDetector()
        self.data_processor = DataProcessor(
            self.follower_repo, 
            self.change_repo, 
            self.profile_repo
        )
        
        self._monitoring_active = False
        self._last_monitoring_run = None
        self._monitoring_stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'profiles_processed': 0,
            'changes_detected': 0,
            'last_error': None
        }
    
    def start_monitoring_cycle(self) -> Dict[str, Any]:
        """Start a complete monitoring cycle for all enabled profiles.
        
        Returns:
            Dict[str, Any]: Results of the monitoring cycle
        """
        if self._monitoring_active:
            logger.warning("Monitoring cycle already in progress")
            return {
                'success': False,
                'message': 'Monitoring cycle already in progress',
                'profiles_processed': 0,
                'changes_detected': 0
            }
        
        self._monitoring_active = True
        start_time = datetime.now()
        
        try:
            logger.info("Starting monitoring cycle")
            
            # Get enabled profiles that are due for scanning
            profiles = self._get_profiles_due_for_scan()
            
            if not profiles:
                logger.info("No profiles due for scanning")
                return {
                    'success': True,
                    'message': 'No profiles due for scanning',
                    'profiles_processed': 0,
                    'changes_detected': 0,
                    'duration': 0
                }
            
            # Ensure authentication before processing profiles
            if not self._ensure_authentication():
                logger.error("Failed to authenticate - aborting monitoring cycle")
                return {
                    'success': False,
                    'message': 'Authentication failed',
                    'profiles_processed': 0,
                    'changes_detected': 0
                }
            
            # Process each profile
            results = []
            total_changes = 0
            
            for profile in profiles:
                try:
                    logger.info(f"Processing profile: {profile.profile_username}")
                    
                    # Check if session rotation is needed
                    self.instagram_client.rotate_session_if_needed()
                    
                    # Process single profile
                    profile_result = self._process_single_profile(profile)
                    results.append(profile_result)
                    
                    if profile_result['success']:
                        total_changes += profile_result['changes_detected']
                        self._monitoring_stats['profiles_processed'] += 1
                    
                    # Add delay between profiles to avoid rate limiting
                    if len(results) < len(profiles):  # Don't delay after last profile
                        delay = self.config.PROFILE_PROCESSING_DELAY
                        logger.debug(f"Waiting {delay}s before next profile")
                        time.sleep(delay)
                        
                except Exception as e:
                    logger.error(f"Error processing profile {profile.profile_username}: {e}")
                    results.append({
                        'profile': profile.profile_username,
                        'success': False,
                        'error': str(e),
                        'changes_detected': 0
                    })
            
            # Update monitoring statistics
            self._monitoring_stats['total_runs'] += 1
            self._monitoring_stats['changes_detected'] += total_changes
            
            successful_profiles = sum(1 for r in results if r['success'])
            if successful_profiles > 0:
                self._monitoring_stats['successful_runs'] += 1
            else:
                self._monitoring_stats['failed_runs'] += 1
            
            self._last_monitoring_run = start_time
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Monitoring cycle completed: {successful_profiles}/{len(profiles)} profiles successful, {total_changes} changes detected")
            
            return {
                'success': True,
                'message': f'Processed {len(profiles)} profiles',
                'profiles_processed': len(profiles),
                'successful_profiles': successful_profiles,
                'changes_detected': total_changes,
                'duration': duration,
                'profile_results': results
            }
            
        except Exception as e:
            logger.error(f"Monitoring cycle failed: {e}")
            self._monitoring_stats['failed_runs'] += 1
            self._monitoring_stats['last_error'] = str(e)
            
            return {
                'success': False,
                'message': f'Monitoring cycle failed: {e}',
                'profiles_processed': 0,
                'changes_detected': 0,
                'error': str(e)
            }
            
        finally:
            self._monitoring_active = False
    
    def _get_profiles_due_for_scan(self) -> List[MonitoringConfig]:
        """Get profiles that are enabled and due for scanning.
        
        Returns:
            List[MonitoringConfig]: Profiles due for scanning
        """
        try:
            enabled_profiles = self.profile_repo.get_enabled_profiles()
            due_profiles = []
            
            for profile in enabled_profiles:
                if profile.is_due_for_scan:
                    due_profiles.append(profile)
                    logger.debug(f"Profile {profile.profile_username} is due for scan")
                else:
                    logger.debug(f"Profile {profile.profile_username} not due for scan yet")
            
            return due_profiles
            
        except Exception as e:
            logger.error(f"Error getting profiles due for scan: {e}")
            return []
    
    def _ensure_authentication(self) -> bool:
        """Ensure Instagram client is authenticated.
        
        Returns:
            bool: True if authenticated successfully
        """
        if self.instagram_client.is_authenticated():
            return True
        
        logger.info("Attempting to authenticate Instagram client")
        return self.instagram_client.authenticate()
    
    def _process_single_profile(self, profile: MonitoringConfig) -> Dict[str, Any]:
        """Process a single profile for changes.
        
        Args:
            profile: Profile configuration to process
            
        Returns:
            Dict[str, Any]: Processing results
        """
        try:
            # Scan profile for current data
            scan_result = self.profile_scanner.scan_profile(profile.profile_username)
            
            if not scan_result['success']:
                return {
                    'profile': profile.profile_username,
                    'success': False,
                    'error': scan_result.get('error', 'Unknown scan error'),
                    'changes_detected': 0
                }
            
            current_data = scan_result['data']
            
            # Get previous data from database
            previous_followers = self.follower_repo.get_current_followers(profile.profile_id)
            previous_following = self.follower_repo.get_current_following(profile.profile_id)
            
            # Detect changes
            changes = self.change_detector.detect_changes(
                profile_username=profile.profile_username,
                profile_id=profile.profile_id,
                current_followers=current_data['followers'],
                previous_followers=previous_followers,
                current_following=current_data['following'],
                previous_following=previous_following
            )
            
            # Process and store changes
            processing_result = self.data_processor.process_changes(
                profile=profile,
                profile_info=current_data['profile_info'],
                current_followers=current_data['followers'],
                current_following=current_data['following'],
                changes=changes
            )
            
            if not processing_result['success']:
                return {
                    'profile': profile.profile_username,
                    'success': False,
                    'error': processing_result.get('error', 'Unknown processing error'),
                    'changes_detected': len(changes)
                }
            
            logger.info(f"Successfully processed profile {profile.profile_username}: {len(changes)} changes detected")
            
            return {
                'profile': profile.profile_username,
                'success': True,
                'changes_detected': len(changes),
                'follower_changes': len([c for c in changes if c.is_follower_change]),
                'following_changes': len([c for c in changes if c.is_following_change]),
                'current_followers': len(current_data['followers']),
                'current_following': len(current_data['following'])
            }
            
        except Exception as e:
            logger.error(f"Error processing profile {profile.profile_username}: {e}")
            return {
                'profile': profile.profile_username,
                'success': False,
                'error': str(e),
                'changes_detected': 0
            }
    
    def monitor_single_profile(self, username: str) -> Dict[str, Any]:
        """Monitor a single profile immediately.
        
        Args:
            username: Instagram username to monitor
            
        Returns:
            Dict[str, Any]: Monitoring results
        """
        try:
            # Get profile configuration
            profile = self.profile_repo.get_profile_by_username(username)
            if not profile:
                return {
                    'success': False,
                    'message': f'Profile {username} not found in monitoring list',
                    'changes_detected': 0
                }
            
            # Ensure authentication
            if not self._ensure_authentication():
                return {
                    'success': False,
                    'message': 'Authentication failed',
                    'changes_detected': 0
                }
            
            # Process the profile
            result = self._process_single_profile(profile)
            
            if result['success']:
                self._monitoring_stats['profiles_processed'] += 1
                self._monitoring_stats['changes_detected'] += result['changes_detected']
            
            return result
            
        except Exception as e:
            logger.error(f"Error monitoring single profile {username}: {e}")
            return {
                'success': False,
                'message': f'Error monitoring profile: {e}',
                'changes_detected': 0,
                'error': str(e)
            }
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring service status.
        
        Returns:
            Dict[str, Any]: Status information
        """
        return {
            'monitoring_active': self._monitoring_active,
            'last_monitoring_run': self._last_monitoring_run.isoformat() if self._last_monitoring_run else None,
            'authenticated': self.instagram_client.is_authenticated(),
            'current_username': self.instagram_client.get_current_username(),
            'statistics': self._monitoring_stats.copy(),
            'session_info': self.instagram_client.get_session_info()
        }
    
    def add_profile_to_monitoring(self, username: str, enabled: bool = True) -> Dict[str, Any]:
        """Add a new profile to monitoring.
        
        Args:
            username: Instagram username to add
            enabled: Whether monitoring should be enabled
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            # Check if profile already exists
            existing_profile = self.profile_repo.get_profile_by_username(username)
            if existing_profile:
                return {
                    'success': False,
                    'message': f'Profile {username} already exists in monitoring list'
                }
            
            # Ensure authentication to validate profile
            if not self._ensure_authentication():
                return {
                    'success': False,
                    'message': 'Authentication required to add profile'
                }
            
            # Get profile info to validate it exists
            profile_info = self.instagram_client.get_profile_info(username)
            if not profile_info:
                return {
                    'success': False,
                    'message': f'Could not fetch profile information for {username}'
                }
            
            # Create monitoring configuration
            config = MonitoringConfig(
                profile_username=username,
                enabled=enabled,
                display_name=profile_info.display_name,
                is_private=profile_info.is_private
            )
            
            # Create profile in database
            profile_id = self.profile_repo.create_profile(config)
            config.profile_id = profile_id
            
            logger.info(f"Added profile {username} to monitoring (ID: {profile_id})")
            
            return {
                'success': True,
                'message': f'Profile {username} added to monitoring',
                'profile_id': profile_id,
                'profile_info': {
                    'username': profile_info.username,
                    'display_name': profile_info.display_name,
                    'follower_count': profile_info.follower_count,
                    'following_count': profile_info.following_count,
                    'is_private': profile_info.is_private
                }
            }
            
        except Exception as e:
            logger.error(f"Error adding profile {username} to monitoring: {e}")
            return {
                'success': False,
                'message': f'Error adding profile: {e}',
                'error': str(e)
            }
    
    def remove_profile_from_monitoring(self, username: str) -> Dict[str, Any]:
        """Remove a profile from monitoring.
        
        Args:
            username: Instagram username to remove
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            # Check if profile exists
            profile = self.profile_repo.get_profile_by_username(username)
            if not profile:
                return {
                    'success': False,
                    'message': f'Profile {username} not found in monitoring list'
                }
            
            # Delete profile and all associated data
            success = self.profile_repo.delete_profile(username)
            
            if success:
                logger.info(f"Removed profile {username} from monitoring")
                return {
                    'success': True,
                    'message': f'Profile {username} removed from monitoring'
                }
            else:
                return {
                    'success': False,
                    'message': f'Failed to remove profile {username}'
                }
                
        except Exception as e:
            logger.error(f"Error removing profile {username} from monitoring: {e}")
            return {
                'success': False,
                'message': f'Error removing profile: {e}',
                'error': str(e)
            }
    
    def update_profile_monitoring(self, username: str, enabled: bool) -> Dict[str, Any]:
        """Update profile monitoring status.
        
        Args:
            username: Instagram username
            enabled: Whether monitoring should be enabled
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            # Get existing profile
            profile = self.profile_repo.get_profile_by_username(username)
            if not profile:
                return {
                    'success': False,
                    'message': f'Profile {username} not found in monitoring list'
                }
            
            # Update enabled status
            profile.enabled = enabled
            success = self.profile_repo.update_profile(profile)
            
            if success:
                status = "enabled" if enabled else "disabled"
                logger.info(f"Profile {username} monitoring {status}")
                return {
                    'success': True,
                    'message': f'Profile {username} monitoring {status}'
                }
            else:
                return {
                    'success': False,
                    'message': f'Failed to update profile {username}'
                }
                
        except Exception as e:
            logger.error(f"Error updating profile {username} monitoring: {e}")
            return {
                'success': False,
                'message': f'Error updating profile: {e}',
                'error': str(e)
            }
    
    def get_monitored_profiles(self) -> List[Dict[str, Any]]:
        """Get list of all monitored profiles with their status.
        
        Returns:
            List[Dict[str, Any]]: List of profile information
        """
        try:
            profiles = self.profile_repo.get_all_profiles()
            profile_list = []
            
            for profile in profiles:
                profile_dict = {
                    'username': profile.profile_username,
                    'display_name': profile.display_name,
                    'enabled': profile.enabled,
                    'is_private': profile.is_private,
                    'last_scan': profile.last_scan.isoformat() if profile.last_scan else None,
                    'created_at': profile.created_at.isoformat() if profile.created_at else None,
                    'is_due_for_scan': profile.is_due_for_scan
                }
                profile_list.append(profile_dict)
            
            return profile_list
            
        except Exception as e:
            logger.error(f"Error getting monitored profiles: {e}")
            return []
    
    def cleanup_old_data(self) -> Dict[str, Any]:
        """Clean up old monitoring data based on retention policy.
        
        Returns:
            Dict[str, Any]: Cleanup results
        """
        try:
            from database.repositories import DataRetentionManager
            
            retention_manager = DataRetentionManager(
                retention_days=self.config.DATA_RETENTION_DAYS
            )
            
            cleanup_stats = retention_manager.cleanup_old_data()
            
            logger.info(f"Data cleanup completed: {cleanup_stats}")
            
            return {
                'success': True,
                'message': 'Data cleanup completed',
                'cleanup_stats': cleanup_stats
            }
            
        except Exception as e:
            logger.error(f"Error during data cleanup: {e}")
            return {
                'success': False,
                'message': f'Data cleanup failed: {e}',
                'error': str(e)
            }