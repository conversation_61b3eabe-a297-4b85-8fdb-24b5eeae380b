# Gunicorn configuration file for Instagram Follower Monitor
# Production deployment configuration

import multiprocessing
import os

# Server socket
bind = "127.0.0.1:8000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "instagram_monitor"

# Daemon mode
daemon = False
pidfile = "/tmp/instagram_monitor.pid"

# User/group to run as (uncomment and modify for production)
# user = "www-data"
# group = "www-data"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# SSL (uncomment and configure for HTTPS)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# Environment variables
raw_env = [
    "FLASK_ENV=production",
    "PYTHONPATH=/path/to/instagram-monitor"
]

# Preload application for better performance
preload_app = True

# Worker timeout for graceful shutdown
graceful_timeout = 30

# Enable worker recycling
worker_tmp_dir = "/dev/shm"