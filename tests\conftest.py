"""
Pytest configuration and shared fixtures for comprehensive test suite.

This module provides shared fixtures and configuration for all test modules,
including database setup, mock configurations, and test utilities.
"""

import pytest
import tempfile
import os
import logging
from unittest.mock import Mock, patch

from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, ChangeRepository, FollowerRepository, SettingsRepository
)
from config import Config


# Configure logging for tests
logging.basicConfig(level=logging.WARNING)  # Reduce log noise during tests


@pytest.fixture(scope="session")
def test_config():
    """Create test configuration."""
    config = Mock(spec=Config)
    config.MIN_REQUEST_DELAY = 0.1  # Faster for testing
    config.MAX_REQUEST_DELAY = 0.2
    config.MAX_RETRIES = 2
    config.ENCRYPTION_KEY = "test_encryption_key_for_testing_only"
    config.DATABASE_URL = ":memory:"  # In-memory database for tests
    config.SECRET_KEY = "test_secret_key"
    config.TESTING = True
    return config


@pytest.fixture
def temp_database():
    """Create a temporary database file for testing."""
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_file.close()
    
    # Initialize database
    db_manager = DatabaseManager(temp_file.name)
    db_manager.initialize_database()
    
    yield temp_file.name
    
    # Cleanup
    try:
        os.unlink(temp_file.name)
    except (OSError, FileNotFoundError):
        pass  # File might already be deleted


@pytest.fixture
def memory_database():
    """Create an in-memory database for fast testing."""
    db_path = ":memory:"
    db_manager = DatabaseManager(db_path)
    db_manager.initialize_database()
    return db_path


@pytest.fixture
def test_repositories(temp_database):
    """Create repository instances with temporary database."""
    return (
        ProfileRepository(temp_database),
        ChangeRepository(temp_database),
        FollowerRepository(temp_database),
        SettingsRepository(temp_database)
    )


@pytest.fixture
def memory_repositories(memory_database):
    """Create repository instances with in-memory database."""
    return (
        ProfileRepository(memory_database),
        ChangeRepository(memory_database),
        FollowerRepository(memory_database),
        SettingsRepository(memory_database)
    )


@pytest.fixture
def mock_instagram_client():
    """Create a mock Instagram client for testing."""
    client = Mock()
    client.is_authenticated.return_value = True
    client.get_current_username.return_value = "test_user"
    client.authenticate.return_value = True
    client.logout.return_value = None
    
    # Mock profile info
    client.get_profile_info.return_value = Mock(
        username="test_user",
        display_name="Test User",
        follower_count=100,
        following_count=50,
        is_private=False
    )
    
    # Mock followers and following
    client.get_followers.return_value = {"follower1", "follower2", "follower3"}
    client.get_following.return_value = {"following1", "following2"}
    
    return client


@pytest.fixture
def mock_authentication_manager():
    """Create a mock authentication manager."""
    auth_manager = Mock()
    auth_manager.has_stored_credentials.return_value = True
    auth_manager.retrieve_credentials.return_value = {
        'username': 'test_user',
        'password': 'test_password'
    }
    auth_manager.store_credentials.return_value = True
    auth_manager.validate_credentials.return_value = True
    auth_manager.clear_credentials.return_value = True
    return auth_manager


@pytest.fixture
def sample_profile_data():
    """Provide sample profile data for testing."""
    return {
        'username': 'testuser',
        'display_name': 'Test User',
        'follower_count': 1000,
        'following_count': 500,
        'is_private': False,
        'followers': {'user1', 'user2', 'user3', 'user4', 'user5'},
        'following': {'follow1', 'follow2', 'follow3'}
    }


@pytest.fixture
def sample_changes_data():
    """Provide sample change data for testing."""
    from models.data_models import FollowerChange, ChangeType
    from datetime import datetime, timedelta
    
    now = datetime.now()
    return [
        FollowerChange(
            profile_username="testuser",
            affected_username="new_follower",
            change_type=ChangeType.GAINED,
            timestamp=now - timedelta(hours=1),
            profile_id=1
        ),
        FollowerChange(
            profile_username="testuser",
            affected_username="lost_follower",
            change_type=ChangeType.LOST,
            timestamp=now - timedelta(hours=2),
            profile_id=1
        ),
        FollowerChange(
            profile_username="testuser",
            affected_username="new_following",
            change_type=ChangeType.STARTED_FOLLOWING,
            timestamp=now - timedelta(hours=3),
            profile_id=1
        ),
        FollowerChange(
            profile_username="testuser",
            affected_username="stopped_following",
            change_type=ChangeType.STOPPED_FOLLOWING,
            timestamp=now - timedelta(hours=4),
            profile_id=1
        )
    ]


# Pytest markers for different test categories
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "performance: marks tests as performance tests"
    )
    config.addinivalue_line(
        "markers", "api: marks tests as API tests"
    )
    config.addinivalue_line(
        "markers", "database: marks tests as database tests"
    )


# Test utilities
class TestDataGenerator:
    """Utility class for generating test data."""
    
    @staticmethod
    def generate_usernames(count, prefix="user"):
        """Generate a set of usernames."""
        return {f"{prefix}_{i:04d}" for i in range(count)}
    
    @staticmethod
    def generate_profile_config(username="testuser", enabled=True):
        """Generate a MonitoringConfig for testing."""
        from models.data_models import MonitoringConfig
        return MonitoringConfig(
            profile_username=username,
            display_name=f"Display {username}",
            is_private=False,
            enabled=enabled,
            interval_hours=2
        )
    
    @staticmethod
    def generate_profile_info(username="testuser", follower_count=100, following_count=50):
        """Generate ProfileInfo for testing."""
        from models.data_models import ProfileInfo
        return ProfileInfo(
            username=username,
            display_name=f"Display {username}",
            follower_count=follower_count,
            following_count=following_count,
            is_private=False
        )
    
    @staticmethod
    def generate_changes(profile_username, profile_id, count=10):
        """Generate a list of FollowerChange objects."""
        from models.data_models import FollowerChange, ChangeType
        from datetime import datetime, timedelta
        
        changes = []
        base_time = datetime.now()
        
        for i in range(count):
            change_type = ChangeType.GAINED if i % 2 == 0 else ChangeType.LOST
            change = FollowerChange(
                profile_username=profile_username,
                affected_username=f"affected_user_{i:03d}",
                change_type=change_type,
                timestamp=base_time - timedelta(hours=i),
                profile_id=profile_id
            )
            changes.append(change)
        
        return changes


@pytest.fixture
def test_data_generator():
    """Provide test data generator utility."""
    return TestDataGenerator


# Mock patches for external dependencies
@pytest.fixture
def mock_instaloader():
    """Mock instaloader module."""
    with patch('services.instagram_client.instaloader') as mock:
        # Mock Instaloader class
        mock_loader = Mock()
        mock.Instaloader.return_value = mock_loader
        
        # Mock Profile class
        mock_profile = Mock()
        mock_profile.username = "testuser"
        mock_profile.full_name = "Test User"
        mock_profile.followers = 100
        mock_profile.followees = 50
        mock_profile.is_private = False
        
        mock.Profile.from_username.return_value = mock_profile
        
        yield mock


@pytest.fixture
def mock_flask_app():
    """Create a mock Flask app for testing."""
    from flask import Flask
    
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['SECRET_KEY'] = 'test-secret-key'
    app.config['WTF_CSRF_ENABLED'] = False  # Disable CSRF for testing
    
    return app


# Performance testing utilities
class PerformanceAssertion:
    """Utility for performance assertions in tests."""
    
    @staticmethod
    def assert_execution_time(max_seconds):
        """Decorator to assert maximum execution time."""
        def decorator(func):
            def wrapper(*args, **kwargs):
                import time
                start_time = time.time()
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                assert execution_time <= max_seconds, (
                    f"Function {func.__name__} took {execution_time:.3f}s, "
                    f"expected <= {max_seconds}s"
                )
                return result
            return wrapper
        return decorator
    
    @staticmethod
    def assert_memory_usage(max_mb_increase):
        """Context manager to assert maximum memory increase."""
        import psutil
        
        class MemoryAssertion:
            def __init__(self, max_increase):
                self.max_increase = max_increase
                self.process = psutil.Process()
                self.initial_memory = None
            
            def __enter__(self):
                self.initial_memory = self.process.memory_info().rss / 1024 / 1024
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                final_memory = self.process.memory_info().rss / 1024 / 1024
                memory_increase = final_memory - self.initial_memory
                
                assert memory_increase <= self.max_increase, (
                    f"Memory increased by {memory_increase:.1f}MB, "
                    f"expected <= {self.max_increase}MB"
                )
        
        return MemoryAssertion(max_mb_increase)


@pytest.fixture
def performance_assertion():
    """Provide performance assertion utilities."""
    return PerformanceAssertion


# Database test utilities
@pytest.fixture
def db_test_utils():
    """Provide database testing utilities."""
    class DatabaseTestUtils:
        @staticmethod
        def count_table_rows(db_path, table_name):
            """Count rows in a database table."""
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        
        @staticmethod
        def get_table_schema(db_path, table_name):
            """Get table schema information."""
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            schema = cursor.fetchall()
            conn.close()
            return schema
        
        @staticmethod
        def execute_query(db_path, query, params=None):
            """Execute a custom query and return results."""
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            results = cursor.fetchall()
            conn.close()
            return results
    
    return DatabaseTestUtils


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_test_files():
    """Automatically cleanup test files after each test."""
    yield
    
    # Cleanup any temporary files that might have been created
    import glob
    import os
    
    temp_patterns = [
        "test_*.db",
        "*.db-journal",
        "*.db-wal",
        "*.db-shm"
    ]
    
    for pattern in temp_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.unlink(file_path)
            except (OSError, FileNotFoundError):
                pass  # File might already be deleted or in use