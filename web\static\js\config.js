/**
 * Application configuration and constants
 * Centralized configuration for the frontend application
 */

// Application configuration
window.AppConfig = {
    // API endpoints
    api: {
        base: '/api',
        dashboard: {
            stats: '/api/dashboard/stats',
            recentChanges: '/api/dashboard/recent-changes',
            changeDistribution: '/api/dashboard/change-distribution',
            profileComparison: '/api/dashboard/profile-comparison'
        },
        profile: {
            stats: '/api/profile/{username}/stats',
            trendData: '/api/profile/{username}/trend-data',
            changeDistribution: '/api/profile/{username}/change-distribution',
            toggle: '/profiles/{username}/toggle'
        },
        monitoring: {
            status: '/api/monitoring/status'
        },
        search: {
            base: '/api/search',
            export: '/api/search/export'
        },
        settings: {
            status: '/api/settings/status'
        }
    },
    
    // UI configuration
    ui: {
        refreshInterval: 300000, // 5 minutes
        notificationTimeout: 5000, // 5 seconds
        searchDebounceDelay: 300, // 300ms
        animationDuration: 300, // 300ms
        maxVisiblePages: 5, // pagination
        defaultPageSize: 20
    },
    
    // Chart configuration
    charts: {
        colors: {
            primary: '#0d6efd',
            success: '#198754',
            danger: '#dc3545',
            warning: '#fd7e14',
            info: '#0dcaf0',
            light: '#f8f9fa',
            dark: '#212529'
        },
        defaultOptions: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    },
    
    // Validation rules
    validation: {
        username: {
            pattern: /^[a-zA-Z0-9._]{1,30}$/,
            minLength: 1,
            maxLength: 30
        },
        password: {
            minLength: 6
        },
        email: {
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        }
    },
    
    // Change types and their display properties
    changeTypes: {
        gained: {
            label: 'New Follower',
            color: '#198754',
            badgeColor: 'success',
            icon: 'bi-person-plus'
        },
        lost: {
            label: 'Lost Follower',
            color: '#dc3545',
            badgeColor: 'danger',
            icon: 'bi-person-dash'
        },
        started_following: {
            label: 'Started Following',
            color: '#0d6efd',
            badgeColor: 'primary',
            icon: 'bi-person-check'
        },
        stopped_following: {
            label: 'Stopped Following',
            color: '#fd7e14',
            badgeColor: 'warning',
            icon: 'bi-person-x'
        }
    },
    
    // Status indicators
    statusTypes: {
        active: {
            label: 'Active',
            color: '#198754',
            class: 'status-active'
        },
        inactive: {
            label: 'Inactive',
            color: '#dc3545',
            class: 'status-inactive'
        },
        pending: {
            label: 'Pending',
            color: '#ffc107',
            class: 'status-pending'
        }
    },
    
    // Date/time formatting
    dateTime: {
        formats: {
            short: 'MMM DD',
            medium: 'MMM DD, YYYY',
            long: 'MMMM DD, YYYY HH:mm',
            time: 'HH:mm:ss'
        },
        locale: 'en-US'
    },
    
    // Feature flags
    features: {
        liveSearch: true,
        autoRefresh: true,
        chartAnimations: true,
        notifications: true,
        exportData: true
    }
};

// Utility functions for configuration
window.AppConfig.utils = {
    /**
     * Get API endpoint URL with parameter substitution
     */
    getApiUrl: function(endpoint, params = {}) {
        let url = endpoint;
        Object.entries(params).forEach(([key, value]) => {
            url = url.replace(`{${key}}`, encodeURIComponent(value));
        });
        return url;
    },
    
    /**
     * Get change type configuration
     */
    getChangeTypeConfig: function(changeType) {
        return window.AppConfig.changeTypes[changeType] || {
            label: changeType,
            color: '#6c757d',
            badgeColor: 'secondary',
            icon: 'bi-question'
        };
    },
    
    /**
     * Get status type configuration
     */
    getStatusTypeConfig: function(statusType) {
        return window.AppConfig.statusTypes[statusType] || {
            label: statusType,
            color: '#6c757d',
            class: 'status-unknown'
        };
    },
    
    /**
     * Check if feature is enabled
     */
    isFeatureEnabled: function(feature) {
        return window.AppConfig.features[feature] === true;
    },
    
    /**
     * Get validation rule for field type
     */
    getValidationRule: function(fieldType) {
        return window.AppConfig.validation[fieldType] || {};
    }
};

// Export configuration for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.AppConfig;
}