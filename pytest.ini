[tool:pytest]
# Pytest configuration for Instagram Follower Monitor

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    performance: marks tests as performance tests
    api: marks tests as API tests
    database: marks tests as database tests
    mock: marks tests that use mocking

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes

# Coverage options (when using --cov)
# These are applied when pytest-cov is used
# --cov-report=term-missing
# --cov-report=html
# --cov-fail-under=80

# Minimum version
minversion = 6.0

# Test timeout (in seconds) - prevents hanging tests
timeout = 300

# Warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    # Ignore specific warnings from dependencies
    ignore:.*imp module.*:DeprecationWarning
    ignore:.*distutils.*:DeprecationWarning

# Logging
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test collection
collect_ignore = [
    "setup.py",
    "build",
    "dist",
    ".git",
    ".pytest_cache",
    "__pycache__",
    "*.egg-info"
]

# Parallel execution (if pytest-xdist is installed)
# addopts = -n auto

# JUnit XML output (for CI/CD)
# junit_family = xunit2
# junit_logging = system-out