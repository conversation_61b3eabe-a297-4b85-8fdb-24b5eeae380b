{% extends "base.html" %}

{% block title %}{{ profile.profile_username }} - Profile Details{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('main.profiles') }}">Profiles</a></li>
                <li class="breadcrumb-item active">@{{ profile.profile_username }}</li>
            </ol>
        </nav>
        
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1 class="h2 mb-1">
                    <span class="status-indicator {% if profile.enabled %}status-active{% else %}status-inactive{% endif %}"></span>
                    @{{ profile.profile_username }}
                </h1>
                {% if profile.display_name %}
                <p class="text-muted mb-2">{{ profile.display_name }}</p>
                {% endif %}
                <div class="d-flex align-items-center gap-3 flex-wrap">
                    <span class="badge {% if profile.enabled %}bg-success{% else %}bg-secondary{% endif %}">
                        {% if profile.enabled %}Monitoring Active{% else %}Monitoring Disabled{% endif %}
                    </span>
                    {% if profile.is_private %}
                    <span class="badge bg-warning">Private Profile</span>
                    {% endif %}
                    <span class="badge bg-info">{{ profile.interval_hours }}h interval</span>
                    {% if profile.last_scan %}
                    <small class="text-muted">
                        <i class="bi bi-clock"></i> Last scanned {{ profile.last_scan|timeago }}
                    </small>
                    {% else %}
                    <small class="text-warning">
                        <i class="bi bi-exclamation-triangle"></i> Never scanned
                    </small>
                    {% endif %}
                </div>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="refreshProfileData()">
                    <i class="bi bi-arrow-clockwise" id="profileRefreshIcon"></i> Refresh
                </button>
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-gear"></i> Manage
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{{ url_for('main.edit_profile', username=profile.profile_username) }}">
                                <i class="bi bi-pencil"></i> Edit Settings
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        {% if profile.enabled %}
                        <li>
                            <button class="dropdown-item text-primary" onclick="forceScanProfile()">
                                <i class="bi bi-arrow-clockwise"></i> Force Scan
                            </button>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        {% endif %}
                        <li>
                            <button class="dropdown-item text-{{ 'warning' if profile.enabled else 'success' }}"
                                    onclick="toggleProfileMonitoring()">
                                <i class="bi bi-{{ 'pause' if profile.enabled else 'play' }}"></i>
                                {{ 'Disable' if profile.enabled else 'Enable' }} Monitoring
                            </button>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="{{ url_for('main.changes', profile=profile.profile_username) }}">
                                <i class="bi bi-list"></i> View All Changes
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <button class="dropdown-item text-danger" onclick="confirmDelete()">
                                <i class="bi bi-trash"></i> Delete Profile
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number text-primary" id="currentFollowers">{{ stats.get('current_followers', 0) }}</div>
                <div class="stat-label">Current Followers</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number text-info" id="currentFollowing">{{ stats.get('current_following', 0) }}</div>
                <div class="stat-label">Current Following</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number text-success" id="followersGained">+{{ stats.get('followers_gained_30d', 0) }}</div>
                <div class="stat-label">Followers Gained (30d)</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-stat">
            <div class="card-body">
                <div class="stat-number text-danger" id="followersLost">-{{ stats.get('followers_lost_30d', 0) }}</div>
                <div class="stat-label">Followers Lost (30d)</div>
            </div>
        </div>
    </div>
</div>

<!-- Data Visualization Charts -->
<div class="row mb-4">
    <div class="col">
        {% set username = profile.profile_username %}
        {% include 'charts.html' %}
    </div>
</div>

<!-- Recent Changes for This Profile -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-activity"></i> Recent Changes
                </h5>
                <a href="{{ url_for('main.changes', profile=profile.profile_username) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-list"></i> View All
                </a>
            </div>
            <div class="card-body">
                <div id="profileRecentChanges">
                    {% if recent_changes %}
                    <div class="timeline">
                        {% for change in recent_changes %}
                        <div class="timeline-item change-item change-{{ change.change_type.value.replace('_', '-') }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="fw-medium">
                                        {{ change|format_change }}
                                    </div>
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> {{ change.timestamp|timeago }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary">{{ change.change_type.value.replace('_', ' ').title() }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-clock-history fs-1"></i>
                        <p class="mt-2">No changes recorded yet</p>
                        <small>Changes will appear here once monitoring detects activity</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    
    // Refresh profile data
    async function refreshProfileData() {
        const refreshIcon = document.getElementById('profileRefreshIcon');
        refreshIcon.classList.add('spinning');
        
        try {
            const response = await fetch(`/api/profile/{{ profile.profile_username }}/stats`);
            if (response.ok) {
                const stats = await response.json();
                
                // Update statistics
                document.getElementById('currentFollowers').textContent = stats.current_followers.toLocaleString();
                document.getElementById('currentFollowing').textContent = stats.current_following.toLocaleString();
                document.getElementById('followersGained').textContent = `+${stats.followers_gained_30d}`;
                document.getElementById('followersLost').textContent = `-${stats.followers_lost_30d}`;
                
                // Refresh charts
                if (window.chartUtils) {
                    window.chartUtils.refreshCharts();
                }
            }
            
            // Refresh recent changes
            const changesResponse = await fetch(`/api/dashboard/recent-changes?limit=20`);
            if (changesResponse.ok) {
                const allChanges = await changesResponse.json();
                // Filter changes for this profile
                const profileChanges = allChanges.filter(change => 
                    change.profile_username === '{{ profile.profile_username }}'
                );
                updateProfileRecentChanges(profileChanges);
            }
            
        } catch (error) {
            console.error('Error refreshing profile data:', error);
        } finally {
            refreshIcon.classList.remove('spinning');
        }
    }
    
    function updateProfileRecentChanges(changes) {
        const container = document.getElementById('profileRecentChanges');
        
        if (changes.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-clock-history fs-1"></i>
                    <p class="mt-2">No changes recorded yet</p>
                    <small>Changes will appear here once monitoring detects activity</small>
                </div>
            `;
            return;
        }
        
        let timelineHtml = '<div class="timeline">';
        changes.forEach(change => {
            const changeClass = change.change_type.replace(/_/g, '-');
            const badgeText = change.change_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            
            timelineHtml += `
                <div class="timeline-item change-item change-${changeClass}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="fw-medium">
                                ${change.display_text}
                            </div>
                            <small class="text-muted">
                                <i class="bi bi-clock"></i> ${timeAgo(change.timestamp)}
                            </small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-secondary">${badgeText}</span>
                        </div>
                    </div>
                </div>
            `;
        });
        timelineHtml += '</div>';
        
        container.innerHTML = timelineHtml;
    }
    
    // Toggle profile monitoring
    async function toggleProfileMonitoring() {
        try {
            const response = await fetch(`/profiles/{{ profile.profile_username }}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert(data.message, 'success');
                // Reload page to update UI
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.error || 'Failed to toggle monitoring', 'danger');
            }
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to toggle monitoring', 'danger');
        }
    }
    
    // Confirm profile deletion
    function confirmDelete() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Force scan functionality
    let forceScanModalDetail = null;

    function forceScanProfile() {
        // Reset modal state
        document.getElementById('scanProgressDetail').classList.add('d-none');
        document.getElementById('forceScanConfirmBtnDetail').disabled = false;
        document.getElementById('forceScanCancelBtnDetail').disabled = false;
        document.getElementById('forceScanConfirmBtnDetail').innerHTML = '<i class="bi bi-arrow-clockwise"></i> Start Scan';

        forceScanModalDetail = new bootstrap.Modal(document.getElementById('forceScanModal'));
        forceScanModalDetail.show();
    }

    // Handle force scan confirmation for profile detail page
    document.addEventListener('DOMContentLoaded', function() {
        const confirmBtn = document.getElementById('forceScanConfirmBtnDetail');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function() {
                performForceScanDetail('{{ profile.profile_username }}');
            });
        }
    });

    async function performForceScanDetail(username) {
        const confirmBtn = document.getElementById('forceScanConfirmBtnDetail');
        const cancelBtn = document.getElementById('forceScanCancelBtnDetail');
        const progressDiv = document.getElementById('scanProgressDetail');

        try {
            // Show loading state
            confirmBtn.disabled = true;
            cancelBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Scanning...';
            progressDiv.classList.remove('d-none');

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Perform the scan
            const response = await fetch(`/profiles/${username}/force-scan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                }
            });

            const data = await response.json();

            if (data.success) {
                // Show success state
                confirmBtn.innerHTML = '<i class="bi bi-check-circle"></i> Scan Complete';
                confirmBtn.className = 'btn btn-success';
                progressDiv.innerHTML = `
                    <div class="d-flex align-items-center text-success">
                        <i class="bi bi-check-circle me-2"></i>
                        <span>Scan completed! Changes detected: ${data.changes_detected}</span>
                    </div>
                `;

                // Show success message
                showAlert(data.message, 'success');

                // Auto-close modal after 2 seconds and refresh page data
                setTimeout(() => {
                    forceScanModalDetail.hide();
                    refreshProfileData();
                }, 2000);

            } else {
                throw new Error(data.error || 'Scan failed');
            }

        } catch (error) {
            console.error('Force scan error:', error);

            // Show error state
            confirmBtn.innerHTML = '<i class="bi bi-exclamation-circle"></i> Scan Failed';
            confirmBtn.className = 'btn btn-danger';
            progressDiv.innerHTML = `
                <div class="d-flex align-items-center text-danger">
                    <i class="bi bi-exclamation-circle me-2"></i>
                    <span>Scan failed: ${error.message}</span>
                </div>
            `;

            showAlert(`Scan failed: ${error.message}`, 'danger');

            // Re-enable cancel button
            cancelBtn.disabled = false;
        }
    }
    
    // Show alert message
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 3000);
    }
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Charts are initialized by the charts.js file
    });
</script>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete profile <strong>@{{ profile.profile_username }}</strong>?</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    This will permanently delete all follower data and change history for this profile.
                </p>
                <p>Type <strong>{{ profile.profile_username }}</strong> to confirm:</p>
                <input type="text" class="form-control" id="confirmUsername" placeholder="Enter username to confirm">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('main.delete_profile', username=profile.profile_username) }}" style="display: inline;">
                    {{ csrf_token() }}
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn" disabled>
                        <i class="bi bi-trash"></i> Delete Profile
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Force Scan Modal -->
<div class="modal fade" id="forceScanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Force Scan Profile</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to force an immediate scan of profile <strong>@{{ profile.profile_username }}</strong>?</p>
                <p class="text-info">
                    <i class="bi bi-info-circle"></i>
                    This will trigger an immediate follower data collection, bypassing the normal scheduled interval.
                    The scan may take a few moments to complete.
                </p>
                <div id="scanProgressDetail" class="d-none">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Scanning...</span>
                        </div>
                        <span>Scanning profile...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="forceScanCancelBtnDetail">Cancel</button>
                <button type="button" class="btn btn-primary" id="forceScanConfirmBtnDetail">
                    <i class="bi bi-arrow-clockwise"></i> Start Scan
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Enable delete button only when username is correctly typed
document.getElementById('confirmUsername').addEventListener('input', function(e) {
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    const expectedUsername = '{{ profile.profile_username }}';
    
    if (e.target.value === expectedUsername) {
        confirmBtn.disabled = false;
    } else {
        confirmBtn.disabled = true;
    }
});
</script>
{% endblock %}