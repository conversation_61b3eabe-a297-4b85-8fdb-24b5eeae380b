# Systemd service file for Instagram Follower Monitor
# Place this file in /etc/systemd/system/instagram-monitor.service

[Unit]
Description=Instagram Follower Monitor Web Application
After=network.target
Wants=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/var/www/instagram-monitor
Environment=PATH=/var/www/instagram-monitor/venv/bin
Environment=FLASK_ENV=production
Environment=PYTHONPATH=/var/www/instagram-monitor
ExecStart=/var/www/instagram-monitor/venv/bin/gunicorn --config deployment/gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/instagram-monitor/logs
ReadWritePaths=/var/www/instagram-monitor/backups
ReadWritePaths=/var/www/instagram-monitor/instagram_monitor.db
ReadWritePaths=/var/www/instagram-monitor/scheduler_jobs.db

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target