"""
End-to-end tests for critical user workflows.

This module provides comprehensive end-to-end tests that simulate complete
user workflows from start to finish, testing the integration of all components.
"""

import pytest
import tempfile
import os
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from flask import Flask

from database.connection import DatabaseManager
from database.repositories import (
    ProfileRepository, ChangeRepository, FollowerRepository, SettingsRepository
)
from models.data_models import (
    MonitoringConfig, FollowerChange, ChangeType, ProfileInfo
)
from services.monitoring_service import MonitoringService
from services.change_detector import ChangeDetector
from services.data_processor import DataProcessor
from services.profile_scanner import ProfileScanner
from services.instagram_client import InstagramClient
from config import Config


class TestCompleteMonitoringWorkflow:
    """End-to-end tests for complete monitoring workflow."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database for testing."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        
        # Initialize database
        db_manager = DatabaseManager(temp_file.name)
        db_manager.initialize_database()
        
        yield temp_file.name
        
        # Cleanup
        os.unlink(temp_file.name)
    
    @pytest.fixture
    def repositories(self, temp_db):
        """Create repository instances."""
        return (
            ProfileRepository(temp_db),
            ChangeRepository(temp_db),
            FollowerRepository(temp_db),
            SettingsRepository(temp_db)
        )
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration."""
        config = Mock(spec=Config)
        config.MIN_REQUEST_DELAY = 0.1  # Faster for testing
        config.MAX_REQUEST_DELAY = 0.2
        config.MAX_RETRIES = 2
        config.ENCRYPTION_KEY = "test_key"
        return config
    
    @pytest.fixture
    def monitoring_service(self, repositories, mock_config):
        """Create monitoring service with mocked Instagram client."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        with patch('services.monitoring_service.InstagramClient') as mock_client_class:
            service = MonitoringService(mock_config)
            
            # Replace repositories with real ones
            service.profile_repo = profile_repo
            service.change_repo = change_repo
            service.follower_repo = follower_repo
            
            # Create real components
            service.change_detector = ChangeDetector()
            service.data_processor = DataProcessor(follower_repo, change_repo, profile_repo)
            service.profile_scanner = ProfileScanner(service.instagram_client, mock_config)
            
            return service
    
    def test_complete_profile_setup_and_monitoring(self, monitoring_service, repositories):
        """Test complete workflow from profile setup to monitoring."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Step 1: Add profile to monitoring
        result = monitoring_service.add_profile_to_monitoring("testuser")
        
        # Mock Instagram client for profile validation
        monitoring_service.instagram_client.is_authenticated.return_value = True
        monitoring_service.instagram_client.get_profile_info.return_value = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        result = monitoring_service.add_profile_to_monitoring("testuser")
        assert result['success'] is True
        assert result['profile_id'] is not None
        
        # Verify profile was created
        profile = profile_repo.get_profile_by_username("testuser")
        assert profile is not None
        assert profile.profile_username == "testuser"
        assert profile.enabled is True
        
        # Step 2: Perform initial scan (baseline establishment)
        monitoring_service.instagram_client.get_followers.return_value = {"user1", "user2", "user3"}
        monitoring_service.instagram_client.get_following.return_value = {"follow1", "follow2"}
        
        # Mock profile scanner
        monitoring_service.profile_scanner.scan_profile.return_value = {
            'success': True,
            'data': {
                'profile_info': ProfileInfo(
                    username="testuser",
                    display_name="Test User",
                    follower_count=103,
                    following_count=52,
                    is_private=False
                ),
                'followers': {"user1", "user2", "user3"},
                'following': {"follow1", "follow2"}
            }
        }
        
        # Run monitoring cycle
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['profiles_processed'] == 1
        
        # Verify baseline data was stored
        stored_followers = follower_repo.get_current_followers(profile.profile_id)
        stored_following = follower_repo.get_current_following(profile.profile_id)
        
        assert stored_followers == {"user1", "user2", "user3"}
        assert stored_following == {"follow1", "follow2"}
        
        # Step 3: Simulate changes and detect them
        # Update mock to return changed data
        monitoring_service.profile_scanner.scan_profile.return_value = {
            'success': True,
            'data': {
                'profile_info': ProfileInfo(
                    username="testuser",
                    display_name="Test User",
                    follower_count=104,  # +1 follower
                    following_count=51,  # -1 following
                    is_private=False
                ),
                'followers': {"user1", "user2", "user3", "user4"},  # Added user4
                'following': {"follow1"}  # Removed follow2
            }
        }
        
        # Run another monitoring cycle
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['changes_detected'] == 2  # +1 follower, -1 following
        
        # Verify changes were detected and stored
        recent_changes = change_repo.get_recent_changes(profile_id=profile.profile_id, limit=10)
        assert len(recent_changes) == 2
        
        # Check specific changes
        change_types = {change.change_type for change in recent_changes}
        affected_users = {change.affected_username for change in recent_changes}
        
        assert ChangeType.GAINED in change_types
        assert ChangeType.STOPPED_FOLLOWING in change_types
        assert "user4" in affected_users
        assert "follow2" in affected_users
        
        # Verify updated baseline
        updated_followers = follower_repo.get_current_followers(profile.profile_id)
        updated_following = follower_repo.get_current_following(profile.profile_id)
        
        assert updated_followers == {"user1", "user2", "user3", "user4"}
        assert updated_following == {"follow1"}
    
    def test_multiple_profiles_monitoring_workflow(self, monitoring_service, repositories):
        """Test monitoring workflow with multiple profiles."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Setup multiple profiles
        profiles_data = [
            ("user1", {"follower1", "follower2"}, {"following1"}),
            ("user2", {"follower3", "follower4", "follower5"}, {"following2", "following3"}),
            ("user3", {"follower6"}, {"following4", "following5", "following6"})
        ]
        
        # Mock Instagram client
        monitoring_service.instagram_client.is_authenticated.return_value = True
        
        # Add all profiles
        for username, followers, following in profiles_data:
            monitoring_service.instagram_client.get_profile_info.return_value = ProfileInfo(
                username=username,
                display_name=f"User {username}",
                follower_count=len(followers),
                following_count=len(following),
                is_private=False
            )
            
            result = monitoring_service.add_profile_to_monitoring(username)
            assert result['success'] is True
        
        # Verify all profiles were created
        all_profiles = profile_repo.get_all_profiles()
        assert len(all_profiles) == 3
        
        # Setup initial scan data for all profiles
        scan_results = {}
        for username, followers, following in profiles_data:
            scan_results[username] = {
                'success': True,
                'data': {
                    'profile_info': ProfileInfo(
                        username=username,
                        display_name=f"User {username}",
                        follower_count=len(followers),
                        following_count=len(following),
                        is_private=False
                    ),
                    'followers': followers,
                    'following': following
                }
            }
        
        # Mock profile scanner to return appropriate data based on username
        def mock_scan_profile(username):
            return scan_results.get(username, {'success': False, 'error': 'Not found'})
        
        monitoring_service.profile_scanner.scan_profile.side_effect = mock_scan_profile
        
        # Run initial monitoring cycle
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['profiles_processed'] == 3
        assert cycle_result['successful_profiles'] == 3
        
        # Verify baseline data for all profiles
        for username, expected_followers, expected_following in profiles_data:
            profile = profile_repo.get_profile_by_username(username)
            stored_followers = follower_repo.get_current_followers(profile.profile_id)
            stored_following = follower_repo.get_current_following(profile.profile_id)
            
            assert stored_followers == expected_followers
            assert stored_following == expected_following
        
        # Simulate changes for each profile
        updated_data = [
            ("user1", {"follower1", "follower2", "new_follower1"}, {"following1"}),  # +1 follower
            ("user2", {"follower3", "follower4"}, {"following2", "following3", "new_following1"}),  # -1 follower, +1 following
            ("user3", {"follower6"}, {"following4", "following5"})  # -1 following
        ]
        
        # Update scan results
        for username, followers, following in updated_data:
            scan_results[username] = {
                'success': True,
                'data': {
                    'profile_info': ProfileInfo(
                        username=username,
                        display_name=f"User {username}",
                        follower_count=len(followers),
                        following_count=len(following),
                        is_private=False
                    ),
                    'followers': followers,
                    'following': following
                }
            }
        
        # Run second monitoring cycle
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['changes_detected'] == 4  # Total changes across all profiles
        
        # Verify changes for each profile
        user1_profile = profile_repo.get_profile_by_username("user1")
        user1_changes = change_repo.get_changes_for_profile(user1_profile.profile_id)
        assert len(user1_changes) == 1
        assert user1_changes[0].change_type == ChangeType.GAINED
        assert user1_changes[0].affected_username == "new_follower1"
        
        user2_profile = profile_repo.get_profile_by_username("user2")
        user2_changes = change_repo.get_changes_for_profile(user2_profile.profile_id)
        assert len(user2_changes) == 2  # -1 follower, +1 following
        
        user3_profile = profile_repo.get_profile_by_username("user3")
        user3_changes = change_repo.get_changes_for_profile(user3_profile.profile_id)
        assert len(user3_changes) == 1
        assert user3_changes[0].change_type == ChangeType.STOPPED_FOLLOWING
        assert user3_changes[0].affected_username == "following6"
    
    def test_error_recovery_workflow(self, monitoring_service, repositories):
        """Test error recovery and resilience in monitoring workflow."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Setup profile
        monitoring_service.instagram_client.is_authenticated.return_value = True
        monitoring_service.instagram_client.get_profile_info.return_value = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        result = monitoring_service.add_profile_to_monitoring("testuser")
        assert result['success'] is True
        
        # Test 1: Instagram API failure
        monitoring_service.profile_scanner.scan_profile.return_value = {
            'success': False,
            'error': 'Instagram API error'
        }
        
        cycle_result = monitoring_service.start_monitoring_cycle()
        # Should still succeed overall but with failed profile
        assert cycle_result['success'] is True
        assert cycle_result['successful_profiles'] == 0
        assert cycle_result['profiles_processed'] == 1
        
        # Test 2: Partial data retrieval (followers succeed, following fails)
        monitoring_service.profile_scanner.scan_profile.return_value = {
            'success': True,
            'data': {
                'profile_info': ProfileInfo(
                    username="testuser",
                    display_name="Test User",
                    follower_count=100,
                    following_count=50,
                    is_private=False
                ),
                'followers': {"user1", "user2"},
                'following': set()  # Empty due to error
            }
        }
        
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['successful_profiles'] == 1
        
        # Should still store available data
        profile = profile_repo.get_profile_by_username("testuser")
        stored_followers = follower_repo.get_current_followers(profile.profile_id)
        assert stored_followers == {"user1", "user2"}
        
        # Test 3: Authentication failure recovery
        monitoring_service.instagram_client.is_authenticated.return_value = False
        monitoring_service.instagram_client.authenticate.return_value = False
        
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is False
        assert 'Authentication failed' in cycle_result['message']
        
        # Test 4: Recovery after authentication is restored
        monitoring_service.instagram_client.is_authenticated.return_value = True
        monitoring_service.profile_scanner.scan_profile.return_value = {
            'success': True,
            'data': {
                'profile_info': ProfileInfo(
                    username="testuser",
                    display_name="Test User",
                    follower_count=101,
                    following_count=51,
                    is_private=False
                ),
                'followers': {"user1", "user2", "user3"},
                'following': {"follow1"}
            }
        }
        
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['successful_profiles'] == 1
        
        # Should detect changes from previous successful scan
        recent_changes = change_repo.get_recent_changes(profile_id=profile.profile_id, limit=10)
        assert len(recent_changes) >= 1  # At least one change detected
    
    def test_profile_management_workflow(self, monitoring_service, repositories):
        """Test complete profile management workflow."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Mock Instagram client
        monitoring_service.instagram_client.is_authenticated.return_value = True
        monitoring_service.instagram_client.get_profile_info.return_value = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        # Step 1: Add profile
        result = monitoring_service.add_profile_to_monitoring("testuser")
        assert result['success'] is True
        profile_id = result['profile_id']
        
        # Step 2: Verify profile exists and is enabled
        profile = profile_repo.get_profile_by_username("testuser")
        assert profile is not None
        assert profile.enabled is True
        
        # Step 3: Disable monitoring
        result = monitoring_service.update_profile_monitoring("testuser", False)
        assert result['success'] is True
        assert 'disabled' in result['message']
        
        # Verify profile is disabled
        profile = profile_repo.get_profile_by_username("testuser")
        assert profile.enabled is False
        
        # Step 4: Verify disabled profile is not processed
        monitoring_service.profile_scanner.scan_profile.return_value = {
            'success': True,
            'data': {
                'profile_info': ProfileInfo(
                    username="testuser",
                    display_name="Test User",
                    follower_count=100,
                    following_count=50,
                    is_private=False
                ),
                'followers': {"user1"},
                'following': {"follow1"}
            }
        }
        
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['profiles_processed'] == 0  # No enabled profiles
        
        # Step 5: Re-enable monitoring
        result = monitoring_service.update_profile_monitoring("testuser", True)
        assert result['success'] is True
        assert 'enabled' in result['message']
        
        # Step 6: Verify profile is processed again
        cycle_result = monitoring_service.start_monitoring_cycle()
        assert cycle_result['success'] is True
        assert cycle_result['profiles_processed'] == 1
        
        # Step 7: Remove profile completely
        result = monitoring_service.remove_profile_from_monitoring("testuser")
        assert result['success'] is True
        
        # Verify profile is deleted
        profile = profile_repo.get_profile_by_username("testuser")
        assert profile is None
        
        # Verify associated data is cleaned up
        stored_followers = follower_repo.get_current_followers(profile_id)
        stored_following = follower_repo.get_current_following(profile_id)
        assert len(stored_followers) == 0
        assert len(stored_following) == 0
    
    def test_data_consistency_workflow(self, monitoring_service, repositories):
        """Test data consistency throughout the monitoring workflow."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Setup profile
        monitoring_service.instagram_client.is_authenticated.return_value = True
        monitoring_service.instagram_client.get_profile_info.return_value = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        result = monitoring_service.add_profile_to_monitoring("testuser")
        profile_id = result['profile_id']
        
        # Perform multiple monitoring cycles with different data
        monitoring_cycles = [
            # Cycle 1: Initial data
            {
                'followers': {"user1", "user2", "user3"},
                'following': {"follow1", "follow2"}
            },
            # Cycle 2: Add followers and following
            {
                'followers': {"user1", "user2", "user3", "user4", "user5"},
                'following': {"follow1", "follow2", "follow3"}
            },
            # Cycle 3: Remove some, add others
            {
                'followers': {"user1", "user3", "user5", "user6"},
                'following': {"follow1", "follow3", "follow4"}
            },
            # Cycle 4: Major changes
            {
                'followers': {"user1", "user7", "user8", "user9"},
                'following': {"follow5"}
            }
        ]
        
        all_changes = []
        
        for i, cycle_data in enumerate(monitoring_cycles):
            # Setup scan result
            monitoring_service.profile_scanner.scan_profile.return_value = {
                'success': True,
                'data': {
                    'profile_info': ProfileInfo(
                        username="testuser",
                        display_name="Test User",
                        follower_count=len(cycle_data['followers']),
                        following_count=len(cycle_data['following']),
                        is_private=False
                    ),
                    'followers': cycle_data['followers'],
                    'following': cycle_data['following']
                }
            }
            
            # Run monitoring cycle
            cycle_result = monitoring_service.start_monitoring_cycle()
            assert cycle_result['success'] is True
            
            # Verify data consistency
            stored_followers = follower_repo.get_current_followers(profile_id)
            stored_following = follower_repo.get_current_following(profile_id)
            
            assert stored_followers == cycle_data['followers']
            assert stored_following == cycle_data['following']
            
            # Track changes (except first cycle which establishes baseline)
            if i > 0:
                cycle_changes = change_repo.get_recent_changes(
                    profile_id=profile_id, 
                    limit=20
                )
                # Filter to changes from this cycle (approximate)
                new_changes = [c for c in cycle_changes if c not in all_changes]
                all_changes.extend(new_changes)
        
        # Verify total change history makes sense
        total_changes = change_repo.get_changes_for_profile(profile_id)
        assert len(total_changes) > 0
        
        # Verify change statistics
        stats = change_repo.get_change_statistics(profile_id=profile_id, days=1)
        assert stats['total_changes'] == len(total_changes)
        assert stats['followers_gained'] > 0
        assert stats['followers_lost'] > 0
        assert stats['following_started'] > 0
        assert stats['following_stopped'] > 0
        
        # Verify final state matches last cycle
        final_followers = follower_repo.get_current_followers(profile_id)
        final_following = follower_repo.get_current_following(profile_id)
        
        assert final_followers == monitoring_cycles[-1]['followers']
        assert final_following == monitoring_cycles[-1]['following']
    
    def test_monitoring_status_and_statistics_workflow(self, monitoring_service, repositories):
        """Test monitoring status and statistics throughout workflow."""
        profile_repo, change_repo, follower_repo, settings_repo = repositories
        
        # Initial status - no profiles
        status = monitoring_service.get_monitoring_status()
        assert status['monitoring_active'] is False
        
        profiles_list = monitoring_service.get_monitored_profiles()
        assert len(profiles_list) == 0
        
        # Add profiles
        monitoring_service.instagram_client.is_authenticated.return_value = True
        
        for i in range(3):
            username = f"user{i}"
            monitoring_service.instagram_client.get_profile_info.return_value = ProfileInfo(
                username=username,
                display_name=f"User {i}",
                follower_count=100 + i * 10,
                following_count=50 + i * 5,
                is_private=False
            )
            
            result = monitoring_service.add_profile_to_monitoring(username)
            assert result['success'] is True
        
        # Check status after adding profiles
        profiles_list = monitoring_service.get_monitored_profiles()
        assert len(profiles_list) == 3
        
        for profile_info in profiles_list:
            assert profile_info['enabled'] is True
            assert profile_info['last_scan'] is None  # Not scanned yet
        
        # Perform monitoring cycles and track statistics
        for cycle in range(3):
            # Setup scan results for all profiles
            def mock_scan_profile(username):
                user_num = int(username[-1])
                return {
                    'success': True,
                    'data': {
                        'profile_info': ProfileInfo(
                            username=username,
                            display_name=f"User {user_num}",
                            follower_count=100 + user_num * 10 + cycle,
                            following_count=50 + user_num * 5,
                            is_private=False
                        ),
                        'followers': {f"follower{j}" for j in range(user_num * 2 + cycle)},
                        'following': {f"following{j}" for j in range(user_num + 1)}
                    }
                }
            
            monitoring_service.profile_scanner.scan_profile.side_effect = mock_scan_profile
            
            # Run monitoring cycle
            cycle_result = monitoring_service.start_monitoring_cycle()
            assert cycle_result['success'] is True
            
            # Check updated status
            profiles_list = monitoring_service.get_monitored_profiles()
            for profile_info in profiles_list:
                assert profile_info['last_scan'] is not None
        
        # Verify final statistics
        status = monitoring_service.get_monitoring_status()
        assert 'statistics' in status
        
        # Check individual profile statistics
        for i in range(3):
            username = f"user{i}"
            profile = profile_repo.get_profile_by_username(username)
            stats = profile_repo.get_profile_stats(profile.profile_id)
            
            assert stats['current_followers'] > 0
            assert stats['current_following'] > 0
            if i > 0:  # First cycle establishes baseline, subsequent cycles may have changes
                assert 'total_changes' in stats
        
        # Test cleanup functionality
        cleanup_result = monitoring_service.cleanup_old_data()
        assert cleanup_result['success'] is True