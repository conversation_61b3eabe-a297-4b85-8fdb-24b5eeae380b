{% extends "base.html" %}

{% block title %}Service Unavailable - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Service Unavailable (503)
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-wrench fa-5x text-warning"></i>
                    </div>
                    
                    <h5 class="card-title">Service Temporarily Unavailable</h5>
                    <p class="card-text text-muted">
                        The service is temporarily unavailable due to maintenance or high load. Please try again in a few minutes.
                    </p>
                    
                    <div class="mt-4">
                        <h6>Possible reasons:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-cog me-2"></i>System maintenance in progress</li>
                            <li><i class="fas fa-chart-line me-2"></i>High server load</li>
                            <li><i class="fas fa-database me-2"></i>Database maintenance</li>
                        </ul>
                    </div>
                    
                    <div class="mt-4">
                        <button onclick="location.reload()" class="btn btn-primary">
                            <i class="fas fa-refresh me-2"></i>
                            Try Again
                        </button>
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}