"""Unit tests for data models."""

import pytest
from datetime import datetime
from models.data_models import (
    ProfileInfo, FollowerChange, MonitoringConfig, UserList, 
    SystemSettings, ChangeType, validate_username
)


class TestProfileInfo:
    """Test cases for ProfileInfo data model."""
    
    def test_valid_profile_creation(self):
        """Test creating a valid profile."""
        profile = ProfileInfo(
            username="testuser",
            display_name="Test User",
            follower_count=100,
            following_count=50,
            is_private=False
        )
        
        assert profile.username == "testuser"
        assert profile.display_name == "Test User"
        assert profile.follower_count == 100
        assert profile.following_count == 50
        assert profile.is_private is False
    
    def test_username_cleaning(self):
        """Test username cleaning removes @ symbol and converts to lowercase."""
        profile = ProfileInfo(username="@TestUser")
        assert profile.username == "testuser"
    
    def test_empty_username_raises_error(self):
        """Test that empty username raises ValueError."""
        with pytest.raises(ValueError, match="Username cannot be empty"):
            ProfileInfo(username="")
    
    def test_negative_follower_count_raises_error(self):
        """Test that negative follower count raises ValueError."""
        with pytest.raises(ValueError, match="Follower count cannot be negative"):
            ProfileInfo(username="testuser", follower_count=-1)
    
    def test_negative_following_count_raises_error(self):
        """Test that negative following count raises ValueError."""
        with pytest.raises(ValueError, match="Following count cannot be negative"):
            ProfileInfo(username="testuser", following_count=-1)


class TestFollowerChange:
    """Test cases for FollowerChange data model."""
    
    def test_valid_follower_change_creation(self):
        """Test creating a valid follower change."""
        change = FollowerChange(
            profile_username="testuser",
            affected_username="follower1",
            change_type=ChangeType.GAINED
        )
        
        assert change.profile_username == "testuser"
        assert change.affected_username == "follower1"
        assert change.change_type == ChangeType.GAINED
        assert isinstance(change.timestamp, datetime)
    
    def test_username_cleaning(self):
        """Test username cleaning in follower changes."""
        change = FollowerChange(
            profile_username="@TestUser",
            affected_username="@Follower1",
            change_type=ChangeType.GAINED
        )
        
        assert change.profile_username == "testuser"
        assert change.affected_username == "follower1"
    
    def test_empty_profile_username_raises_error(self):
        """Test that empty profile username raises ValueError."""
        with pytest.raises(ValueError, match="Profile username cannot be empty"):
            FollowerChange(
                profile_username="",
                affected_username="follower1",
                change_type=ChangeType.GAINED
            )
    
    def test_empty_affected_username_raises_error(self):
        """Test that empty affected username raises ValueError."""
        with pytest.raises(ValueError, match="Affected username cannot be empty"):
            FollowerChange(
                profile_username="testuser",
                affected_username="",
                change_type=ChangeType.GAINED
            )
    
    def test_invalid_change_type_raises_error(self):
        """Test that invalid change type raises ValueError."""
        with pytest.raises(ValueError, match="Change type must be a ChangeType enum value"):
            FollowerChange(
                profile_username="testuser",
                affected_username="follower1",
                change_type="invalid"
            )
    
    def test_is_follower_change_property(self):
        """Test is_follower_change property."""
        gained_change = FollowerChange("user", "follower", ChangeType.GAINED)
        lost_change = FollowerChange("user", "follower", ChangeType.LOST)
        following_change = FollowerChange("user", "follower", ChangeType.STARTED_FOLLOWING)
        
        assert gained_change.is_follower_change is True
        assert lost_change.is_follower_change is True
        assert following_change.is_follower_change is False
    
    def test_is_following_change_property(self):
        """Test is_following_change property."""
        started_change = FollowerChange("user", "follower", ChangeType.STARTED_FOLLOWING)
        stopped_change = FollowerChange("user", "follower", ChangeType.STOPPED_FOLLOWING)
        follower_change = FollowerChange("user", "follower", ChangeType.GAINED)
        
        assert started_change.is_following_change is True
        assert stopped_change.is_following_change is True
        assert follower_change.is_following_change is False


class TestMonitoringConfig:
    """Test cases for MonitoringConfig data model."""
    
    def test_valid_config_creation(self):
        """Test creating a valid monitoring configuration."""
        config = MonitoringConfig(
            profile_username="testuser",
            enabled=True,
            interval_hours=2
        )
        
        assert config.profile_username == "testuser"
        assert config.enabled is True
        assert config.interval_hours == 2
    
    def test_username_cleaning(self):
        """Test username cleaning in monitoring config."""
        config = MonitoringConfig(profile_username="@TestUser")
        assert config.profile_username == "testuser"
    
    def test_empty_username_raises_error(self):
        """Test that empty username raises ValueError."""
        with pytest.raises(ValueError, match="Profile username cannot be empty"):
            MonitoringConfig(profile_username="")
    
    def test_zero_interval_raises_error(self):
        """Test that zero interval raises ValueError."""
        with pytest.raises(ValueError, match="Interval hours must be positive"):
            MonitoringConfig(profile_username="testuser", interval_hours=0)
    
    def test_negative_interval_raises_error(self):
        """Test that negative interval raises ValueError."""
        with pytest.raises(ValueError, match="Interval hours must be positive"):
            MonitoringConfig(profile_username="testuser", interval_hours=-1)
    
    def test_excessive_interval_raises_error(self):
        """Test that excessive interval raises ValueError."""
        with pytest.raises(ValueError, match="Interval hours cannot exceed 168"):
            MonitoringConfig(profile_username="testuser", interval_hours=200)
    
    def test_is_due_for_scan_disabled(self):
        """Test is_due_for_scan returns False when disabled."""
        config = MonitoringConfig(profile_username="testuser", enabled=False)
        assert config.is_due_for_scan is False
    
    def test_is_due_for_scan_never_scanned(self):
        """Test is_due_for_scan returns True when never scanned."""
        config = MonitoringConfig(profile_username="testuser", enabled=True)
        assert config.is_due_for_scan is True
    
    def test_is_due_for_scan_recent_scan(self):
        """Test is_due_for_scan returns False for recent scan."""
        config = MonitoringConfig(
            profile_username="testuser",
            enabled=True,
            interval_hours=2,
            last_scan=datetime.now()
        )
        assert config.is_due_for_scan is False


class TestUserList:
    """Test cases for UserList data model."""
    
    def test_valid_user_list_creation(self):
        """Test creating a valid user list."""
        user_list = UserList(
            profile_username="testuser",
            usernames=["user1", "user2", "user3"],
            list_type="followers"
        )
        
        assert user_list.profile_username == "testuser"
        assert user_list.usernames == ["user1", "user2", "user3"]
        assert user_list.list_type == "followers"
        assert user_list.count == 3
    
    def test_username_cleaning(self):
        """Test username cleaning in user lists."""
        user_list = UserList(
            profile_username="@TestUser",
            usernames=["@User1", "@User2"],
            list_type="followers"
        )
        
        assert user_list.profile_username == "testuser"
        assert user_list.usernames == ["user1", "user2"]
    
    def test_duplicate_removal(self):
        """Test that duplicate usernames are removed."""
        user_list = UserList(
            profile_username="testuser",
            usernames=["user1", "user2", "user1", "user3"],
            list_type="followers"
        )
        
        assert user_list.usernames == ["user1", "user2", "user3"]
        assert user_list.count == 3
    
    def test_empty_username_raises_error(self):
        """Test that empty profile username raises ValueError."""
        with pytest.raises(ValueError, match="Profile username cannot be empty"):
            UserList(profile_username="", usernames=["user1"])
    
    def test_invalid_list_type_raises_error(self):
        """Test that invalid list type raises ValueError."""
        with pytest.raises(ValueError, match="List type must be 'followers' or 'following'"):
            UserList(profile_username="testuser", list_type="invalid")
    
    def test_to_set_method(self):
        """Test converting user list to set."""
        user_list = UserList(
            profile_username="testuser",
            usernames=["user1", "user2", "user3"]
        )
        
        user_set = user_list.to_set()
        assert user_set == {"user1", "user2", "user3"}
        assert isinstance(user_set, set)


class TestSystemSettings:
    """Test cases for SystemSettings data model."""
    
    def test_valid_settings_creation(self):
        """Test creating valid system settings."""
        settings = SystemSettings(
            monitoring_interval_hours=2,
            max_profiles=10,
            data_retention_days=365
        )
        
        assert settings.monitoring_interval_hours == 2
        assert settings.max_profiles == 10
        assert settings.data_retention_days == 365
    
    def test_zero_monitoring_interval_raises_error(self):
        """Test that zero monitoring interval raises ValueError."""
        with pytest.raises(ValueError, match="Monitoring interval must be positive"):
            SystemSettings(monitoring_interval_hours=0)
    
    def test_zero_max_profiles_raises_error(self):
        """Test that zero max profiles raises ValueError."""
        with pytest.raises(ValueError, match="Max profiles must be positive"):
            SystemSettings(max_profiles=0)
    
    def test_zero_retention_days_raises_error(self):
        """Test that zero retention days raises ValueError."""
        with pytest.raises(ValueError, match="Data retention days must be positive"):
            SystemSettings(data_retention_days=0)
    
    def test_negative_rate_limit_raises_error(self):
        """Test that negative rate limit raises ValueError."""
        with pytest.raises(ValueError, match="Rate limit delay cannot be negative"):
            SystemSettings(rate_limit_delay_seconds=-1)
    
    def test_negative_retry_attempts_raises_error(self):
        """Test that negative retry attempts raises ValueError."""
        with pytest.raises(ValueError, match="Max retry attempts cannot be negative"):
            SystemSettings(max_retry_attempts=-1)


class TestValidateUsername:
    """Test cases for validate_username function."""
    
    def test_valid_username(self):
        """Test validating a valid username."""
        result = validate_username("testuser123")
        assert result == "testuser123"
    
    def test_username_with_at_symbol(self):
        """Test username with @ symbol is cleaned."""
        result = validate_username("@testuser")
        assert result == "testuser"
    
    def test_uppercase_username(self):
        """Test uppercase username is converted to lowercase."""
        result = validate_username("TestUser")
        assert result == "testuser"
    
    def test_username_with_periods_and_underscores(self):
        """Test username with valid special characters."""
        result = validate_username("test.user_123")
        assert result == "test.user_123"
    
    def test_empty_username_raises_error(self):
        """Test that empty username raises ValueError."""
        with pytest.raises(ValueError, match="Username cannot be empty"):
            validate_username("")
    
    def test_username_with_invalid_characters_raises_error(self):
        """Test that username with invalid characters raises ValueError."""
        with pytest.raises(ValueError, match="Username contains invalid characters"):
            validate_username("test-user!")
    
    def test_username_too_long_raises_error(self):
        """Test that username longer than 30 characters raises ValueError."""
        long_username = "a" * 31
        with pytest.raises(ValueError, match="Username cannot exceed 30 characters"):
            validate_username(long_username)