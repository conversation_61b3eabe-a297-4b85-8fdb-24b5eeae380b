{% extends "base.html" %}

{% block title %}Error Monitoring - Instagram Follower Monitor{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-bug me-2"></i>
                    Error Monitoring
                </h2>
                <div>
                    <button onclick="location.reload()" class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh
                    </button>
                    <a href="{{ url_for('monitoring.system_status') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-heartbeat me-2"></i>
                        System Status
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Summary -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center border-danger">
                <div class="card-body">
                    <h3 class="text-danger">{{ critical_errors|length }}</h3>
                    <p class="card-text">Critical Errors (24h)</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <h3 class="text-warning">{{ health_data.alerts.error }}</h3>
                    <p class="card-text">Error Alerts (24h)</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center border-info">
                <div class="card-body">
                    <h3 class="text-info">{{ health_data.alerts.total }}</h3>
                    <p class="card-text">Total Alerts (24h)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Critical Errors -->
    {% if critical_errors %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Critical Errors (Last 24 Hours)
                    </h5>
                </div>
                <div class="card-body">
                    {% for error in critical_errors %}
                    <div class="card mb-3 border-danger">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ error.component }}</strong>
                                <span class="badge bg-danger ms-2">{{ error.error_type }}</span>
                            </div>
                            <small class="text-muted">{{ error.timestamp }}</small>
                        </div>
                        <div class="card-body">
                            <p class="card-text">{{ error.error_message }}</p>
                            {% if error.context %}
                            <div class="mt-3">
                                <h6>Context:</h6>
                                <div class="bg-light p-2 rounded">
                                    <pre><code>{{ error.context|tojson(indent=2) }}</code></pre>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Error Statistics by Component -->
    {% if health_data.components %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Error Statistics by Component
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Component</th>
                                    <th>Status</th>
                                    <th>Error Count</th>
                                    <th>Last Error</th>
                                    <th>Last Updated</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for component, status in health_data.components.items() %}
                                <tr>
                                    <td><code>{{ component }}</code></td>
                                    <td>
                                        {% if status.status == 'healthy' or status.status == 'ok' %}
                                            <span class="badge bg-success">{{ status.status.title() }}</span>
                                        {% elif status.status == 'warning' %}
                                            <span class="badge bg-warning">Warning</span>
                                        {% elif status.status == 'error' %}
                                            <span class="badge bg-danger">Error</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ status.status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if status.error_count %}
                                            <span class="badge bg-danger">{{ status.error_count }}</span>
                                        {% else %}
                                            <span class="text-muted">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if status.last_error %}
                                            <small class="text-danger">{{ status.last_error }}</small>
                                        {% else %}
                                            <span class="text-muted">None</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ status.last_updated }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Error Rate Trends -->
    {% if health_data.error_rates %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Error Rate Trends (Last Hour)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Component/Operation</th>
                                    <th>Error Rate</th>
                                    <th>Status</th>
                                    <th>Trend</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for component, rate in health_data.error_rates.items() %}
                                <tr>
                                    <td><code>{{ component }}</code></td>
                                    <td>{{ "%.2f"|format(rate * 100) }}%</td>
                                    <td>
                                        {% if rate < 0.01 %}
                                            <span class="badge bg-success">Excellent</span>
                                        {% elif rate < 0.05 %}
                                            <span class="badge bg-info">Good</span>
                                        {% elif rate < 0.1 %}
                                            <span class="badge bg-warning">Elevated</span>
                                        {% else %}
                                            <span class="badge bg-danger">High</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rate < 0.05 %}
                                            <i class="fas fa-arrow-down text-success"></i>
                                        {% elif rate < 0.1 %}
                                            <i class="fas fa-arrow-right text-warning"></i>
                                        {% else %}
                                            <i class="fas fa-arrow-up text-danger"></i>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                    <h4>No Error Data Available</h4>
                    <p class="text-muted">
                        Either the system is running perfectly, or error tracking hasn't collected enough data yet.
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Auto-refresh every 60 seconds
setTimeout(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}