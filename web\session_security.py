"""
Enhanced session security management for Flask application.

This module provides secure session handling, token management, and
session-based security features for the Instagram Follower Monitor.
"""

import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from flask import session, request, current_app, g
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class SecureSessionManager:
    """Enhanced session security management."""
    
    @staticmethod
    def initialize_session():
        """Initialize secure session with proper tokens and settings."""
        # Generate CSRF token if not present
        if 'csrf_token' not in session:
            session['csrf_token'] = secrets.token_hex(32)
        
        # Set session creation time
        if 'session_created' not in session:
            session['session_created'] = datetime.now().isoformat()
            session.permanent = True
        
        # Generate session fingerprint for additional security
        if 'session_fingerprint' not in session:
            fingerprint_data = f"{request.environ.get('HTTP_USER_AGENT', '')}{request.environ.get('REMOTE_ADDR', '')}"
            session['session_fingerprint'] = hashlib.sha256(fingerprint_data.encode()).hexdigest()
        
        # Set last activity timestamp
        session['last_activity'] = datetime.now().isoformat()
    
    @staticmethod
    def validate_session() -> bool:
        """Validate session integrity and security."""
        try:
            # Check if session exists
            if 'session_created' not in session:
                return False
            
            # Check session age (max 24 hours)
            session_created = datetime.fromisoformat(session['session_created'])
            if datetime.now() - session_created > timedelta(hours=24):
                logger.warning("Session expired due to age")
                return False
            
            # Check last activity (max 2 hours of inactivity)
            if 'last_activity' in session:
                last_activity = datetime.fromisoformat(session['last_activity'])
                if datetime.now() - last_activity > timedelta(hours=2):
                    logger.warning("Session expired due to inactivity")
                    return False
            
            # Validate session fingerprint
            if 'session_fingerprint' in session:
                fingerprint_data = f"{request.environ.get('HTTP_USER_AGENT', '')}{request.environ.get('REMOTE_ADDR', '')}"
                expected_fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()
                if not hmac.compare_digest(session['session_fingerprint'], expected_fingerprint):
                    logger.warning("Session fingerprint mismatch - possible session hijacking")
                    return False
            
            return True
        
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return False
    
    @staticmethod
    def refresh_session():
        """Refresh session tokens and timestamps."""
        # Update last activity
        session['last_activity'] = datetime.now().isoformat()
        
        # Regenerate CSRF token periodically (every hour)
        if 'csrf_generated' in session:
            csrf_generated = datetime.fromisoformat(session['csrf_generated'])
            if datetime.now() - csrf_generated > timedelta(hours=1):
                session['csrf_token'] = secrets.token_hex(32)
                session['csrf_generated'] = datetime.now().isoformat()
        else:
            session['csrf_generated'] = datetime.now().isoformat()
    
    @staticmethod
    def clear_session():
        """Securely clear session data."""
        session.clear()
        logger.info("Session cleared")
    
    @staticmethod
    def get_csrf_token() -> str:
        """Get current CSRF token."""
        return session.get('csrf_token', '')

class CSRFProtection:
    """Enhanced CSRF protection."""
    
    @staticmethod
    def validate_csrf_token(token: str) -> bool:
        """Validate CSRF token."""
        session_token = session.get('csrf_token')
        if not session_token or not token:
            return False
        
        return hmac.compare_digest(session_token, token)
    
    @staticmethod
    def get_csrf_token() -> str:
        """Get CSRF token for forms."""
        return session.get('csrf_token', '')

def require_valid_session(f):
    """Decorator to require valid session."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Initialize session if needed
        SecureSessionManager.initialize_session()
        
        # Validate session
        if not SecureSessionManager.validate_session():
            SecureSessionManager.clear_session()
            SecureSessionManager.initialize_session()
        
        # Refresh session
        SecureSessionManager.refresh_session()
        
        return f(*args, **kwargs)
    return decorated_function

def csrf_protect(f):
    """Decorator for CSRF protection on forms."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
            # Check for CSRF token in form data or headers
            csrf_token = request.form.get('csrf_token') or request.headers.get('X-CSRF-Token')
            
            if not csrf_token:
                logger.warning(f"CSRF token missing for {request.endpoint}")
                if request.is_json:
                    from flask import jsonify
                    return jsonify({'error': 'CSRF token required'}), 403
                else:
                    from flask import flash, redirect, url_for
                    flash('Security token missing. Please try again.', 'error')
                    return redirect(request.referrer or url_for('main.dashboard'))
            
            if not CSRFProtection.validate_csrf_token(csrf_token):
                logger.warning(f"Invalid CSRF token for {request.endpoint}")
                if request.is_json:
                    from flask import jsonify
                    return jsonify({'error': 'Invalid CSRF token'}), 403
                else:
                    from flask import flash, redirect, url_for
                    flash('Security token invalid. Please try again.', 'error')
                    return redirect(request.referrer or url_for('main.dashboard'))
        
        return f(*args, **kwargs)
    return decorated_function

def setup_session_security(app):
    """Set up session security for Flask app."""
    
    @app.before_request
    def before_request():
        """Initialize and validate session before each request."""
        # Skip session handling for static files and health checks
        if request.endpoint in ['static', 'health']:
            return
        
        # Initialize session
        SecureSessionManager.initialize_session()
        
        # Validate session for protected routes
        if request.endpoint and not request.endpoint.startswith('static'):
            if not SecureSessionManager.validate_session():
                SecureSessionManager.clear_session()
                SecureSessionManager.initialize_session()
            else:
                SecureSessionManager.refresh_session()
    
    @app.context_processor
    def inject_csrf_token():
        """Inject CSRF token into all templates."""
        return {
            'csrf_token': lambda: f'<input type="hidden" name="csrf_token" value="{CSRFProtection.get_csrf_token()}">'
        }
    
    @app.template_global()
    def get_csrf_token():
        """Template function to get CSRF token."""
        return CSRFProtection.get_csrf_token()

class RateLimitManager:
    """Enhanced rate limiting with session tracking."""
    
    def __init__(self):
        self.attempts = {}
    
    def check_rate_limit(self, identifier: str, max_attempts: int = 5, window_minutes: int = 15) -> bool:
        """Check if identifier is within rate limits."""
        current_time = datetime.now()
        
        # Clean old entries
        cutoff_time = current_time - timedelta(minutes=window_minutes)
        if identifier in self.attempts:
            self.attempts[identifier] = [
                attempt_time for attempt_time in self.attempts[identifier]
                if attempt_time > cutoff_time
            ]
        
        # Check current attempts
        if identifier not in self.attempts:
            self.attempts[identifier] = []
        
        if len(self.attempts[identifier]) >= max_attempts:
            return False
        
        # Record this attempt
        self.attempts[identifier].append(current_time)
        return True
    
    def reset_attempts(self, identifier: str):
        """Reset attempts for identifier."""
        if identifier in self.attempts:
            del self.attempts[identifier]

# Global rate limit manager
rate_limit_manager = RateLimitManager()

def rate_limit_by_session(max_attempts: int = 5, window_minutes: int = 15):
    """Rate limiting decorator using session ID."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Use session ID or IP as identifier
            identifier = session.get('csrf_token', request.environ.get('REMOTE_ADDR', 'unknown'))
            
            if not rate_limit_manager.check_rate_limit(identifier, max_attempts, window_minutes):
                logger.warning(f"Rate limit exceeded for {identifier} on {request.endpoint}")
                if request.is_json:
                    from flask import jsonify
                    return jsonify({
                        'error': 'Rate limit exceeded',
                        'message': f'Too many attempts. Try again in {window_minutes} minutes.'
                    }), 429
                else:
                    from flask import flash, redirect, url_for
                    flash(f'Too many attempts. Please wait {window_minutes} minutes before trying again.', 'error')
                    return redirect(request.referrer or url_for('main.dashboard'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_security_event(event_type: str, details: Dict[str, Any]):
    """Log security events for monitoring."""
    security_logger = logging.getLogger('security')
    
    event_data = {
        'timestamp': datetime.now().isoformat(),
        'event_type': event_type,
        'ip_address': request.environ.get('REMOTE_ADDR'),
        'user_agent': request.environ.get('HTTP_USER_AGENT'),
        'endpoint': request.endpoint,
        'session_id': session.get('csrf_token', 'unknown')[:8],  # First 8 chars only
        **details
    }
    
    security_logger.warning(f"Security Event: {event_type}", extra=event_data)