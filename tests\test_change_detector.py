"""
Unit tests for change detection algorithms.

Tests the ChangeDetector class and its algorithms for comparing
follower/following lists and detecting changes.
"""

import pytest
from datetime import datetime
from typing import Set

from models.data_models import Follower<PERSON>hange, ChangeType
from services.change_detector import ChangeDetector


class TestChangeDetector:
    """Test cases for ChangeDetector class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.detector = ChangeDetector()
        self.test_profile = "testuser"
        self.test_profile_id = 1
    
    def test_detect_no_changes(self):
        """Test detection when there are no changes."""
        current_followers = {"user1", "user2", "user3"}
        previous_followers = {"user1", "user2", "user3"}
        current_following = {"follow1", "follow2"}
        previous_following = {"follow1", "follow2"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        assert len(changes) == 0
    
    def test_detect_new_followers(self):
        """Test detection of new followers."""
        current_followers = {"user1", "user2", "user3", "user4"}
        previous_followers = {"user1", "user2"}
        current_following = {"follow1"}
        previous_following = {"follow1"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect 2 new followers
        follower_changes = [c for c in changes if c.is_follower_change]
        new_followers = [c for c in follower_changes if c.change_type == ChangeType.GAINED]
        
        assert len(new_followers) == 2
        new_follower_usernames = {c.affected_username for c in new_followers}
        assert new_follower_usernames == {"user3", "user4"}
        
        # Verify change properties
        for change in new_followers:
            assert change.profile_username == self.test_profile
            assert change.profile_id == self.test_profile_id
            assert change.change_type == ChangeType.GAINED
            assert isinstance(change.timestamp, datetime)
    
    def test_detect_lost_followers(self):
        """Test detection of lost followers."""
        current_followers = {"user1"}
        previous_followers = {"user1", "user2", "user3"}
        current_following = {"follow1"}
        previous_following = {"follow1"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect 2 lost followers
        follower_changes = [c for c in changes if c.is_follower_change]
        lost_followers = [c for c in follower_changes if c.change_type == ChangeType.LOST]
        
        assert len(lost_followers) == 2
        lost_follower_usernames = {c.affected_username for c in lost_followers}
        assert lost_follower_usernames == {"user2", "user3"}
        
        # Verify change properties
        for change in lost_followers:
            assert change.profile_username == self.test_profile
            assert change.profile_id == self.test_profile_id
            assert change.change_type == ChangeType.LOST
            assert isinstance(change.timestamp, datetime)
    
    def test_detect_new_following(self):
        """Test detection of new following."""
        current_followers = {"user1"}
        previous_followers = {"user1"}
        current_following = {"follow1", "follow2", "follow3"}
        previous_following = {"follow1"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect 2 new following
        following_changes = [c for c in changes if c.is_following_change]
        new_following = [c for c in following_changes if c.change_type == ChangeType.STARTED_FOLLOWING]
        
        assert len(new_following) == 2
        new_following_usernames = {c.affected_username for c in new_following}
        assert new_following_usernames == {"follow2", "follow3"}
        
        # Verify change properties
        for change in new_following:
            assert change.profile_username == self.test_profile
            assert change.profile_id == self.test_profile_id
            assert change.change_type == ChangeType.STARTED_FOLLOWING
            assert isinstance(change.timestamp, datetime)
    
    def test_detect_stopped_following(self):
        """Test detection of stopped following."""
        current_followers = {"user1"}
        previous_followers = {"user1"}
        current_following = {"follow1"}
        previous_following = {"follow1", "follow2", "follow3"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect 2 stopped following
        following_changes = [c for c in changes if c.is_following_change]
        stopped_following = [c for c in following_changes if c.change_type == ChangeType.STOPPED_FOLLOWING]
        
        assert len(stopped_following) == 2
        stopped_following_usernames = {c.affected_username for c in stopped_following}
        assert stopped_following_usernames == {"follow2", "follow3"}
        
        # Verify change properties
        for change in stopped_following:
            assert change.profile_username == self.test_profile
            assert change.profile_id == self.test_profile_id
            assert change.change_type == ChangeType.STOPPED_FOLLOWING
            assert isinstance(change.timestamp, datetime)
    
    def test_detect_mixed_changes(self):
        """Test detection of mixed follower and following changes."""
        current_followers = {"user1", "user4"}  # lost user2, gained user4
        previous_followers = {"user1", "user2"}
        current_following = {"follow1", "follow3"}  # lost follow2, gained follow3
        previous_following = {"follow1", "follow2"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        assert len(changes) == 4  # 2 follower changes + 2 following changes
        
        # Check follower changes
        follower_changes = [c for c in changes if c.is_follower_change]
        assert len(follower_changes) == 2
        
        gained_followers = [c for c in follower_changes if c.change_type == ChangeType.GAINED]
        lost_followers = [c for c in follower_changes if c.change_type == ChangeType.LOST]
        
        assert len(gained_followers) == 1
        assert gained_followers[0].affected_username == "user4"
        assert len(lost_followers) == 1
        assert lost_followers[0].affected_username == "user2"
        
        # Check following changes
        following_changes = [c for c in changes if c.is_following_change]
        assert len(following_changes) == 2
        
        started_following = [c for c in following_changes if c.change_type == ChangeType.STARTED_FOLLOWING]
        stopped_following = [c for c in following_changes if c.change_type == ChangeType.STOPPED_FOLLOWING]
        
        assert len(started_following) == 1
        assert started_following[0].affected_username == "follow3"
        assert len(stopped_following) == 1
        assert stopped_following[0].affected_username == "follow2"
    
    def test_detect_changes_with_invalid_usernames(self):
        """Test detection with invalid usernames that should be filtered out."""
        current_followers = {"user1", "user2", "", "invalid@user"}
        previous_followers = {"user1", ""}
        current_following = {"follow1"}
        previous_following = {"follow1"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should only detect valid username changes
        follower_changes = [c for c in changes if c.is_follower_change]
        assert len(follower_changes) == 1  # Only user2 should be detected as new
        assert follower_changes[0].affected_username == "user2"
        assert follower_changes[0].change_type == ChangeType.GAINED
    
    def test_detect_follower_changes_only(self):
        """Test detecting only follower changes."""
        current_followers = {"user1", "user2"}
        previous_followers = {"user1", "user3"}
        
        changes = self.detector.detect_follower_changes_only(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers
        )
        
        assert len(changes) == 2
        
        gained = [c for c in changes if c.change_type == ChangeType.GAINED]
        lost = [c for c in changes if c.change_type == ChangeType.LOST]
        
        assert len(gained) == 1
        assert gained[0].affected_username == "user2"
        assert len(lost) == 1
        assert lost[0].affected_username == "user3"
    
    def test_detect_following_changes_only(self):
        """Test detecting only following changes."""
        current_following = {"follow1", "follow2"}
        previous_following = {"follow1", "follow3"}
        
        changes = self.detector.detect_following_changes_only(
            self.test_profile, self.test_profile_id,
            current_following, previous_following
        )
        
        assert len(changes) == 2
        
        started = [c for c in changes if c.change_type == ChangeType.STARTED_FOLLOWING]
        stopped = [c for c in changes if c.change_type == ChangeType.STOPPED_FOLLOWING]
        
        assert len(started) == 1
        assert started[0].affected_username == "follow2"
        assert len(stopped) == 1
        assert stopped[0].affected_username == "follow3"
    
    def test_analyze_change_patterns(self):
        """Test analysis of change patterns."""
        changes = [
            FollowerChange(self.test_profile, "user1", ChangeType.GAINED, profile_id=self.test_profile_id),
            FollowerChange(self.test_profile, "user2", ChangeType.GAINED, profile_id=self.test_profile_id),
            FollowerChange(self.test_profile, "user3", ChangeType.LOST, profile_id=self.test_profile_id),
            FollowerChange(self.test_profile, "follow1", ChangeType.STARTED_FOLLOWING, profile_id=self.test_profile_id),
            FollowerChange(self.test_profile, "follow2", ChangeType.STOPPED_FOLLOWING, profile_id=self.test_profile_id),
        ]
        
        analysis = self.detector.analyze_change_patterns(changes)
        
        assert analysis['total_changes'] == 5
        assert analysis['follower_changes'] == 3
        assert analysis['following_changes'] == 2
        assert analysis['net_follower_change'] == 1  # +2 gained, -1 lost
        assert analysis['net_following_change'] == 0  # +1 started, -1 stopped
        assert analysis['gained_followers'] == 2
        assert analysis['lost_followers'] == 1
        assert analysis['started_following'] == 1
        assert analysis['stopped_following'] == 1
        
        # Check change type breakdown
        expected_breakdown = {
            'gained': 2,
            'lost': 1,
            'started_following': 1,
            'stopped_following': 1
        }
        assert analysis['change_types'] == expected_breakdown
        
        # Check affected users
        expected_users = {'user1', 'user2', 'user3', 'follow1', 'follow2'}
        assert set(analysis['affected_users']) == expected_users
    
    def test_analyze_empty_changes(self):
        """Test analysis of empty changes list."""
        analysis = self.detector.analyze_change_patterns([])
        
        assert analysis['total_changes'] == 0
        assert analysis['follower_changes'] == 0
        assert analysis['following_changes'] == 0
        assert analysis['net_follower_change'] == 0
        assert analysis['net_following_change'] == 0
        assert analysis['change_types'] == {}
        assert analysis['affected_users'] == []
    
    def test_compare_lists(self):
        """Test list comparison functionality."""
        current = {"user1", "user2", "user3"}
        previous = {"user1", "user4", "user5"}
        
        comparison = self.detector.compare_lists(current, previous)
        
        assert comparison['added'] == {"user2", "user3"}
        assert comparison['removed'] == {"user4", "user5"}
        assert comparison['unchanged'] == {"user1"}
    
    def test_compare_empty_lists(self):
        """Test comparison with empty lists."""
        current = {"user1", "user2"}
        previous = set()
        
        comparison = self.detector.compare_lists(current, previous)
        
        assert comparison['added'] == {"user1", "user2"}
        assert comparison['removed'] == set()
        assert comparison['unchanged'] == set()
        
        # Test reverse
        comparison2 = self.detector.compare_lists(set(), current)
        
        assert comparison2['added'] == set()
        assert comparison2['removed'] == {"user1", "user2"}
        assert comparison2['unchanged'] == set()
    
    def test_is_significant_change(self):
        """Test significance detection."""
        # Test with significant absolute change
        significant_changes = [
            FollowerChange(self.test_profile, f"user{i}", ChangeType.GAINED, profile_id=self.test_profile_id)
            for i in range(15)
        ]
        
        assert self.detector.is_significant_change(significant_changes, threshold_absolute=10)
        
        # Test with insignificant change
        minor_changes = [
            FollowerChange(self.test_profile, "user1", ChangeType.GAINED, profile_id=self.test_profile_id)
        ]
        
        assert not self.detector.is_significant_change(minor_changes, threshold_absolute=10)
        
        # Test with empty changes
        assert not self.detector.is_significant_change([])
    
    def test_filter_changes_by_type(self):
        """Test filtering changes by type."""
        changes = [
            FollowerChange(self.test_profile, "user1", ChangeType.GAINED, profile_id=self.test_profile_id),
            FollowerChange(self.test_profile, "user2", ChangeType.LOST, profile_id=self.test_profile_id),
            FollowerChange(self.test_profile, "follow1", ChangeType.STARTED_FOLLOWING, profile_id=self.test_profile_id),
            FollowerChange(self.test_profile, "follow2", ChangeType.STOPPED_FOLLOWING, profile_id=self.test_profile_id),
        ]
        
        # Filter for only follower gains
        gained_only = self.detector.filter_changes_by_type(changes, [ChangeType.GAINED])
        assert len(gained_only) == 1
        assert gained_only[0].change_type == ChangeType.GAINED
        
        # Filter for follower changes
        follower_changes = self.detector.filter_changes_by_type(
            changes, [ChangeType.GAINED, ChangeType.LOST]
        )
        assert len(follower_changes) == 2
        
        # Filter for following changes
        following_changes = self.detector.filter_changes_by_type(
            changes, [ChangeType.STARTED_FOLLOWING, ChangeType.STOPPED_FOLLOWING]
        )
        assert len(following_changes) == 2
    
    def test_group_changes_by_profile(self):
        """Test grouping changes by profile."""
        changes = [
            FollowerChange("profile1", "user1", ChangeType.GAINED, profile_id=1),
            FollowerChange("profile1", "user2", ChangeType.LOST, profile_id=1),
            FollowerChange("profile2", "user3", ChangeType.GAINED, profile_id=2),
            FollowerChange("profile2", "user4", ChangeType.STARTED_FOLLOWING, profile_id=2),
        ]
        
        grouped = self.detector.group_changes_by_profile(changes)
        
        assert len(grouped) == 2
        assert len(grouped["profile1"]) == 2
        assert len(grouped["profile2"]) == 2
        
        # Verify correct grouping
        profile1_changes = grouped["profile1"]
        profile1_users = {c.affected_username for c in profile1_changes}
        assert profile1_users == {"user1", "user2"}
        
        profile2_changes = grouped["profile2"]
        profile2_users = {c.affected_username for c in profile2_changes}
        assert profile2_users == {"user3", "user4"}
    
    def test_detection_statistics(self):
        """Test detection statistics tracking."""
        # Reset stats first
        self.detector.reset_statistics()
        
        initial_stats = self.detector.get_detection_statistics()
        assert initial_stats['total_detections'] == 0
        assert initial_stats['changes_detected'] == 0
        
        # Perform some detections
        current_followers = {"user1", "user2"}
        previous_followers = {"user1"}
        current_following = {"follow1"}
        previous_following = {"follow1", "follow2"}
        
        changes = self.detector.detect_changes(
            self.test_profile, self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        stats = self.detector.get_detection_statistics()
        assert stats['total_detections'] == 1
        assert stats['changes_detected'] == 2  # 1 follower gain + 1 following loss
        assert stats['follower_changes'] == 1
        assert stats['following_changes'] == 1
        assert stats['average_changes_per_detection'] == 2.0
        assert stats['last_detection_time'] is not None
    
    def test_username_cleaning(self):
        """Test that usernames are properly cleaned during detection."""
        # Test with usernames that need cleaning
        current_followers = {"@user1", "USER2", "user3"}
        previous_followers = {"@user1", "user4"}
        current_following = set()
        previous_following = set()
        
        changes = self.detector.detect_changes(
            "@TestProfile", self.test_profile_id,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect user2 and user3 as gained, user4 as lost
        # @user1 and USER2 should be cleaned and compared properly
        assert len(changes) == 3
        
        gained = [c for c in changes if c.change_type == ChangeType.GAINED]
        lost = [c for c in changes if c.change_type == ChangeType.LOST]
        
        assert len(gained) == 2
        gained_usernames = {c.affected_username for c in gained}
        assert gained_usernames == {"user2", "user3"}  # Should be cleaned to lowercase
        assert len(lost) == 1
        assert lost[0].affected_username == "user4"
        
        # Profile username should also be cleaned
        for change in changes:
            assert change.profile_username == "testprofile"


class TestChangeDetectorEdgeCases:
    """Test edge cases and error conditions for ChangeDetector."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.detector = ChangeDetector()
    
    def test_detect_changes_with_none_profile_id(self):
        """Test detection with None profile_id."""
        changes = self.detector.detect_changes(
            "testuser", None,
            {"user1"}, set(),
            {"follow1"}, set()
        )
        
        assert len(changes) == 2
        for change in changes:
            assert change.profile_id is None
    
    def test_detect_changes_with_large_sets(self):
        """Test detection with large follower/following sets."""
        # Create large sets
        current_followers = {f"user{i}" for i in range(1000)}  # 0-999
        previous_followers = {f"user{i}" for i in range(500, 1500)}  # 500-1499, overlap 500-999
        current_following = {f"follow{i}" for i in range(100)}  # 0-99
        previous_following = {f"follow{i}" for i in range(50, 150)}  # 50-149, overlap 50-99
        
        changes = self.detector.detect_changes(
            "testuser", 1,
            current_followers, previous_followers,
            current_following, previous_following
        )
        
        # Should detect:
        # - 500 new followers (0-499)
        # - 500 lost followers (1000-1499) 
        # - 50 new following (0-49)
        # - 50 stopped following (100-149)
        assert len(changes) == 1100  # 500 + 500 + 50 + 50
        
        follower_changes = [c for c in changes if c.is_follower_change]
        following_changes = [c for c in changes if c.is_following_change]
        
        assert len(follower_changes) == 1000  # 500 gained + 500 lost
        assert len(following_changes) == 100  # 50 started + 50 stopped
    
    def test_detect_changes_with_empty_sets(self):
        """Test detection with empty sets."""
        changes = self.detector.detect_changes(
            "testuser", 1,
            set(), set(),
            set(), set()
        )
        
        assert len(changes) == 0
    
    def test_compare_lists_with_invalid_usernames(self):
        """Test list comparison with invalid usernames."""
        current = {"user1", "", "invalid@user", "user2"}
        previous = {"user1", "", "user3"}
        
        comparison = self.detector.compare_lists(current, previous)
        
        # Invalid usernames should be filtered out
        assert "user1" in comparison['unchanged']
        assert "user2" in comparison['added']
        assert "user3" in comparison['removed']
        assert "" not in comparison['added']
        assert "" not in comparison['removed']
        assert "" not in comparison['unchanged']